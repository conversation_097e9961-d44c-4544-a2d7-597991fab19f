.sidebar {
    width: 280px;
    height: 100%;
    max-height: 100%;
    background: white;
    border-right: 1px solid #e2e8f0;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    overflow: hidden;
    /* 防止整体出现滚动条 */
    transition: all 0.3s ease;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
}

.new-chat-btn {
    width: 100%;
    padding: 12px;
    background: #1a7aff;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.new-chat-btn:hover {
    background: #0056d6;
}

.plus-icon {
    font-size: 18px;
    font-weight: 300;
}

.tabs-container {
    padding: 12px 20px;
    border-bottom: 1px solid #e2e8f0;
}

.tabs {
    display: flex;
    gap: 8px;
}

.tab-btn {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    color: #64748b;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    color: #1a7aff;
    background: #f1f5f9;
}

.tab-btn.active {
    color: #1a7aff;
    background: #e0f2fe;
    font-weight: 500;
}

.conversation-list {
    flex: 1;
    overflow-y: auto;
    padding: 12px 0;
    max-height: calc(100vh - 140px);
    /* 减去头部和标签栏的高度 */
}

.conversation-item {
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
    position: relative;
}

.conversation-item:hover {
    background: #f8fafc;
}

.conversation-item.active {
    background: #f1f5f9;
    border-left-color: #1a7aff;
}

.conversation-item.active::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 2px;
    background: #1a7aff;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.conversation-item.active::before {
    opacity: 1;
}

.conversation-item.active .conversation-title {
    color: #1a7aff;
}

.conversation-item.active .conversation-message {
    color: #334155;
}

.conversation-content {
    flex: 1;
    min-width: 0;
}

.conversation-title {
    margin: 0 0 4px 0;
    font-size: 14px;
    /* font-weight: 500; */
    color: #334155;
}

.conversation-message {
    margin: 0;
    font-size: 13px;
    color: #64748b;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.conversation-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    margin-left: 12px;
}

.conversation-time {
    font-size: 12px;
    color: #94a3b8;
}

.unread-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #1a7aff;
}

/* 自定义滚动条 */
.conversation-list::-webkit-scrollbar {
    width: 8px;
}

.conversation-list::-webkit-scrollbar-track {
    background: transparent;
}

.conversation-list::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.conversation-list::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 深色主题样式 */
.dark-theme .sidebar {
    background: #000 !important;
    border-right-color: #2d2d2d !important;
}

.dark-theme .sidebar-header {
    border-bottom-color: #2d2d2d !important;
}

.dark-theme .tabs-container {
    border-bottom-color: #2d2d2d !important;
}

.dark-theme .conversation-item:hover {
    background: #1a1a1a !important;
}

.dark-theme .conversation-item.active {
    background: #1a1a1a !important;
}

.dark-theme .conversation-title {
    color: #fff !important;
}

.dark-theme .conversation-message {
    color: #888 !important;
}

.dark-theme .conversation-time {
    color: #666 !important;
}

.dark-theme .tab-btn {
    color: #888 !important;
}

.dark-theme .tab-btn:hover {
    background: #1a1a1a !important;
    color: #1a7aff !important;
}

.dark-theme .tab-btn.active {
    background: #1a1a1a !important;
    color: #1a7aff !important;
}

.loading-state {
    padding: 20px;
    text-align: center;
    color: #94a3b8;
    font-size: 14px;
}

.dark-theme .loading-state {
    color: #64748b;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #94a3b8;
    text-align: center;
}

.empty-state svg {
    margin-bottom: 16px;
    color: #cbd5e1;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

.dark-theme .empty-state {
    color: #64748b;
}

.dark-theme .empty-state svg {
    color: #475569;
}

.rename-input {
    width: 100%;
    padding: 4px 8px;
    border: 1px solid #1a7aff;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    color: #333;
    outline: none;
    margin: -4px 0;
}

.rename-input:focus {
    box-shadow: 0 0 0 2px rgba(26, 122, 255, 0.1);
}

@media (max-width: 750px) {
    .idebar-header {
        display: none !important;
    }

    .conversation-list {
        padding: 8px 0;
        max-height: calc(100vh - 120px);
    }

    .conversation-item {
        padding: 10px 14px;
        font-size: 15px;
        gap: 4px;
    }

    .conversation-title {
        font-size: 15px;
        margin-bottom: 2px;
    }

    .conversation-message {
        font-size: 13px;
    }
}