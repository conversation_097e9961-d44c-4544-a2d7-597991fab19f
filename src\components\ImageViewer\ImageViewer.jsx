import React, { useEffect, useState } from 'react';
import './ImageViewer.css';

const ImageViewer = ({ src, alt, onClose }) => {
    const [scale, setScale] = useState(1);
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

    // 处理按键事件
    useEffect(() => {
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                onClose();
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [onClose]);

    // 处理滚轮缩放
    const handleWheel = (e) => {
        e.preventDefault();
        const delta = e.deltaY * -0.01;
        const newScale = Math.min(Math.max(0.1, scale + delta), 10);
        setScale(newScale);
    };

    // 处理拖动开始
    const handleMouseDown = (e) => {
        e.preventDefault();
        setIsDragging(true);
        setDragStart({
            x: e.clientX - position.x,
            y: e.clientY - position.y
        });
    };

    // 处理拖动中
    const handleMouseMove = (e) => {
        if (isDragging) {
            setPosition({
                x: e.clientX - dragStart.x,
                y: e.clientY - dragStart.y
            });
        }
    };

    // 处理拖动结束
    const handleMouseUp = () => {
        setIsDragging(false);
    };

    // 重置缩放和位置
    const handleDoubleClick = () => {
        setScale(1);
        setPosition({ x: 0, y: 0 });
    };

    // 处理背景点击
    const handleBackgroundClick = (e) => {
        if (e.target === e.currentTarget) {
            onClose();
        }
    };

    return (
        <div 
            className="image-viewer-overlay"
            onClick={handleBackgroundClick}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
        >
            <div className="image-viewer-controls">
                <button onClick={() => setScale(scale => Math.min(scale + 0.1, 10))}>
                    <svg viewBox="0 0 24 24" width="24" height="24">
                        <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                    </svg>
                </button>
                <button onClick={() => setScale(scale => Math.max(scale - 0.1, 0.1))}>
                    <svg viewBox="0 0 24 24" width="24" height="24">
                        <path fill="currentColor" d="M19 13H5v-2h14v2z"/>
                    </svg>
                </button>
                <button onClick={() => { setScale(1); setPosition({ x: 0, y: 0 }); }}>
                    <svg viewBox="0 0 24 24" width="24" height="24">
                        <path fill="currentColor" d="M9 3L5 7h3v7c0 .55.45 1 1 1s1-.45 1-1V7h3L9 3zm6 14v-7c0-.55-.45-1-1-1s-1 .45-1 1v7h-3l4 4 4-4h-3z"/>
                    </svg>
                </button>
                <button onClick={onClose}>
                    <svg viewBox="0 0 24 24" width="24" height="24">
                        <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/>
                    </svg>
                </button>
            </div>
            <img
                src={src}
                alt={alt}
                className="image-viewer-image"
                style={{
                    transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
                    cursor: isDragging ? 'grabbing' : 'grab'
                }}
                onMouseDown={handleMouseDown}
                onWheel={handleWheel}
                onDoubleClick={handleDoubleClick}
                draggable="false"
            />
        </div>
    );
};

export default ImageViewer; 