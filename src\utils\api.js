// src/utils/api.js
import axios from 'axios';
import store from '../store/store'; // 导入 Redux store
import { refreshToken } from './auth';

// 公共配置变量
const BASE_URL = 'http://123.56.141.236:8010';

// 创建 axios 实例
const api = axios.create({
    baseURL: BASE_URL,
    timeout: 10000 * 120,
});

// 获取当前认证状态
const getAuthState = () => {
    return store.getState().auth;
};

// 请求拦截器 - 添加认证头
api.interceptors.request.use(
    (config) => {
        const { accessToken } = getAuthState();
        if (accessToken) {
            config.headers.Authorization = `Bearer ${accessToken}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// 响应拦截器 - 处理 token 刷新
api.interceptors.response.use(
    (response) => {
        return response;
    },
    async (error) => {
        const originalRequest = error.config;

        // 检查是否是 token 过期错误 (401)
        if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            try {
                // 尝试刷新 token
                const newToken = await refreshToken();

                // 更新请求头
                originalRequest.headers.Authorization = `Bearer ${newToken}`;

                // 重试原始请求
                return api(originalRequest);
            } catch (refreshError) {
                // 刷新失败 - 跳转到登录
                const { logout } = store.getState().auth;
                if (logout) logout(); // 调用 Redux 登出操作
                window.location.href = '/login';
                return Promise.reject(refreshError);
            }
        }

        // 其他错误处理
        return Promise.reject(error);
    }
);

export default api;