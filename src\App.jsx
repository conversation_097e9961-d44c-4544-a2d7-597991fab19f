// import React from 'react';
// import './App.css';
// import ChatComponent from './ChatComponent';

// function App() {
//   return (
//     <div className="App">
//       <ChatComponent />
//     </div>
//   );
// }

// export default App;

// App.js
import React from 'react';
import { HashRouter, Routes, Route, Navigate } from 'react-router-dom';
import Home from './pages/Home';
import Login from './pages/Login';
import Chat from './pages/Chat';
import SvgDemo from './pages/SvgDemo';
import MarkdownDemo from './pages/MarkdownDemo';
import CombinedDemo from './pages/CombinedDemo';
import PrivateRoute from './routes/PrivateRoute';

import './App.css';

function App() {
    return (
        <HashRouter>
            <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/login" element={<Login />} />
                <Route path="/chat" element={
                    <PrivateRoute>
                        <Chat />
                    </PrivateRoute>
                } />
                <Route path="/svg-demo" element={<SvgDemo />} />
                <Route path="*" element={<Navigate to="/" />} />
            </Routes>
        </HashRouter>
    );
}

export default App;