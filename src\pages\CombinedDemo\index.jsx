import React, { useState } from 'react';
import SvgRenderer from '../../components/SvgRenderer';
import MarkdownRenderer from '../../components/MarkdownRenderer';
import './CombinedDemo.css';

const CombinedDemo = () => {
    const [content, setContent] = useState(`# 综合渲染演示

这是一个综合演示页面，展示了 **SVG** 和 **Markdown** 的混合渲染效果。

## 房屋平面图

下面是一个房屋平面图的SVG示例：

<svg width="512" height="512" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <g id="layer-bottom">
    <!-- 客厅 -->
    <path d="M 80,60 L 200,60 L 200,140 L 80,140 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="140" y="105" font-size="12" text-anchor="middle" fill="black">客厅</text>
    
    <!-- 餐厅 -->
    <path d="M 200,60 L 280,60 L 280,140 L 200,140 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="240" y="105" font-size="12" text-anchor="middle" fill="black">餐厅</text>
    
    <!-- 厨房 -->
    <path d="M 280,60 L 360,60 L 360,140 L 280,140 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="320" y="105" font-size="12" text-anchor="middle" fill="black">厨房</text>
    
    <!-- 主卧 -->
    <path d="M 80,140 L 200,140 L 200,220 L 80,220 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="140" y="185" font-size="12" text-anchor="middle" fill="black">主卧</text>
    
    <!-- 主卧卫生间 -->
    <path d="M 200,140 L 250,140 L 250,180 L 200,180 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="225" y="165" font-size="10" text-anchor="middle" fill="black">主卫</text>
    
    <!-- 次卧 -->
    <path d="M 200,180 L 280,180 L 280,220 L 200,220 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="240" y="205" font-size="12" text-anchor="middle" fill="black">次卧</text>
    
    <!-- 儿童房 -->
    <path d="M 280,140 L 360,140 L 360,220 L 280,220 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="320" y="185" font-size="12" text-anchor="middle" fill="black">儿童房</text>
    
    <!-- 客卫 -->
    <path d="M 80,220 L 130,220 L 130,260 L 80,260 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="105" y="245" font-size="10" text-anchor="middle" fill="black">客卫</text>
    
    <!-- 走廊 -->
    <path d="M 130,220 L 280,220 L 280,260 L 130,260 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="205" y="245" font-size="12" text-anchor="middle" fill="black">走廊</text>
    
    <!-- 储物间 -->
    <path d="M 280,220 L 360,220 L 360,260 L 280,260 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="320" y="245" font-size="10" text-anchor="middle" fill="black">储物间</text>
    
    <!-- 阳台 -->
    <path d="M 80,40 L 200,40 L 200,60 L 80,60 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="140" y="55" font-size="10" text-anchor="middle" fill="black">阳台</text>
  </g>
</svg>

## 房间说明

### 主要功能区域

1. **客厅** - 家庭活动中心
2. **餐厅** - 用餐区域
3. **厨房** - 烹饪空间

### 卧室区域

- **主卧** - 主人卧室
- **次卧** - 次卧室
- **儿童房** - 儿童卧室

### 辅助设施

| 设施 | 位置 | 功能 |
|------|------|------|
| 主卫 | 主卧旁 | 主人专用 |
| 客卫 | 走廊端 | 公共使用 |
| 储物间 | 走廊端 | 物品存储 |
| 阳台 | 客厅外 | 晾晒休闲 |

## 设计特点

> 这个户型设计注重功能性和实用性，各个区域布局合理，动线流畅。

### 代码示例

\`\`\`javascript
// 房屋面积计算
const calculateArea = (rooms) => {
    return rooms.reduce((total, room) => total + room.area, 0);
};

const rooms = [
    { name: '客厅', area: 24 },
    { name: '餐厅', area: 12 },
    { name: '厨房', area: 8 },
    { name: '主卧', area: 16 },
    { name: '次卧', area: 12 },
    { name: '儿童房', area: 10 }
];

const totalArea = calculateArea(rooms);
console.log(\`总面积: \${totalArea} 平方米\`);
\`\`\`

---

*这个演示展示了如何在聊天应用中混合渲染 SVG 图表和 Markdown 文本内容。*`);

    const [isDarkMode, setIsDarkMode] = useState(false);

    const toggleTheme = () => {
        setIsDarkMode(!isDarkMode);
    };

    // 分离SVG和Markdown内容
    const separateContent = (text) => {
        if (!text) return [];
        
        const parts = [];
        let currentIndex = 0;
        
        // 处理SVG内容
        const svgRegex = /<svg[\s\S]*?<\/svg>/g;
        let svgMatch;
        
        while ((svgMatch = svgRegex.exec(text)) !== null) {
            // 添加SVG之前的文本
            if (svgMatch.index > currentIndex) {
                const beforeSvg = text.slice(currentIndex, svgMatch.index);
                if (beforeSvg.trim()) {
                    parts.push({
                        type: 'markdown',
                        content: beforeSvg
                    });
                }
            }
            // 添加SVG内容
            parts.push({
                type: 'svg',
                content: svgMatch[0]
            });
            currentIndex = svgMatch.index + svgMatch[0].length;
        }
        
        // 添加剩余的文本
        if (currentIndex < text.length) {
            const remainingText = text.slice(currentIndex);
            if (remainingText.trim()) {
                parts.push({
                    type: 'markdown',
                    content: remainingText
                });
            }
        }
        
        return parts;
    };

    const contentParts = separateContent(content);

    return (
        <div className={`combined-demo ${isDarkMode ? 'dark-theme' : 'light-theme'}`}>
            <div className="demo-header">
                <h1>综合渲染演示</h1>
                <button onClick={toggleTheme} className="theme-toggle">
                    {isDarkMode ? '🌞' : '🌙'}
                </button>
            </div>
            
            <div className="demo-content">
                <div className="demo-section">
                    <h2>渲染效果</h2>
                    <div className="render-area">
                        {contentParts.map((part, index) => {
                            if (part.type === 'svg') {
                                return (
                                    <SvgRenderer 
                                        key={index}
                                        svgContent={part.content}
                                        className={isDarkMode ? 'dark-theme' : ''}
                                    />
                                );
                            } else {
                                return (
                                    <MarkdownRenderer
                                        key={index}
                                        content={part.content}
                                        className={isDarkMode ? 'dark-theme' : ''}
                                    />
                                );
                            }
                        })}
                    </div>
                </div>
                
                <div className="demo-section">
                    <h2>混合内容代码</h2>
                    <textarea
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        className="content-code"
                        rows="40"
                        placeholder="输入包含SVG和Markdown的混合内容..."
                    />
                </div>
            </div>
        </div>
    );
};

export default CombinedDemo; 