import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { loginWithPassword, loginWithCode, sendVerificationCode } from '../../utils/auth';
import './Login.css';

const Login = () => {
    const navigate = useNavigate();

    // 表单状态
    const [phone, setPhone] = useState('18813082663');
    const [code, setCode] = useState('');
    const [password, setPassword] = useState('');
    const [method, setMethod] = useState('code'); // 'code' 或 'password'
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const [isCodeSent, setIsCodeSent] = useState(false);
    const [isDarkMode, setIsDarkMode] = useState(() => {
        // 从 localStorage 获取主题设置，默认为 false（浅色主题）
        const savedTheme = localStorage.getItem('theme');
        return savedTheme === 'dark';
    });
    // detail
    // 验证码倒计时
    const [countdown, setCountdown] = useState(0);

    // 切换主题
    const toggleTheme = () => {
        const newTheme = !isDarkMode;
        setIsDarkMode(newTheme);
        localStorage.setItem('theme', newTheme ? 'dark' : 'light');
    };

    // 处理表单提交
    const handleSubmit = async (e) => {
        // console.log(method)
        // await loginWithCode(phone, code);
        e.preventDefault();
        setIsLoading(true);
        setError('');

        try {
            if (!phone) {
                throw new Error('请输入手机号');
            }

            if (method === 'code') {
                if (!code) throw new Error('请输入验证码');
                await loginWithCode(phone, code);
            } else {
                if (!password) throw new Error('请输入密码');
                await loginWithPassword(phone, password);
            }

            navigate('/chat');
        } catch (err) {
            setError(err.message || '登录失败，请稍后重试');
            console.error('登录错误:', err);
        } finally {
            setIsLoading(false);
        }
    };

    // 发送验证码
    const handleSendCode = async () => {
        if (!phone) {
            setError('请输入手机号');
            setIsCodeSent(false);
            return;
        }

        if (countdown > 0) return;

        try {
            setIsLoading(true);
            setError('');
            await sendVerificationCode(phone);

            // 启动倒计时
            setCountdown(60);
            setError('验证码已发送，请查收');
            setIsCodeSent(true);
        } catch (err) {
            setError(err.message || '发送验证码失败');
            setIsCodeSent(false);
            console.error('验证码发送错误:', err);
        } finally {
            setIsLoading(false);
        }
    };

    // 倒计时效果
    useEffect(() => {
        let timer;
        if (countdown > 0) {
            timer = setTimeout(() => setCountdown(countdown - 1), 1000);
        }
        return () => clearTimeout(timer);
    }, [countdown]);

    // 检查是否已登录
    useEffect(() => {
        // 修复：添加条件判断防止无限循环
        const token = localStorage.getItem('access_token');
        if (token) {
            navigate('/chat');
        }
    }, [navigate]);

    return (
        <div className={`page-container ${isDarkMode ? 'dark-theme' : 'light-theme'}`}>
            <div className="login-container">
                <button className="theme-toggle" onClick={toggleTheme}>
                    {isDarkMode ? (
                        <svg className="theme-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 17C14.7614 17 17 14.7614 17 12C17 9.23858 14.7614 7 12 7C9.23858 7 7 9.23858 7 12C7 14.7614 9.23858 17 12 17Z" stroke="currentColor" strokeWidth="2" />
                            <path d="M12 1V3M12 21V23M1 12H3M21 12H23M4.22 4.22L5.64 5.64M18.36 18.36L19.78 19.78M18.36 5.64L19.78 4.22M4.22 19.78L5.64 18.36" stroke="currentColor" strokeWidth="2" />
                        </svg>
                    ) : (
                        <svg className="theme-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" stroke="currentColor" strokeWidth="2" />
                        </svg>
                    )}
                </button>

                <div className="login-header">
                    <h1>博笛智家</h1>
                    <p>开启AI建房新时代</p>
                </div>

                <form onSubmit={handleSubmit} className="login-form">
                    <h2>用户登录</h2>

                    <div className="input-group">
                        <input
                            type="tel"
                            placeholder="请输入手机号"
                            value={phone}
                            onChange={(e) => setPhone(e.target.value)}
                            disabled={isLoading}
                            required
                        />
                    </div>

                    {method === 'code' ? (
                        <div className="input-group code-input">
                            <input
                                type="text"
                                placeholder="验证码"
                                value={code}
                                onChange={(e) => setCode(e.target.value)}
                                disabled={isLoading}
                                maxLength={6}
                                required
                            />
                            <button
                                type="button"
                                className={`send-code ${countdown > 0 ? 'disabled' : ''}`}
                                onClick={handleSendCode}
                                disabled={isLoading || countdown > 0}
                            >
                                {countdown > 0 ? `${countdown}秒后重发` : '获取验证码'}
                            </button>
                        </div>
                    ) : (
                        <div className="input-group">
                            <input
                                type="password"
                                placeholder="密码"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                disabled={isLoading}
                                required
                            />
                        </div>
                    )}

                    <div className="switch-method">
                        <button type="button" onClick={() => setMethod(method === 'code' ? 'password' : 'code')} disabled={isLoading} >
                            {method === 'code' ? '使用密码登录' : '使用验证码登录'}
                        </button>
                    </div>

                    {error && (
                        <div className={`error-message ${isCodeSent ? 'success' : ''}`}>
                            {error}
                        </div>
                    )}

                    <button type="submit" className="login-button" disabled={isLoading} >
                        {isLoading ? (<span className="loading-spinner"></span>) : ('登录')}
                    </button>
                </form>

                <div className="login-footer">
                    <p>© 2023 博笛智家AI智能助手 - 让沟通更智能</p>
                    <p>首次登录将自动创建账号</p>
                </div>
            </div>
        </div>
    );
};

export default Login;