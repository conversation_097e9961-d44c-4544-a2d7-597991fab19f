*{
    margin: 0;
    padding: 0;
}
/* .App {
    text-align: center;
    padding: 20px;
  }
  
  .chat-container {
    max-width: 800px;
    margin: 0 auto;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
  }
  
  .messages {
    height: 60vh;
    padding: 20px;
    background: #f9f9f9;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .message {
    max-width: 70%;
    padding: 12px 18px;
    border-radius: 15px;
    word-break: break-word;
    animation: fadeIn 0.3s ease-in;
  }
  
  .message.user {
    background: #007bff;
    color: white;
    align-self: flex-end;
  }
  
  .message.ai {
    background: #ffffff;
    border: 1px solid #eee;
    align-self: flex-start;
  }
  
  .loading {
    padding: 10px;
    color: #666;
    font-style: italic;
    text-align: center;
  }
  
  form {
    display: flex;
    padding: 20px;
    background: white;
    border-top: 1px solid #eee;
  }
  
  input {
    flex: 1;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 25px;
    margin-right: 10px;
    font-size: 16px;
  }
  
  button {
    padding: 12px 25px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: background 0.3s;
  }
  
  button:hover {
    background: #0056b3;
  }
  
  button:disabled {
    background: #a0a0a0;
    cursor: not-allowed;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  .streaming-message::after {
    content: '|';
    animation: cursor-blink 1s infinite;
    color: #666;
  }
  
  @keyframes cursor-blink {
    0% { opacity: 0; }
    50% { opacity: 1; }
    100% { opacity: 0; }
  } */

