import React from 'react';
import './SvgRenderer.css';

const SvgRenderer = ({ svgContent, className = '' }) => {
    // 检查内容是否为SVG
    const isSvg = svgContent && (
        svgContent.includes('<svg') || 
        svgContent.includes('viewBox') ||
        svgContent.trim().startsWith('<svg')
    );

    if (!isSvg) {
        return null;
    }

    // 下载SVG函数
    const handleDownload = () => {
        // 创建Blob对象
        const blob = new Blob([svgContent], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        // 创建临时a标签
        const a = document.createElement('a');
        a.href = url;
        a.download = 'image.svg';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    // 创建一个安全的SVG渲染函数
    const createMarkup = () => {
        return { __html: svgContent };
    };

    return (
        <div className={`svg-renderer ${className}`}>
            <div className="svg-toolbar">
                <button className="svg-download-btn" onClick={handleDownload} title="下载SVG图">
                    <span className="svg-download-icon" aria-hidden="true">
                        <svg viewBox="0 0 20 20" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M10 3v10m0 0l-4-4m4 4l4-4" strokeLinecap="round" strokeLinejoin="round"/>
                            <rect x="4" y="15" width="12" height="2" rx="1" fill="currentColor" />
                        </svg>
                    </span>
                    下载图纸
                </button>
            </div>
            <div className="svg-container" dangerouslySetInnerHTML={createMarkup()} />
        </div>
    );
};

export default SvgRenderer; 