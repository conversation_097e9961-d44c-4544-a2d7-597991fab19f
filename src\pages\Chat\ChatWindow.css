input{
    width: 95% !important;
}
.chat-containers {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-height: 100vh;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    overflow: hidden; /* 防止整体出现滚动条 */
    position: relative;
    transition: all 0.3s ease;
}

.messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;
    border-radius: 12px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    max-height: calc(100vh - 140px); /* 减去其他元素的高度 */
}

.message {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 12px;
    font-size: 15px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
    position: relative;
}

.message.user {
    align-self: flex-end;
    background: #1a7aff;
    color: white;
    border-bottom-right-radius: 4px;
    box-shadow: 0 2px 4px rgba(26, 122, 255, 0.1);
}

.message.ai {
    align-self: flex-start;
    background: white;
    color: #334155;
    border: 1px solid #e2e8f0;
    border-bottom-left-radius: 4px;
}

.streaming-cursor {
    display: none;
}

.message.ai .streaming-cursor {
    background-color: #334155;
}

.chat-containers form {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    -webkit-border-radius: 12px;
    -moz-border-radius: 12px;
    -ms-border-radius: 12px;
    -o-border-radius: 12px;
}

.chat-containers form .input-row{
    display: flex;
    width: 100%;
    gap: 12px;
    padding: 16px;
    border-radius: 12px;
}

.chat-containers input {
    /* flex: 1; */
    padding: 12px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 15px;
    background: white;
    color: #334155;
    transition: all 0.2s ease;
}

.chat-containers input:focus {
    outline: none;
    border-color: #1a7aff;
    box-shadow: 0 0 0 3px rgba(26, 122, 255, 0.1);
}
.chat-containers form .status-row {
    display: flex;
    width: 100%;
    justify-content: center;
    gap: 12px;
    margin-bottom: 8px;
    align-items: center;
  }

  .chat-containers form .input-row {
    display: flex;
    width: 100%;
    gap: 8px;
  }

  .chat-containers input {
    /* flex: 1; */
    margin: 0;
    padding: 12px 16px;
    height: 44px;
    border: 1px solid #e2e8f0;
    border-radius: 22px;
    font-size: 15px;
    background: #f8fafc;
    color: #334155;
  }

  .chat-containers button.send-button {
    width: 44px;
    height: 44px;
    min-width: 44px;
    padding: 0;
    border-radius: 22px;
    background: #1a7aff;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    color: white;
  }

  .chat-containers .generating-text {
    color: #1a7aff;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .chat-containers .abort-button {
    background: #fee2e2;
    color: #ef4444;
    border: none;
    padding: 6px 16px;
    border-radius: 16px;
    font-size: 14px;
  }
.chat-containers input:disabled {
    background: #f1f5f9;
    cursor: not-allowed;
}

.chat-containers button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.send-button {
    background: #1a7aff;
    color: white;
    padding: 12px 24px;
    position: relative;
}

.send-button svg {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
    width: 20px;
    height: 20px;
}

.send-button:hover:not(:disabled) {
    background: #0056d6;
}

.send-button:disabled {
    background: #94a3b8;
    cursor: not-allowed;
}

.abort-button {
    background: #fee2e2;
    color: #ef4444;
    font-weight: 500;
}

.abort-button:hover {
    background: #fecaca;
}

/* 自定义滚动条样式 */
.messages::-webkit-scrollbar {
    width: 8px;
}

.messages::-webkit-scrollbar-track {
    background: transparent;
}

.messages::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.messages::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0; }
    100% { opacity: 1; }
}

/* 加载动画 */
.loading-spinner {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1a7aff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Think标签样式 */
.thinking-text {
    color: #888;
    font-size: 0.9em;
    font-style: italic;
    padding: 4px 8px;
    border-radius: 4px;
    margin: 0 4px;
    display: inline-block;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
}

.normal-text {
    color: #334155;
    font-size: 1em;
    display: inline;
}

/* 主题切换按钮 */
.theme-toggle-button {
    position: fixed;
    top: 20px;
    width: 40px;
    height: 40px;
    z-index: 999;
    border-radius: 50%;
    background: transparent;
    border: 2px solid currentColor;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1672cf;
    transition: all 0.3s ease;
    z-index: 1000;
}

.dark-theme .theme-toggle-button {
    color: #fff;
}

.theme-icon {
    width: 20px;
    height: 20px;
    position: absolute;
    top: 0px;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
}

/* 深色主题样式 */
.chat-containers.dark-theme {
    background: #000 !important;
}

.dark-theme .messages {
    background: #2d2d2d !important;
    border-color: #3d3d3d !important;
}

.dark-theme .message.ai {
    background: #383838 !important;
    border-color: #4a4a4a !important;
    color: #fff !important;
}

.dark-theme .normal-text {
    color: #fff !important;
}

.dark-theme .thinking-text {
    color: #aaa !important;
}

.chat-containers.dark-theme form {
    background: #000 !important;
    border-color: #2d2d2d !important;
}

.chat-containers.dark-theme input {
    background: #2d2d2d !important;
    border-color: #2d2d2d !important;
    color: #fff !important;
}

.dark-theme .chat-containers input::placeholder {
    color: #888;
}

.dark-theme .chat-containers input:focus {
    border-color: #1a7aff;
    box-shadow: 0 0 0 3px rgba(26, 122, 255, 0.2);
}

.dark-theme .theme-toggle-button:hover {
    background: rgba(255, 255, 255, 0.1);
}

.dark-theme .streaming-cursor {
    background-color: #fff;
}

.loading-animation {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.loading-history {
    padding: 20px;
    text-align: center;
    color: #94a3b8;
    font-size: 14px;
}

.dark-theme .loading-history {
    color: #64748b;
}

.empty-chat {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #94a3b8;
    font-size: 14px;
}

.dark-theme .empty-chat {
    color: #64748b;
}

.loading-dots {
    display: inline-flex;
    align-items: center;
    margin-left: 4px;
}

.loading-dots .dot {
    width: 4px;
    height: 4px;
    margin: 0 2px;
    background-color: #64748b; /* 浅色模式下的默认颜色 */
    border-radius: 50%;
    animation: dot-flashing 1s infinite linear alternate;
}

/* 深色模式下的样式 */
.dark-theme .loading-dots .dot {
    background-color: #ffffff; /* 深色模式下使用白色 */
}

.loading-dots .dot:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dots .dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes dot-flashing {
    0% {
        opacity: 0.2;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* SVG渲染相关样式 */
.message.ai .svg-renderer {
    margin: 8px 0;
    width: 100%;
    display: flex;
    justify-content: center;
}

.message.ai .svg-container {
    width: 600px;
    max-width: 100%;
    border-radius: 8px;
    overflow: hidden;
    /* background: #f8f9fa; */
    /* border: 1px solid #e9ecef; */
    /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
    /* transition: all 0.3s ease; */
}

/* .message.ai .svg-container:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
} */

.message.ai .svg-container svg {
    display: block;
    max-width: 100%;
    height: auto;
    background: white;
}

/* 深色主题下的SVG样式 */
.dark-theme .message.ai .svg-container {
    background: #fff;
    border-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}
.messages-button{
    width: auto !important;
    height: auto !important;
}
/* .dark-theme .message.ai .svg-container:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
} */

/* .dark-theme .message.ai .svg-container svg {
    background: #fff;
} */

/* 响应式设计 */
@media (max-width: 768px) {
    .message.ai .svg-container {
        max-width: 90%;
    }
}

@media (max-width: 480px) {
    .message.ai .svg-container {
        max-width: 95%;
    }
}

/* Markdown渲染相关样式 */
.message.ai .markdown-renderer {
    width: 100%;
    margin: 8px 0;
}

.message.ai .markdown-renderer h1,
.message.ai .markdown-renderer h2,
.message.ai .markdown-renderer h3,
.message.ai .markdown-renderer h4,
.message.ai .markdown-renderer h5,
.message.ai .markdown-renderer h6 {
    color: inherit;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.message.ai .markdown-renderer p {
    margin: 0.5rem 0;
    line-height: 1.5;
}

.message.ai .markdown-renderer ul,
.message.ai .markdown-renderer ol {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.message.ai .markdown-renderer li {
    margin: 0.2rem 0;
}

.message.ai .markdown-renderer code {
    background: #f1f3f4;
    color: #d73a49;
    padding: 0.1rem 0.3rem;
    border-radius: 3px;
    font-size: 0.9em;
}

.message.ai .markdown-renderer pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem;
    margin: 0.5rem 0;
    overflow-x: auto;
}

.message.ai .markdown-renderer blockquote {
    border-left: 3px solid #1a7aff;
    padding-left: 1rem;
    margin: 0.5rem 0;
    color: #6b7280;
    font-style: italic;
}

.message.ai .markdown-renderer table {
    width: 100%;
    border-collapse: collapse;
    margin: 0.5rem 0;
    font-size: 0.9em;
}

.message.ai .markdown-renderer th,
.message.ai .markdown-renderer td {
    border: 1px solid #e2e8f0;
    padding: 0.5rem;
    text-align: left;
}

.message.ai .markdown-renderer th {
    background: #f8fafc;
    font-weight: 600;
}

/* 深色主题下的Markdown样式 */
.dark-theme .message.ai .markdown-renderer code {
    background: #2d3748;
    color: #fbb6ce;
}

.dark-theme .message.ai .markdown-renderer pre {
    background: #1a202c;
    border-color: #4a5568;
}

.dark-theme .message.ai .markdown-renderer blockquote {
    color: #a0aec0;
    border-left-color: #1a7aff;
}

.dark-theme .message.ai .markdown-renderer th {
    background: #2d3748;
    color: #e2e8f0;
}

.dark-theme .message.ai .markdown-renderer th,
.dark-theme .message.ai .markdown-renderer td {
    border-color: #4a5568;
}

@media (max-width: 750px) {
  .chat-containers {
    padding: 0;
    height: 100%;
    background: #f8fafc;
  }

  .messages {
    flex: 1;
    margin: 0;
    padding: 12px;
    padding-bottom: calc(140px + env(safe-area-inset-bottom)); /* 增加底部间距，确保最后一条消息完全可见 */
    border: none;
    border-radius: 0;
    background: #f8fafc;
    max-height: none;
  }
  
  .message {
    max-width: 88%;
    padding: 12px 16px;
    font-size: 15px;
  }

  .chat-containers form {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0;
    /* padding: 12px 16px; */
    padding: 10rpx 0 0 0 !important;
    padding-bottom: calc(12px + env(safe-area-inset-bottom)); /* 适配底部安全区域 */
    background: #fff;
    border: none;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0;
    gap: 8px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
    flex-direction: column;
  }

  .chat-containers form .status-row {
    display: flex;
    width: 100%;
    justify-content: center;
    gap: 12px;
    margin-bottom: 0px;
    align-items: center;
  }

  .chat-containers form .input-row {
    display: flex;
    width: 100%;
    padding: 0 !important;
    gap: 8px;
  }

  .chat-containers input {
    width: 90% !important;
    margin: 0;
    padding: 12px 16px;
    height: 44px;
    border: 1px solid #e2e8f0;
    border-radius: 22px;
    font-size: 15px;
    background: #f8fafc;
    color: #334155;
  }

  .chat-containers button.send-button {
    width: 44px;
    height: 44px;
    min-width: 44px;
    padding: 0;
    border-radius: 22px;
    background: #1a7aff;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    color: white;
  }

  .chat-containers .generating-text {
    color: #1a7aff;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .chat-containers .abort-button {
    background: #fee2e2;
    color: #ef4444;
    border: none;
    padding: 6px 16px;
    border-radius: 16px;
    font-size: 14px;
  }

  /* 深色模式适配 */
  .dark-theme .chat-containers form {
    background: #18181c;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .dark-theme .chat-containers .generating-text {
    color: #3b82f6;
  }

  .dark-theme .chat-containers .abort-button {
    background: rgba(239, 68, 68, 0.2);
  }
}

.message-image {
    width: 100%; /* 填满容器宽度 */
    max-height: 300px; /* 最大高度限制 */
    border-radius: 8px;
    object-fit: contain; /* 改为contain以完整显示图片 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    cursor: zoom-in;  /* 添加放大镜光标 */
    display: block; /* 确保图片是块级元素 */
    background-color: #f8f9fa; /* 添加背景色以便看清图片边界 */
}

.message-image:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);  /* 增强悬停时的阴影效果 */
}

/* 深色模式下的图片样式 */
.dark-theme .message-image {
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

.dark-theme .message-image:hover {
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.15);
}

/* 图片容器的包装器 */
.images-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 20px; /* 增加图片之间的间距 */
    margin: 16px 0;
    justify-content: flex-start; /* 左对齐 */
}

/* 图片容器样式优化 */
.message-image-container {
    flex: 0 0 calc(50% - 10px); /* 每个容器占50%减去间距的一半 */
    max-width: calc(50% - 10px);
    display: block; /* 改为block，因为不再需要flex布局 */
    position: relative;  /* 为可能的加载状态提供定位上下文 */
    padding: 8px; /* 添加内边距 */
    border: 1px solid #e5e7eb; /* 添加边框 */
    border-radius: 12px; /* 圆角 */
    background-color: #ffffff; /* 背景色 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 轻微阴影 */
}

/* 图片加载失败时的样式 */
.message-image[style*="display: none"] + .image-error {
    color: #ff4d4f;
    font-size: 14px;
    margin: 8px 0;
    text-align: center;
    padding: 8px;
    background: rgba(255, 77, 79, 0.1);
    border-radius: 4px;
}

/* 确保图片在消息气泡中正确显示 */
.message.ai .message-image-container,
.message.user .message-image-container {
    margin: 8px -9px;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .message-image {
        max-height: 250px; /* 移动端进一步减小高度 */
    }

    .message-image:hover {
        transform: none;  /* 移动端禁用悬停效果 */
    }

    .message-image-container {
        flex: 0 0 100%; /* 移动端单列显示 */
        max-width: 100%;
        margin-bottom: 16px; /* 移动端图片容器之间的外边距 */
    }

    .images-wrapper {
        gap: 16px; /* 移动端间距 */
        flex-direction: column; /* 移动端垂直排列 */
    }

    .buttons-wrapper {
        flex-direction: column; /* 移动端按钮垂直排列 */
        gap: 8px;
    }

    .independent-button {
        width: 100%; /* 移动端按钮全宽 */
        justify-content: center;
    }
}

/* 添加图片加载动画 */
@keyframes imageLoading {
    0% { opacity: 0.6; }
    50% { opacity: 0.8; }
    100% { opacity: 0.6; }
}

.message-image:not([src]) {
    min-height: 200px;
    background: #f0f0f0;
    animation: imageLoading 1.5s infinite;
}

.dark-theme .message-image:not([src]) {
    background: #2a2a2a;
}

/* 优化图片预览的过渡效果 */
.image-viewer-overlay {
    opacity: 0;
    animation: fadeIn 0.2s ease-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 添加图片加载中的占位效果 */
.message-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #f0f0f0;
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.2s;
    pointer-events: none;
}

.message-image-container.loading::before {
    opacity: 1;
}

.dark-theme .message-image-container::before {
    background: #2a2a2a;
}

.svg-container {
    position: relative;
    margin: 10px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.download-svg-button {
    margin-top: 10px;
    padding: 8px 16px;
    background-color: #10b981;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.download-svg-button:hover {
    background-color: #059669;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(16, 185, 129, 0.3);
}

.download-svg-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.download-svg-button svg {
    width: 20px;
    height: 20px;
}

/* 深色模式适配 */
.dark-theme .download-svg-button {
    background-color: #059669;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.4);
}

.dark-theme .download-svg-button:hover {
    background-color: #047857;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .download-svg-button {
        padding: 6px 12px;
        font-size: 13px;
    }

    .download-svg-button svg {
        width: 18px;
        height: 18px;
    }
}

/* 下载中状态 */
.download-svg-button.downloading {
    opacity: 0.7;
    cursor: not-allowed;
    background-color: #6b7280;
}

/* 独立按钮区域 */
.buttons-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 16px;
    justify-content: flex-start;
}

/* 独立的下载按钮样式 */
.independent-button {
    background-color: #10b981;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.independent-button:hover {
    background-color: #059669;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.independent-button:active {
    transform: translateY(0);
}

.independent-button svg {
    width: 18px;
    height: 18px;
}

/* 确保SVG容器内的图像正确显示 */
.svg-container img,
.svg-container svg {
    max-width: 100%;
    height: auto;
}

/* SVG链接容器样式 */
.svg-link-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #f0f9ff;
    border: 2px dashed #0ea5e9;
    border-radius: 12px;
    margin: 8px 0;
}

.svg-link-message {
    font-size: 14px;
    color: #0369a1;
    font-weight: 500;
    text-align: center;
}

/* 深色主题下的SVG链接容器 */
.dark-theme .svg-link-container {
    background: #1e293b;
    border-color: #0ea5e9;
}

.dark-theme .svg-link-message {
    color: #38bdf8;
}

/* 深色主题下的图片容器 */
.dark-theme .message-image-container {
    border-color: #374151;
    background-color: #1f2937;
}

.dark-theme .message-image {
    background-color: #374151;
}

/* 深色主题下的独立按钮 */
.dark-theme .independent-button {
    background-color: #059669;
    box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);
}

.dark-theme .independent-button:hover {
    background-color: #047857;
    box-shadow: 0 4px 8px rgba(5, 150, 105, 0.3);
}


