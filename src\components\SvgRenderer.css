.svg-renderer {
    margin: 10px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.svg-container {
    max-width: 100%;
    border-radius: 8px;
    overflow: hidden;
    /* background: #f8f9fa; */
    /* border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease; */
}

/* .svg-container:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
} */

.svg-container svg {
    display: block;
    max-width: 100%;
    height: auto;
    background: white;
}

/* 深色主题样式 */
.dark-theme .svg-container {
    background: rgba(0, 0, 0, 0.3);
    border-color: #4a5568;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* .dark-theme .svg-container:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
} */

.dark-theme .svg-container svg {
    background: #1a202c;
}

/* 响应式设计 */
/* @media (max-width: 768px) {
    .svg-container {
        max-width: 90%;
    }
}

@media (max-width: 480px) {
    .svg-container {
        max-width: 95%;
    }
} */

.svg-toolbar {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
    display: flex;
    justify-content: flex-end;
    pointer-events: none;
}

.svg-download-btn {
    background: linear-gradient(90deg, #1a7aff 60%, #3b82f6 100%);
    color: #fff;
    border: none;
    border-radius: 20px;
    padding: 6px 18px 6px 32px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(26, 122, 255, 0.08);
    transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
    margin-right: 0;
    position: relative;
    pointer-events: auto;
    display: flex;
    align-items: center;
    gap: 6px;
}

.svg-download-btn:hover {
    background: linear-gradient(90deg, #0056d6 60%, #2563eb 100%);
    box-shadow: 0 4px 16px rgba(26, 122, 255, 0.15);
    transform: translateY(-2px) scale(1.04);
}

.svg-download-btn .svg-download-icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    display: inline-block;
    vertical-align: middle;
}

.dark-theme .svg-download-btn {
    background: linear-gradient(90deg, #2563eb 60%, #60a5fa 100%);
    color: #fff;
}

.dark-theme .svg-download-btn:hover {
    background: linear-gradient(90deg, #1e40af 60%, #2563eb 100%);
} 