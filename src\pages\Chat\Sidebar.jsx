import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import './Sidebar.css';

const MOBILE_BREAKPOINT = 750;

const Sidebar = ({ isDarkMode, onConversationSelect, fetchTrigger }) => {
    const [activeTab, setActiveTab] = useState('recent');
    const [selectedConversation, setSelectedConversation] = useState(null);
    const [conversations, setConversations] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [editingId, setEditingId] = useState(null);
    const [editingName, setEditingName] = useState('');
    const userId = useSelector(state => state.auth.userId);
    const [isMobile, setIsMobile] = useState(window.innerWidth <= MOBILE_BREAKPOINT);

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth <= MOBILE_BREAKPOINT);
        };
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // 获取会话列表
    // curl -X POST 'http://192.168.110.36/ \
    // --header 'Authorization: Bearer {api_key}' \
    // --header 'Content-Type: application/json' \
    // --data-raw '{ 
    //  "name": "", 
    //  "auto_generate": true, 
    //  "user": "abc-123"
    // }'

    

    // 公共配置变量
    const API_KEY = 'app-2XnCWvdqFxRWhUP69QSsjQbZ';
    const BASE_URL = 'http://ai-model.kaikungroup.com:8081';
    const AUTHORIZATION_HEADER = 'Bearer ' + API_KEY;

    const fetchConversations = async () => {
        setIsLoading(true);
        try {
            const response = await fetch(`${BASE_URL}/v1/conversations?user=${userId}&limit=100`, {
                method: 'GET',
                headers: {
                    'Authorization': AUTHORIZATION_HEADER,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('获取会话列表失败');
            }

            const data = await response.json();
            const conversationList = data.data || [];
            setConversations(conversationList);

            // 判断今天的对话
            const isToday = (timestamp) => {
                if (!timestamp) return false;
                const timestampMs = typeof timestamp === 'string' ? Date.parse(timestamp) : Number(timestamp) * 1000;
                const date = new Date(timestampMs);
                const now = new Date();
                return date.toDateString() === now.toDateString();
            };
            const todayConvs = conversationList.filter(conv => isToday(conv.updated_at));

            if (todayConvs.length === 0) {
                // 今天没有对话，自动创建
                handleNewChat();
            } else {
                // 有对话，默认选中第一个今天的
                setSelectedConversation(todayConvs[0].id);
                onConversationSelect(todayConvs[0].id);
            }
        } catch (error) {
            console.error('获取会话列表错误:', error);
            // 如果获取失败，也创建一个新的会话
            handleNewChat();
        } finally {
            setIsLoading(false);
        }
    };

    // 监听 fetchTrigger 变化
    useEffect(() => {
        if (userId) {
            fetchConversations();
        }
    }, [userId, fetchTrigger]);

    const filteredConversations = () => {
        if (isMobile) {
            return conversations;
        }
        // 判断是否是今天的对话
        const isToday = (timestamp) => {
            if (!timestamp) return false;
            const timestampMs = typeof timestamp === 'string' ? Date.parse(timestamp) : Number(timestamp) * 1000;
            const date = new Date(timestampMs);
            const now = new Date();
            return date.toDateString() === now.toDateString();
        };
        switch (activeTab) {
            case 'saved':
                return conversations.filter(conv => conv.saved);
            case 'all':
                return conversations;
            default: // recent
                return conversations.filter(conv => isToday(conv.updated_at));
        }
    };

    const handleTabChange = (newTab) => {
        const filteredConvs = conversations.filter(conv => {
            switch (newTab) {
                case 'saved':
                    return conv.saved;
                case 'all':
                    return true;
                default: // recent
                    return conversations.indexOf(conv) < 5;
            }
        });

        if (!filteredConvs.some(conv => conv.id === selectedConversation)) {
            setSelectedConversation(null);
        }

        setActiveTab(newTab);
    };

    const handleConversationClick = (id) => {
        setSelectedConversation(id);
        setConversations(prev => prev.map(conv => 
            conv.id === id ? { ...conv, unread: false } : conv
        ));
        // 调用父组件传递的回调函数
        onConversationSelect(id);
    };

    // 创建新对话
    const handleNewChat = async () => {
        // 检查是否存在临时对话（ID以'new-'开头的对话）
        const hasTemporaryChat = conversations.some(conv => conv.id.toString().startsWith('new-'));

        // 如果存在临时对话，先刷新列表
        if (hasTemporaryChat) {
            await fetchConversations();
        }

        const newConversation = {
            id: 'new-' + Date.now(), // 临时ID
            name: '新对话',
            introduction: '您好，有什么需要帮助您呢',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            isNew: true // 标记为新对话
        };

        // 将新对话添加到列表开头
        setConversations(prev => [newConversation, ...prev]);
        setSelectedConversation(newConversation.id);
        onConversationSelect(newConversation.id, true); // 传递isNew标记
    };

    // 格式化时间
    const formatTime = (timestamp) => {
        if (!timestamp) return '';
        
        // 将时间戳转换为毫秒
        const timestampMs = typeof timestamp === 'string' ? Date.parse(timestamp) : Number(timestamp) * 1000;
        const date = new Date(timestampMs);
        
        if (isNaN(date.getTime())) {
            return '';
        }

        const now = new Date();
        const diff = now - date;
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        
        // 今天
        if (days === 0) {
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            return `${hours}:${minutes}`;
        } 
        // 昨天
        else if (days === 1) {
            return '昨天';
        } 
        // 一周内
        else if (days < 7) {
            const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
            return weekdays[date.getDay()];
        } 
        // 更早
        else {
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `${month}/${day}`;
        }
    };

    // 空状态组件
    const EmptyState = () => (
        <div className="empty-state">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2v10z" />
            </svg>
            <p>{activeTab === 'recent' ? '今天还没有新对话' : '暂无对话记录'}</p>
        </div>
    );

    // 处理双击重命名
    const handleDoubleClick = (conv) => {
        if (conv.id.toString().startsWith('new-')) return; // 不允许重命名新对话
        setEditingId(conv.id);
        setEditingName(conv.name || '新对话');
    };

    // 处理重命名提交
    const handleRename = async (e) => {
        e.preventDefault();
        if (!editingId || !editingName.trim()) return;

        try {
            const response = await fetch(`${BASE_URL}/v1/conversations/${editingId}/name`, {
                method: 'POST',
                headers: {
                    'Authorization': AUTHORIZATION_HEADER,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: editingName.trim(),
                    user: userId
                })
            });

            if (!response.ok) {
                throw new Error('重命名失败');
            }

            // 重命名成功后刷新对话列表
            await fetchConversations();
        } catch (error) {
            console.error('重命名错误:', error);
        } finally {
            setEditingId(null);
            setEditingName('');
        }
    };

    // 处理重命名取消
    const handleRenameCancel = () => {
        setEditingId(null);
        setEditingName('');
    };

    // 处理重命名输入框按键事件
    const handleRenameKeyDown = (e) => {
        if (e.key === 'Enter') {
            handleRename(e);
        } else if (e.key === 'Escape') {
            handleRenameCancel();
        }
    };

    return (
        <div className={`sidebar ${isDarkMode ? 'dark-theme' : ''}`}>
            <div className="sidebar-header">
                <button className="new-chat-btn" onClick={handleNewChat}>
                    <span className="plus-icon">+</span> 新对话
                </button>
            </div>

            {/* PC端显示tab，移动端不显示tab */}
            {!isMobile && (
                <div className="tabs-container">
                    <div className="tabs">
                        <button
                            className={`tab-btn ${activeTab === 'recent' ? 'active' : ''}`}
                            onClick={() => setActiveTab('recent')}
                        >
                            今天
                        </button>
                        <button
                            className={`tab-btn ${activeTab === 'all' ? 'active' : ''}`}
                            onClick={() => setActiveTab('all')}
                        >
                            全部
                        </button>
                        <button
                            className={`tab-btn ${activeTab === 'saved' ? 'active' : ''}`}
                            onClick={() => setActiveTab('saved')}
                        >
                            已保存
                        </button>
                    </div>
                </div>
            )}

            <div className="conversation-list">
                {isLoading ? (
                    <div className="loading-state">加载中...</div>
                ) : filteredConversations().length === 0 ? (
                    <EmptyState />
                ) : (
                    filteredConversations().map(conv => (
                        <div
                            key={conv.id}
                            className={`conversation-item ${selectedConversation === conv.id ? 'active' : ''}`}
                            onClick={() => handleConversationClick(conv.id)}
                        >
                            <div className="conversation-content">
                                {editingId === conv.id ? (
                                    <input
                                        type="text"
                                        className="rename-input"
                                        value={editingName}
                                        onChange={(e) => setEditingName(e.target.value)}
                                        onBlur={handleRename}
                                        onKeyDown={handleRenameKeyDown}
                                        autoFocus
                                        onClick={(e) => e.stopPropagation()}
                                    />
                                ) : (
                                    <>
                                        <h4 
                                            className="conversation-title"
                                            onDoubleClick={() => handleDoubleClick(conv)}
                                        >
                                            {conv.name || '新对话'}
                                        </h4>
                                        <span className="conversation-time" style={{fontSize: '12px', color: '#94a3b8', display: 'block', marginTop: 2}}>
                                            {formatTime(conv.updated_at)}
                                        </span>
                                    </>
                                )}
                            </div>
                            <div className="conversation-meta">
                                {conv.unread && <span className="unread-dot"></span>}
                            </div>
                        </div>
                    ))
                )}
            </div>
        </div>
    );
};

export default Sidebar;