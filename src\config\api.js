// 公共API配置文件
export const API_CONFIG = {
    // API密钥
    API_KEY: 'app-2XnCWvdqFxRWhUP69QSsjQbZ',
    
    // 基础URL
    BASE_URL: 'http://ai-model.kaikungroup.com:8081',
    
    // 完整的API端点
    get CHAT_API_URL() {
        return `${this.BASE_URL}/v1/chat-messages`;
    },
    
    get HISTORY_API_URL() {
        return `${this.BASE_URL}/v1/messages`;
    },
    
    get CONVERSATIONS_API_URL() {
        return `${this.BASE_URL}/v1/conversations`;
    },
    
    // Authorization头
    get AUTHORIZATION_HEADER() {
        return 'Bearer ' + this.API_KEY;
    },
    
    // 请求配置
    TIMEOUT: 10000 * 120,
    MAX_RETRIES: 3,
    RETRY_DELAY: 2000,
    STREAM_TIMEOUT: 50000 * 100,
    NO_DATA_TIMEOUT: 3000
};

// 导出常用的配置
export const { 
    API_KEY, 
    BASE_URL, 
    AUTHORIZATION_HEADER 
} = API_CONFIG;

export default API_CONFIG;
