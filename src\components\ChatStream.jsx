import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import './ChatWindow.css';

const ChatWindow = () => {
    const [input, setInput] = useState('');
    const [messages, setMessages] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [controller, setController] = useState(null);
    const userId = useSelector(state => state.auth.userId);
    const messagesEndRef = useRef(null);

    // 公共配置变量
    const API_KEY = 'app-2XnCWvdqFxRWhUP69QSsjQbZ';
    const BASE_URL = 'http://ai-model.kaikungroup.com:8081';
    const API_URL = import.meta.env.VITE_DIFY_API_URL || `${BASE_URL}/v1/chat-messages`;
    const AUTHORIZATION_HEADER = 'Bearer ' + API_KEY;
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 2000;
    const STREAM_TIMEOUT = 30000;
    console.log(API_KEY)
    // 自动滚动到底部
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    // 更新消息内容
    const updateLastMessage = (newText) => {
        setMessages(prev => {
            const last = prev[prev.length - 1];
            if (last && !last.isUser) {
                return [
                    ...prev.slice(0, -1),
                    { ...last, text: last.text + newText }
                ];
            }
            return prev;
        });
    };

    // 替换最后一条消息
    const replaceLastMessage = (newText) => {
        setMessages(prev => {
            const last = prev[prev.length - 1];
            if (last && !last.isUser) {
                return [
                    ...prev.slice(0, -1),
                    { ...last, text: newText }
                ];
            }
            return prev;
        });
    };

    // 处理流式响应
    const processStream = async (reader) => {
        const decoder = new TextDecoder();
        let buffer = '';
        let isFirstChunk = true;
        let timeoutId;
        let hasContent = false; // 标记是否有内容

        try {
            // 设置超时
            timeoutId = setTimeout(() => {
                console.error('流超时');
                reader.cancel();
                updateLastMessage('\n\n[响应超时，请重试]');
            }, STREAM_TIMEOUT);

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                // 重置超时计时器
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    reader.cancel();
                    updateLastMessage('\n\n[响应超时，请重试]');
                }, STREAM_TIMEOUT);

                buffer += decoder.decode(value, { stream: true });

                // 处理可能的多行数据
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (!line.trim()) continue; // 跳过空行
                    if (line === ':heartbeat') continue;

                    try {
                        const jsonStr = line.startsWith('data: ') ? line.substring(6) : line;
                        const data = JSON.parse(jsonStr);
                        if (data.answer) {
                            hasContent = true;
                            if (isFirstChunk) {
                                isFirstChunk = false;
                                replaceLastMessage(data.answer);
                            } else {
                                updateLastMessage(data.answer);
                            }
                        }
                    } catch (e) {
                        console.warn('解析JSON失败:', line);
                    }
                }
            }

            // 处理剩余的 buffer
            if (buffer.trim()) {
                try {
                    const jsonStr = buffer.startsWith('data: ') ? buffer.substring(6) : buffer;
                    const data = JSON.parse(jsonStr);
                    if (data.answer) {
                        hasContent = true;
                        if (isFirstChunk) {
                            isFirstChunk = false;
                            replaceLastMessage(data.answer);
                        } else {
                            updateLastMessage(data.answer);
                        }
                    }
                } catch (e) {
                    // 可选：console.warn('解析JSON失败:', buffer);
                }
            }

            // 结束后检查是否有内容
            if (!hasContent) {
                replaceLastMessage('当前服务器繁忙，请稍后再试');
            }
        } catch (error) {
            console.error('流读取错误:', error);
            updateLastMessage('\n\n[响应中断，请重试]');
            throw error;
        } finally {
            clearTimeout(timeoutId);
            try {
                await reader.cancel();
            } catch (e) {
                console.warn('关闭流时出错:', e);
            }
        }
    };

    // 提交处理
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!input.trim() || isLoading) return;

        // 添加用户消息
        setMessages(prev => [
            ...prev,
            { text: input, isUser: true, timestamp: Date.now() },
            { text: '', isUser: false, timestamp: Date.now() + 1 } // 占位AI消息
        ]);

        const userInput = input;
        setInput('');
        setIsLoading(true);

        try {
            let retryCount = 0;
            let success = false;
            
            while (!success && retryCount < MAX_RETRIES) {
                try {
                    const newController = new AbortController();
                    setController(newController);
                    
                    const response = await fetch(API_URL, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${API_KEY}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            inputs: {},
                            query: userInput,
                            response_mode: 'streaming',
                            user: userId
                        }),
                        signal: newController.signal
                    });

                    if (!response.ok) throw new Error(response.statusText);
                    if (!response.body) throw new Error('无可读流');

                    const reader = response.body.getReader();
                    await processStream(reader);
                    
                    success = true; // 请求成功，退出循环
                } catch (error) {
                    retryCount++;
                    console.warn(`请求失败 (尝试 ${retryCount}/${MAX_RETRIES}):`, error);
                    
                    if (retryCount < MAX_RETRIES && error.name !== 'AbortError') {
                        // 显示重试提示
                        updateLastMessage(`\n\n[网络连接不稳定，尝试重新连接 (${retryCount}/${MAX_RETRIES})...]`);
                        
                        // 等待一段时间后重试
                        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
                    } else {
                        throw error; // 重试次数用尽或用户取消
                    }
                }
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('请求失败:', error);
                updateLastMessage('\n\n[请求出错，请稍后重试]');
            }
        } finally {
            setIsLoading(false);
            setController(null);
        }
    };
    const handleAbort = () => {
        if (controller) {
            controller.abort();
            setController(null);
            setIsLoading(false);
        }
    };

    return (
        <div className="chat-containers">
            <div className="messages">
                {messages.map((msg, index) => (
                    <div
                        key={`${msg.timestamp}-${index}`}
                        className={`message ${msg.isUser ? 'user' : 'ai'}`}
                    >
                        {msg.text}
                        {/* 流式响应光标 */}
                        {index === messages.length - 1 && !msg.isUser && isLoading && (
                            <span className="streaming-cursor">|</span>
                        )}
                    </div>
                ))}
                <div ref={messagesEndRef} />
            </div>
            
            <form onSubmit={handleSubmit}>
                <input
                    type="text"
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    placeholder="输入消息..."
                    disabled={isLoading}
                    autoFocus
                />
                <button
                    type="submit"
                    disabled={isLoading}
                    className="send-button"
                >
                    {isLoading ? '生成中...' : '发送'}
                </button>
                {isLoading && (
                    <button
                        type="button"
                        onClick={handleAbort}
                        className="abort-button"
                    >
                        停止
                    </button>
                )}
            </form>
        </div>
    );
};

export default ChatWindow;