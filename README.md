# 博笛智家 - AI智能助手

一个支持SVG图表和Markdown渲染的智能聊天应用。

## 功能特性

### 🎨 多格式内容渲染
- **SVG图表渲染**: 支持在聊天中显示SVG图表和图形
- **Markdown渲染**: 支持完整的Markdown语法渲染
- **混合内容**: 支持SVG和Markdown内容的混合显示
- **深色主题**: 支持浅色和深色主题切换

### 💬 智能聊天
- 流式响应显示
- 实时打字效果
- 消息历史记录
- 会话管理

### 🎯 专业功能
- 用户认证系统
- 响应式设计
- 现代化UI界面

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm start
```

### 构建生产版本
```bash
npm run build
```

## 演示页面

### SVG渲染演示
访问 `/svg-demo` 查看SVG渲染功能演示

### Markdown渲染演示
访问 `/markdown-demo` 查看Markdown渲染功能演示

### 综合渲染演示
访问 `/combined-demo` 查看SVG和Markdown混合渲染演示

## 技术栈

- **前端框架**: React 18
- **路由**: React Router
- **状态管理**: Redux Toolkit
- **样式**: CSS3 + 响应式设计
- **Markdown渲染**: react-markdown + remark-gfm
- **构建工具**: Vite

## 项目结构

```
src/
├── components/
│   ├── SvgRenderer.jsx      # SVG渲染组件
│   ├── MarkdownRenderer.jsx # Markdown渲染组件
│   └── ...
├── pages/
│   ├── Chat/                # 聊天页面
│   ├── SvgDemo/             # SVG演示页面
│   ├── MarkdownDemo/        # Markdown演示页面
│   ├── CombinedDemo/        # 综合演示页面
│   └── ...
└── ...
```

## 使用说明

### SVG渲染
在聊天消息中包含SVG代码，系统会自动识别并渲染：

```html
<svg width="400" height="300" viewBox="0 0 400 300">
  <rect x="50" y="50" width="100" height="100" fill="blue"/>
</svg>
```

### Markdown渲染
在聊天消息中使用Markdown语法：

```markdown
# 标题
**粗体文本**
*斜体文本*
- 列表项
```

### 混合内容
可以同时使用SVG和Markdown：

```markdown
# 房屋平面图

这是一个房屋设计图：

<svg>...</svg>

## 说明
- 客厅：24平方米
- 卧室：16平方米
```

## 配置

### 环境变量
创建 `.env` 文件：

```env
VITE_DIFY_API_URL=http://your-api-url/v1/chat-messages
```

### API配置
在 `src/pages/Chat/ChatWindow.jsx` 中配置API密钥：

```javascript
const API_KEY = 'your-api-key';
```

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

MIT License
