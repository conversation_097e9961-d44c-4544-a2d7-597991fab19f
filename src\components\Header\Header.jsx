import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { logout } from '../../store/authSlice';
import logoImage from '../../assets/images/logo.png';
import { useNavigate } from 'react-router-dom';
import './Header.css';

const Header = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const userInfo = useSelector(state => state.auth.userInfo);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [isMobile, setIsMobile] = useState(window.innerWidth <= 750);
    
    // 使用 access_token 判断登录状态
    const isAuthenticated = !!localStorage.getItem('access_token');

    useEffect(() => {
        const handleResize = () => setIsMobile(window.innerWidth <= 750);
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const handleLogout = () => {
        // 清除 token
        localStorage.removeItem('access_token');
        dispatch(logout());
        navigate('/login');
        setIsMobileMenuOpen(false);
    };

    const handleConsult = () => {
        navigate('/login');
        setIsMobileMenuOpen(false);
    };

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

    return (
        <header className={`header ${isMobileMenuOpen ? 'menu-open' : ''}`}>
            <div className="header-left">
                <div className="logo">
                    <img src={logoImage} alt="Logo" className="logo-image" />
                    {/* <span className="logo-text">博笛智家</span> */}
                </div>
            </div>

            {isMobile && (
                <button className="mobile-menu-button" onClick={toggleMobileMenu}>
                    <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2">
                        {isMobileMenuOpen ? (
                            <path d="M6 18L18 6M6 6l12 12" />
                        ) : (
                            <>
                                <line x1="3" y1="12" x2="21" y2="12" />
                                <line x1="3" y1="6" x2="21" y2="6" />
                                <line x1="3" y1="18" x2="21" y2="18" />
                            </>
                        )}
                    </svg>
                </button>
            )}

            <div className={`header-right ${isMobileMenuOpen ? 'show' : ''}`}>
                {/* {isAuthenticated && (
                    <nav className="nav-section">
                        <ul className="nav-menu">
                            <li className="nav-item">
                                <a href="/dashboard" className="nav-link">控制台</a>
                            </li>
                            <li className="nav-item">
                                <a href="/settings" className="nav-link">设置</a>
                            </li>
                        </ul>
                    </nav>
                )} */}

                <div className="auth-section">
                    {isAuthenticated ? (
                        <div className="user-dropdown">
                            <button className="user-button">
                                <span className="user-avatar">
                                    {userInfo?.avatar ? (
                                        <img src={userInfo.avatar} alt="用户头像" />
                                    ) : (
                                        <span className="avatar-placeholder">
                                            {userInfo?.name?.[0] || 'U'}
                                        </span>
                                    )}
                                </span>
                                <span className="user-name">{userInfo?.name || '用户'}</span>
                            </button>
                            <div className="dropdown-menu">
                                <a href="/profile" className="dropdown-item">个人信息</a>
                                <a href="/profile" onClick={handleLogout} className="dropdown-item">退出登录</a>
                            </div>
                        </div>
                    ) : (
                        <a className="nav-link login-button" onClick={handleConsult}>登录</a>
                    )}
                </div>
            </div>
        </header>
    );
};

export default Header; 