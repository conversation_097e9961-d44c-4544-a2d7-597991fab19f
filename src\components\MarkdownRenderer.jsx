import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import SvgRenderer from './SvgRenderer';
import './MarkdownRenderer.css';

const MarkdownRenderer = ({ content, className = '' }) => {
    if (!content) return null;

    // 拆分内容，保留 <think> 标签
    const parts = [];
    let lastIndex = 0;
    const thinkPattern = /<think>([\s\S]*?)<\/think>/g;
    let match;
    while ((match = thinkPattern.exec(content)) !== null) {
        if (match.index > lastIndex) {
            parts.push({ type: 'normal', text: content.slice(lastIndex, match.index) });
        }
        parts.push({ type: 'think', text: match[1] });
        lastIndex = thinkPattern.lastIndex;
    }
    if (lastIndex < content.length) {
        parts.push({ type: 'normal', text: content.slice(lastIndex) });
    }

    // 公共的 markdown 渲染配置
    const markdownProps = {
        remarkPlugins: [remarkGfm],
        components: {
            code: ({ node, inline, className, children, ...props }) => {
                const codeContent = Array.isArray(children) ? children.join('') : (children || '');
                if (!codeContent.trim()) {
                    return null;
                }
                if (!inline && className && className.includes('language-svg')) {
                    return <SvgRenderer svgContent={codeContent} />;
                }
                return !inline ? (
                    <pre className="code-block">
                        <code className={className} {...props}>
                            {children}
                        </code>
                    </pre>
                ) : (
                    <code className="inline-code" {...props}>
                        {children}
                    </code>
                );
            },
            table: ({ children }) => (
                <div className="table-container">
                    <table className="markdown-table">{children}</table>
                </div>
            ),
            a: ({ href, children }) => (
                <a href={href} target="_blank" rel="noopener noreferrer" className="markdown-link">{children}</a>
            ),
            img: ({ src, alt }) => (
                <img src={src} alt={alt} className="markdown-image" />
            ),
            ul: ({ children }) => <ul className="markdown-list">{children}</ul>,
            ol: ({ children }) => <ol className="markdown-list">{children}</ol>,
            blockquote: ({ children }) => <blockquote className="markdown-blockquote">{children}</blockquote>,
            h1: ({ children }) => <h1 className="markdown-h1">{children}</h1>,
            h2: ({ children }) => <h2 className="markdown-h2">{children}</h2>,
            h3: ({ children }) => <h3 className="markdown-h3">{children}</h3>,
            h4: ({ children }) => <h4 className="markdown-h4">{children}</h4>,
            h5: ({ children }) => <h5 className="markdown-h5">{children}</h5>,
            h6: ({ children }) => <h6 className="markdown-h6">{children}</h6>,
        }
    };

    return (
        <div className={`markdown-renderer ${className}`}>
            {parts.map((part, idx) =>
                part.type === 'think' ? (
                    <div className="thinking-process" key={idx}>
                        <ReactMarkdown {...markdownProps}>{part.text}</ReactMarkdown>
                    </div>
                ) : (
                    <ReactMarkdown {...markdownProps} key={idx}>{part.text}</ReactMarkdown>
                )
            )}
        </div>
    );
};

export default MarkdownRenderer; 