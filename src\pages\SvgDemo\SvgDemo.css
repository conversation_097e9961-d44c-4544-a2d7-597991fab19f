.svg-demo {
    min-height: 100vh;
    padding: 20px;
    transition: all 0.3s ease;
}

.svg-demo.light-theme {
    background: #f8fafc;
    color: #334155;
}

.svg-demo.dark-theme {
    background: #1a202c;
    color: #e2e8f0;
}

.demo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark-theme .demo-header {
    background: #2d3748;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.demo-header h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 600;
}

.theme-toggle {
    padding: 10px 15px;
    border: none;
    border-radius: 8px;
    background: #1a7aff;
    color: white;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.2s ease;
}

.theme-toggle:hover {
    background: #0056d6;
}

.demo-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.demo-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark-theme .demo-section {
    background: #2d3748;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.demo-section h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 1.5rem;
    font-weight: 600;
}

.svg-code {
    width: 100%;
    min-height: 400px;
    padding: 15px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    background: #f8f9fa;
    color: #334155;
    resize: vertical;
}

.dark-theme .svg-code {
    background: #1a202c;
    border-color: #4a5568;
    color: #e2e8f0;
}

.svg-code:focus {
    outline: none;
    border-color: #1a7aff;
    box-shadow: 0 0 0 3px rgba(26, 122, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .demo-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .demo-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .demo-header h1 {
        font-size: 1.5rem;
    }
} 