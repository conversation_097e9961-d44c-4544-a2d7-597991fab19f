// src/features/chat/chatSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  messages: [
    { 
      id: 'welcome', 
      text: '你好！我是智能助手，请问有什么可以帮您的吗？', 
      isUser: false,
      timestamp: Date.now()
    }
  ]
};

export const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    addMessage: (state, action) => {
      state.messages.push(action.payload);
    },
    updateLastMessage: (state, action) => {
      const lastMessage = state.messages[state.messages.length - 1];
      if (lastMessage && !lastMessage.isUser) {
        lastMessage.text += action.payload;
      }
    },
    clearChat: (state) => {
      state.messages = initialState.messages;
    }
  }
});

export const { addMessage, updateLastMessage, clearChat } = chatSlice.actions;

export const selectMessages = state => state.chat.messages;

export default chatSlice.reducer;