// src/auth.js
import api from './api';
import store from '../store/store';
import { loginStart, loginSuccess, loginFailure, tokenRefreshed, logout } from '../store/authSlice';

// 发送验证码
export const sendVerificationCode = async (phone) => {
    try {
        const response = await api.post('/api/auth/send-verification-code', {
            target: phone,
            type: 'phone'
        });

        if (response.status !== 200) {
            throw new Error(response.data?.message || '发送验证码失败');
        }

        return true;
    } catch (error) {
        console.error('发送验证码错误:', error);
        throw new Error(error.response?.data?.message || '发送验证码失败，请稍后重试');
    }
};

// 处理登录成功的通用逻辑
const handleLoginSuccess = (response) => {
    const { access_token, refresh_token, session_id, user_id, expires_in = 120 } = response.data;

    store.dispatch(loginSuccess({
        access_token,
        refresh_token,
        session_id,
        user_id,
        expires_in
    }));
    return access_token;
};

// 使用验证码登录
export const loginWithCode = async (phone, code) => {
    try {
        store.dispatch(loginStart());

        const response = await api.post('/api/auth/auth-with-verification-code', {
            target: phone,
            code: code,
            type: 'phone',
            client_id: 'ai_design_web',
            client_secret: 'hbSmqUQ8qPrnJJnyS1Xk933PiS2dKHLP',
            device_id: 'asha',
            remember_me: true
        });

        return handleLoginSuccess(response);
    } catch (error) {
        const errorMsg = error.response?.data?.message || '登录失败，请检查验证码';
        store.dispatch(loginFailure(errorMsg));
        throw new Error(errorMsg);
    }
};

// 使用密码登录
export const loginWithPassword = async (phone, password) => {
    try {
        store.dispatch(loginStart());

        const response = await api.post('/api/auth/login', {
            username: phone,
            password: password,
            client_id: 'ai_design_web',
            client_secret: 'hbSmqUQ8qPrnJJnyS1Xk933PiS2dKHLP',
            device_id: 'asha',
            remember_me: false
        });

        return handleLoginSuccess(response);
    } catch (error) {
        const errorMsg = error.response?.data?.message || '登录失败，请检查手机号和密码';
        store.dispatch(loginFailure(errorMsg));
        throw new Error(errorMsg);
    }
};

// 刷新 token
export const refreshToken = async () => {
    try {
        const { refreshToken: refreshTokenValue } = store.getState().auth; // 从 Redux 获取

        if (!refreshTokenValue) throw new Error('No refresh token');

        const response = await api.post('/api/auth/refresh-token', {
            refresh_token: refreshTokenValue,
            client_id: 'ai_design_web',
            client_secret: 'hbSmqUQ8qPrnJJnyS1Xk933PiS2dKHLP'
        });

        const { access_token, expires_in } = response.data;

        // 更新 Redux
        store.dispatch(tokenRefreshed({
            access_token,
            expires_in
        }));

        return access_token;
    } catch (error) {
        console.error('Token refresh failed:', error);
        throw error;
    }
};

// 定时检查 token
export const initTokenCheck = () => {
    setInterval(() => {
        const { tokenExpiry } = store.getState().auth;
        if (tokenExpiry && Date.now() > tokenExpiry - 300000) {
            refreshToken().catch(console.error);
        }
    }, 300000);
};

// 登出
export const logoutUser = () => {
    store.dispatch(logout());
};