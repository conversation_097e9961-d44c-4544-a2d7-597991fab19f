.page-container {
    min-height: 100vh;
    width: 100%;
    transition: all 0.3s ease;
}

.page-container.light-theme {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #2c3e50;
}

.page-container.dark-theme {
    background: linear-gradient(135deg, #000 0%, #2c3e50 100%);
    color: #fff;
}

.login-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-height: 100vh;
    overflow: hidden;
    padding: 20px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: transparent;
    border: 2px solid currentColor;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1672cf;
    transition: all 0.3s ease;
    z-index: 1000;
}

.dark-theme .theme-toggle {
    color: #fff;
}

.theme-icon {
    width: 20px;
    height: 20px;
}

.login-header {
    text-align: center;
    margin: 20px 0;
    flex-shrink: 0;
}

.login-header h1 {
    font-size: 2.5rem;
    color: #1672cf;
    margin-bottom: 10px;
    letter-spacing: -0.5px;
}

.dark-theme .login-header h1 {
    color: transparent;
    background: linear-gradient(300deg, #00c6ff 0%, #0072ff 25%, #7b42ff 50%, #ff00e5 75%, #00c6ff 100%);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    background-clip: text;
    animation: gradientMove 12s ease infinite;
    text-shadow: 0 0 30px rgba(0, 198, 255, 0.2);
}

.login-header p {
    font-size: 1.2rem;
    color: #7f8c8d;
}

.dark-theme .login-header p {
    color: #fff;
}

.login-form {
    max-width: 450px;
    width: 100%;
    margin: auto;
    margin-top: 20px;
    padding: 30px;
    background: #ffffff;
    border: 1px solid #e1e5eb;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.dark-theme .login-form {
    background-color: rgba(23, 37, 84, 0.3);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.login-form h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #2c3e50;
}

.dark-theme .login-form h2 {
    color: #fff;
}

.input-group {
    margin-bottom: 20px;
}

input {
    width: 100%;
    padding: 15px;
    border: 1px solid #e1e5eb;
    border-radius: 10px;
    background: #fff;
    font-size: 16px;
    color: #2c3e50;
    transition: all 0.3s;
    box-sizing: border-box;
}

.dark-theme input {
    border: 1px solid rgba(59, 130, 246, 0.2);
    background: rgba(23, 37, 84, 0.3);
    color: #fff;
}

input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.dark-theme input:focus {
    border-color: #6d55d2;
    box-shadow: 0 0 0 3px rgba(109, 85, 210, 0.3);
}

.code-input {
    display: flex;
    gap: 10px;
}

.code-input input {
    flex: 1;
}

.send-code {
    padding: 15px 20px;
    background: #f0f7ff;
    color: #3498db;
    border: 1px solid #e1e5eb;
    border-radius: 10px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    white-space: nowrap;
}

.send-code:hover:not(.disabled) {
    background: #e1f0ff;
}

.send-code.disabled {
    background: #f5f5f5;
    color: #bdc3c7;
    cursor: not-allowed;
}

.switch-method {
    text-align: right;
    margin: 15px 0 25px;
}

.switch-method button {
    background: none;
    border: none;
    color: #3498db;
    cursor: pointer;
    font-size: 14px;
    padding: 5px;
}

.switch-method button:hover {
    text-decoration: underline;
}

.error-message {
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    text-align: center;
    background-color: #fef2f2;
    color: #ef4444;
    transition: all 0.3s ease;
}

.error-message.success {
    background-color: #D6fadb;
    color: #28b93d;
}

.login-container .login-button {
    width: 100%;
    padding: 16px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
}

.dark-theme .login-button {
    background: linear-gradient(to right, #06b6d4 0%, #3b82f6 100%);
}

.login-container .login-button:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

.login-container .login-button:not(:disabled):hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3);
    -webkit-transform: translateY(-2px);
    -moz-transform: translateY(-2px);
    -ms-transform: translateY(-2px);
    -o-transform: translateY(-2px);
}

.dark-theme .login-button:not(:disabled):hover {
    background: linear-gradient(to right, #3b82f6 0%, #06b6d4 100%);
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.login-footer {
    text-align: center;
    padding: 20px 0;
    color: #7f8c8d;
    font-size: 14px;
    flex-shrink: 0;
}

.dark-theme .login-footer {
    color: rgba(255, 255, 255, 0.7);
}

.login-footer p {
    margin: 5px 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-form {
        padding: 30px 20px;
    }

    .code-input {
        flex-direction: column;
    }

    .send-code {
        width: 100%;
    }
}

/* 添加浅色主题特定样式 */
.light-theme .login-header h1 {
    color: #1672cf;
}

.light-theme .login-header p {
    color: #7f8c8d;
}

.light-theme .login-form {
    background: #ffffff;
    border: 1px solid #e1e5eb;
}

.light-theme .login-form h2 {
    color: #2c3e50;
}

.light-theme input {
    background: #fff;
    border: 1px solid #e1e5eb;
    color: #2c3e50;
}

.light-theme input:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.light-theme .login-button {
    background: #3498db;
}

.light-theme .login-button:not(:disabled):hover {
    background: #2980b9;
}

.light-theme .login-footer {
    color: #7f8c8d;
}

/* 移动端样式 */
@media (max-width: 750px) {
    .login-container {
        padding: 0;
    }

    .theme-toggle {
        top: 10px;
        right: 10px;
        width: 36px;
        height: 36px;
    }

    .login-header {
        margin: 40px 0 20px;
    }

    .login-header h1 {
        font-size: 2rem;
    }

    .login-header p {
        font-size: 1rem;
    }

    .login-form {
        width: 90%;
        max-width: 90%;
        margin: 10px auto;
        padding: 20px;
        border-radius: 12px;
        box-sizing: border-box;
    }

    .login-form h2 {
        font-size: 1.5rem;
        margin-bottom: 20px;
    }

    input {
        padding: 12px;
        font-size: 15px;
    }

    .code-input {
        flex-direction: column;
        gap: 8px;
    }

    .send-code {
        width: 100%;
        padding: 12px;
    }

    .switch-method {
        margin: 12px 0 20px;
    }

    .login-button {
        padding: 14px;
        font-size: 15px;
    }

    .login-footer {
        padding: 15px 0;
        font-size: 12px;
    }
}

/* 平板端样式 */
@media (min-width: 751px) and (max-width: 1024px) {
    .login-form {
        max-width: 400px;
        margin: 20px auto;
    }

    .login-header h1 {
        font-size: 2.2rem;
    }
}