function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var t,n,r={exports:{}},o={};var i,a,l=(n||(n=1,r.exports=function(){if(t)return o;t=1;var e=Symbol.for("react.transitional.element"),n=Symbol.for("react.fragment");function r(t,n,r){var o=null;if(void 0!==r&&(o=""+r),void 0!==n.key&&(o=""+n.key),"key"in n)for(var i in r={},n)"key"!==i&&(r[i]=n[i]);else r=n;return n=r.ref,{$$typeof:e,type:t,key:o,ref:void 0!==n?n:null,props:r}}return o.Fragment=n,o.jsx=r,o.jsxs=r,o}()),r.exports),s={exports:{}},u={};function c(){if(i)return u;i=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),a=Symbol.for("react.consumer"),l=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),f=Symbol.for("react.memo"),d=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var k=b.prototype=new v;k.constructor=b,m(k,y.prototype),k.isPureReactComponent=!0;var w=Array.isArray,x={H:null,A:null,T:null,S:null,V:null},S=Object.prototype.hasOwnProperty;function E(t,n,r,o,i,a){return r=a.ref,{$$typeof:e,type:t,key:n,ref:void 0!==r?r:null,props:a}}function C(t){return"object"==typeof t&&null!==t&&t.$$typeof===e}var _=/\/+/g;function T(e,t){return"object"==typeof e&&null!==e&&null!=e.key?(n=""+e.key,r={"=":"=0",":":"=2"},"$"+n.replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var n,r}function P(){}function N(n,r,o,i,a){var l=typeof n;"undefined"!==l&&"boolean"!==l||(n=null);var s,u,c=!1;if(null===n)c=!0;else switch(l){case"bigint":case"string":case"number":c=!0;break;case"object":switch(n.$$typeof){case e:case t:c=!0;break;case d:return N((c=n._init)(n._payload),r,o,i,a)}}if(c)return a=a(n),c=""===i?"."+T(n,0):i,w(a)?(o="",null!=c&&(o=c.replace(_,"$&/")+"/"),N(a,r,o,"",(function(e){return e}))):null!=a&&(C(a)&&(s=a,u=o+(null==a.key||n&&n.key===a.key?"":(""+a.key).replace(_,"$&/")+"/")+c,a=E(s.type,u,void 0,0,0,s.props)),r.push(a)),1;c=0;var f,h=""===i?".":i+":";if(w(n))for(var m=0;m<n.length;m++)c+=N(i=n[m],r,o,l=h+T(i,m),a);else if("function"==typeof(m=null===(f=n)||"object"!=typeof f?null:"function"==typeof(f=p&&f[p]||f["@@iterator"])?f:null))for(n=m.call(n),m=0;!(i=n.next()).done;)c+=N(i=i.value,r,o,l=h+T(i,m++),a);else if("object"===l){if("function"==typeof n.then)return N(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(P,P):(e.status="pending",e.then((function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)}),(function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(n),r,o,i,a);throw r=String(n),Error("Objects are not valid as a React child (found: "+("[object Object]"===r?"object with keys {"+Object.keys(n).join(", ")+"}":r)+"). If you meant to render a collection of children, use an array instead.")}return c}function O(e,t,n){if(null==e)return e;var r=[],o=0;return N(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function L(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var R="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e)};function A(){}return u.Children={map:O,forEach:function(e,t,n){O(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return O(e,(function(){t++})),t},toArray:function(e){return O(e,(function(e){return e}))||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},u.Component=y,u.Fragment=n,u.Profiler=o,u.PureComponent=b,u.StrictMode=r,u.Suspense=c,u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=x,u.__COMPILER_RUNTIME={__proto__:null,c:function(e){return x.H.useMemoCache(e)}},u.cache=function(e){return function(){return e.apply(null,arguments)}},u.cloneElement=function(e,t,n){if(null==e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),o=e.key;if(null!=t)for(i in void 0!==t.ref&&void 0,void 0!==t.key&&(o=""+t.key),t)!S.call(t,i)||"key"===i||"__self"===i||"__source"===i||"ref"===i&&void 0===t.ref||(r[i]=t[i]);var i=arguments.length-2;if(1===i)r.children=n;else if(1<i){for(var a=Array(i),l=0;l<i;l++)a[l]=arguments[l+2];r.children=a}return E(e.type,o,void 0,0,0,r)},u.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:a,_context:e},e},u.createElement=function(e,t,n){var r,o={},i=null;if(null!=t)for(r in void 0!==t.key&&(i=""+t.key),t)S.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(o[r]=t[r]);var a=arguments.length-2;if(1===a)o.children=n;else if(1<a){for(var l=Array(a),s=0;s<a;s++)l[s]=arguments[s+2];o.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps)void 0===o[r]&&(o[r]=a[r]);return E(e,i,void 0,0,0,o)},u.createRef=function(){return{current:null}},u.forwardRef=function(e){return{$$typeof:s,render:e}},u.isValidElement=C,u.lazy=function(e){return{$$typeof:d,_payload:{_status:-1,_result:e},_init:L}},u.memo=function(e,t){return{$$typeof:f,type:e,compare:void 0===t?null:t}},u.startTransition=function(e){var t=x.T,n={};x.T=n;try{var r=e(),o=x.S;null!==o&&o(n,r),"object"==typeof r&&null!==r&&"function"==typeof r.then&&r.then(A,R)}catch(i){R(i)}finally{x.T=t}},u.unstable_useCacheRefresh=function(){return x.H.useCacheRefresh()},u.use=function(e){return x.H.use(e)},u.useActionState=function(e,t,n){return x.H.useActionState(e,t,n)},u.useCallback=function(e,t){return x.H.useCallback(e,t)},u.useContext=function(e){return x.H.useContext(e)},u.useDebugValue=function(){},u.useDeferredValue=function(e,t){return x.H.useDeferredValue(e,t)},u.useEffect=function(e,t,n){var r=x.H;if("function"==typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},u.useId=function(){return x.H.useId()},u.useImperativeHandle=function(e,t,n){return x.H.useImperativeHandle(e,t,n)},u.useInsertionEffect=function(e,t){return x.H.useInsertionEffect(e,t)},u.useLayoutEffect=function(e,t){return x.H.useLayoutEffect(e,t)},u.useMemo=function(e,t){return x.H.useMemo(e,t)},u.useOptimistic=function(e,t){return x.H.useOptimistic(e,t)},u.useReducer=function(e,t,n){return x.H.useReducer(e,t,n)},u.useRef=function(e){return x.H.useRef(e)},u.useState=function(e){return x.H.useState(e)},u.useSyncExternalStore=function(e,t,n){return x.H.useSyncExternalStore(e,t,n)},u.useTransition=function(){return x.H.useTransition()},u.version="19.1.0",u}function f(){return a||(a=1,s.exports=c()),s.exports}var d,p,h=f(),m={exports:{}},g={},y={exports:{}},v={};function b(){return p||(p=1,y.exports=(d||(d=1,function(e){function t(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,i=e[r];if(!(0<o(i,t)))break e;e[r]=t,e[n]=i,n=r}}function n(e){return 0===e.length?null:e[0]}function r(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,i=e.length,a=i>>>1;r<a;){var l=2*(r+1)-1,s=e[l],u=l+1,c=e[u];if(0>o(s,n))u<i&&0>o(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[l]=n,r=l);else{if(!(u<i&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(e.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var i=performance;e.unstable_now=function(){return i.now()}}else{var a=Date,l=a.now();e.unstable_now=function(){return a.now()-l}}var s=[],u=[],c=1,f=null,d=3,p=!1,h=!1,m=!1,g=!1,y="function"==typeof setTimeout?setTimeout:null,v="function"==typeof clearTimeout?clearTimeout:null,b="undefined"!=typeof setImmediate?setImmediate:null;function k(e){for(var o=n(u);null!==o;){if(null===o.callback)r(u);else{if(!(o.startTime<=e))break;r(u),o.sortIndex=o.expirationTime,t(s,o)}o=n(u)}}function w(e){if(m=!1,k(e),!h)if(null!==n(s))h=!0,S||(S=!0,x());else{var t=n(u);null!==t&&L(w,t.startTime-e)}}var x,S=!1,E=-1,C=5,_=-1;function T(){return!(!g&&e.unstable_now()-_<C)}function P(){if(g=!1,S){var t=e.unstable_now();_=t;var o=!0;try{e:{h=!1,m&&(m=!1,v(E),E=-1),p=!0;var i=d;try{t:{for(k(t),f=n(s);null!==f&&!(f.expirationTime>t&&T());){var a=f.callback;if("function"==typeof a){f.callback=null,d=f.priorityLevel;var l=a(f.expirationTime<=t);if(t=e.unstable_now(),"function"==typeof l){f.callback=l,k(t),o=!0;break t}f===n(s)&&r(s),k(t)}else r(s);f=n(s)}if(null!==f)o=!0;else{var c=n(u);null!==c&&L(w,c.startTime-t),o=!1}}break e}finally{f=null,d=i,p=!1}o=void 0}}finally{o?x():S=!1}}}if("function"==typeof b)x=function(){b(P)};else if("undefined"!=typeof MessageChannel){var N=new MessageChannel,O=N.port2;N.port1.onmessage=P,x=function(){O.postMessage(null)}}else x=function(){y(P,0)};function L(t,n){E=y((function(){t(e.unstable_now())}),n)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_forceFrameRate=function(e){0>e||125<e||(C=0<e?Math.floor(1e3/e):5)},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_next=function(e){switch(d){case 1:case 2:case 3:var t=3;break;default:t=d}var n=d;d=t;try{return e()}finally{d=n}},e.unstable_requestPaint=function(){g=!0},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=d;d=e;try{return t()}finally{d=n}},e.unstable_scheduleCallback=function(r,o,i){var a=e.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?a+i:a,r){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return r={id:c++,callback:o,priorityLevel:r,startTime:i,expirationTime:l=i+l,sortIndex:-1},i>a?(r.sortIndex=i,t(u,r),null===n(s)&&r===n(u)&&(m?(v(E),E=-1):m=!0,L(w,i-a))):(r.sortIndex=l,t(s,r),h||p||(h=!0,S||(S=!0,x()))),r},e.unstable_shouldYield=T,e.unstable_wrapCallback=function(e){var t=d;return function(){var n=d;d=t;try{return e.apply(this,arguments)}finally{d=n}}}}(v)),v)),y.exports}var k,w,x,S,E={exports:{}},C={};function _(){if(k)return C;k=1;var e=f();function t(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function n(){}var r={d:{f:n,r:function(){throw Error(t(522))},D:n,C:n,L:n,m:n,X:n,S:n,M:n},p:0,findDOMNode:null},o=Symbol.for("react.portal");var i=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function a(e,t){return"font"===e?"":"string"==typeof t?"use-credentials"===t?t:"":void 0}return C.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,C.createPortal=function(e,n){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!n||1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType)throw Error(t(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:o,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,n,null,r)},C.flushSync=function(e){var t=i.T,n=r.p;try{if(i.T=null,r.p=2,e)return e()}finally{i.T=t,r.p=n,r.d.f()}},C.preconnect=function(e,t){"string"==typeof e&&(t?t="string"==typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,r.d.C(e,t))},C.prefetchDNS=function(e){"string"==typeof e&&r.d.D(e)},C.preinit=function(e,t){if("string"==typeof e&&t&&"string"==typeof t.as){var n=t.as,o=a(n,t.crossOrigin),i="string"==typeof t.integrity?t.integrity:void 0,l="string"==typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?r.d.S(e,"string"==typeof t.precedence?t.precedence:void 0,{crossOrigin:o,integrity:i,fetchPriority:l}):"script"===n&&r.d.X(e,{crossOrigin:o,integrity:i,fetchPriority:l,nonce:"string"==typeof t.nonce?t.nonce:void 0})}},C.preinitModule=function(e,t){if("string"==typeof e)if("object"==typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=a(t.as,t.crossOrigin);r.d.M(e,{crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0})}}else null==t&&r.d.M(e)},C.preload=function(e,t){if("string"==typeof e&&"object"==typeof t&&null!==t&&"string"==typeof t.as){var n=t.as,o=a(n,t.crossOrigin);r.d.L(e,n,{crossOrigin:o,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0,type:"string"==typeof t.type?t.type:void 0,fetchPriority:"string"==typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"==typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"==typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"==typeof t.imageSizes?t.imageSizes:void 0,media:"string"==typeof t.media?t.media:void 0})}},C.preloadModule=function(e,t){if("string"==typeof e)if(t){var n=a(t.as,t.crossOrigin);r.d.m(e,{as:"string"==typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0})}else r.d.m(e)},C.requestFormReset=function(e){r.d.r(e)},C.unstable_batchedUpdates=function(e,t){return e(t)},C.useFormState=function(e,t,n){return i.H.useFormState(e,t,n)},C.useFormStatus=function(){return i.H.useHostTransitionStatus()},C.version="19.1.0",C}function T(){if(x)return g;x=1;var e=b(),t=f(),n=(w||(w=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){}}(),E.exports=_()),E.exports);function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function i(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function a(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function l(e){if(i(e)!==e)throw Error(r(188))}function s(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=s(e)))return t;e=e.sibling}return null}var u=Object.assign,c=Symbol.for("react.element"),d=Symbol.for("react.transitional.element"),p=Symbol.for("react.portal"),h=Symbol.for("react.fragment"),m=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),v=Symbol.for("react.provider"),k=Symbol.for("react.consumer"),S=Symbol.for("react.context"),C=Symbol.for("react.forward_ref"),T=Symbol.for("react.suspense"),P=Symbol.for("react.suspense_list"),N=Symbol.for("react.memo"),O=Symbol.for("react.lazy"),L=Symbol.for("react.activity"),R=Symbol.for("react.memo_cache_sentinel"),A=Symbol.iterator;function j(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=A&&e[A]||e["@@iterator"])?e:null}var z=Symbol.for("react.client.reference");function D(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===z?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case h:return"Fragment";case y:return"Profiler";case m:return"StrictMode";case T:return"Suspense";case P:return"SuspenseList";case L:return"Activity"}if("object"==typeof e)switch(e.$$typeof){case p:return"Portal";case S:return(e.displayName||"Context")+".Provider";case k:return(e._context.displayName||"Context")+".Consumer";case C:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case N:return null!==(t=e.displayName||null)?t:D(e.type)||"Memo";case O:t=e._payload,e=e._init;try{return D(e(t))}catch(n){}}return null}var M=Array.isArray,I=t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F=n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,U={pending:!1,data:null,method:null,action:null},B=[],H=-1;function $(e){return{current:e}}function V(e){0>H||(e.current=B[H],B[H]=null,H--)}function W(e,t){H++,B[H]=e.current,e.current=t}var q=$(null),Q=$(null),K=$(null),Y=$(null);function X(e,t){switch(W(K,t),W(Q,e),W(q,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?uf(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=cf(t=uf(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}V(q),W(q,e)}function J(){V(q),V(Q),V(K)}function G(e){null!==e.memoizedState&&W(Y,e);var t=q.current,n=cf(t,e.type);t!==n&&(W(Q,e),W(q,n))}function Z(e){Q.current===e&&(V(q),V(Q)),Y.current===e&&(V(Y),Zf._currentValue=U)}var ee=Object.prototype.hasOwnProperty,te=e.unstable_scheduleCallback,ne=e.unstable_cancelCallback,re=e.unstable_shouldYield,oe=e.unstable_requestPaint,ie=e.unstable_now,ae=e.unstable_getCurrentPriorityLevel,le=e.unstable_ImmediatePriority,se=e.unstable_UserBlockingPriority,ue=e.unstable_NormalPriority,ce=e.unstable_LowPriority,fe=e.unstable_IdlePriority,de=e.log,pe=e.unstable_setDisableYieldValue,he=null,me=null;function ge(e){if("function"==typeof de&&pe(e),me&&"function"==typeof me.setStrictMode)try{me.setStrictMode(he,e)}catch(t){}}var ye=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(ve(e)/be|0)|0},ve=Math.log,be=Math.LN2;var ke=256,we=4194304;function xe(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Se(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var o=0,i=e.suspendedLanes,a=e.pingedLanes;e=e.warmLanes;var l=134217727&r;return 0!==l?0!==(r=l&~i)?o=xe(r):0!==(a&=l)?o=xe(a):n||0!==(n=l&~e)&&(o=xe(n)):0!==(l=r&~i)?o=xe(l):0!==a?o=xe(a):n||0!==(n=r&~e)&&(o=xe(n)),0===o?0:0!==t&&t!==o&&0===(t&i)&&((i=o&-o)>=(n=t&-t)||32===i&&4194048&n)?t:o}function Ee(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function Ce(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function _e(){var e=ke;return!(4194048&(ke<<=1))&&(ke=256),e}function Te(){var e=we;return!(62914560&(we<<=1))&&(we=4194304),e}function Pe(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ne(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Oe(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-ye(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Le(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ye(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}function Re(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ae(e){return 2<(e&=-e)?8<e?134217727&e?32:268435456:8:2}function je(){var e=F.p;return 0!==e?e:void 0===(e=window.event)?32:hd(e.type)}var ze=Math.random().toString(36).slice(2),De="__reactFiber$"+ze,Me="__reactProps$"+ze,Ie="__reactContainer$"+ze,Fe="__reactEvents$"+ze,Ue="__reactListeners$"+ze,Be="__reactHandles$"+ze,He="__reactResources$"+ze,$e="__reactMarker$"+ze;function Ve(e){delete e[De],delete e[Me],delete e[Fe],delete e[Ue],delete e[Be]}function We(e){var t=e[De];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ie]||n[De]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=Ef(e);null!==e;){if(n=e[De])return n;e=Ef(e)}return t}n=(e=n).parentNode}return null}function qe(e){if(e=e[De]||e[Ie]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function Qe(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(r(33))}function Ke(e){var t=e[He];return t||(t=e[He]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ye(e){e[$e]=!0}var Xe=new Set,Je={};function Ge(e,t){Ze(e,t),Ze(e+"Capture",t)}function Ze(e,t){for(Je[e]=t,e=0;e<t.length;e++)Xe.add(t[e])}var et,tt,nt=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),rt={},ot={};function it(e,t,n){if(o=t,ee.call(ot,o)||!ee.call(rt,o)&&(nt.test(o)?ot[o]=!0:(rt[o]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var o}function at(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function lt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function st(e){if(void 0===et)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);et=t&&t[1]||"",tt=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+et+e+tt}var ut=!1;function ct(e,t){if(!e||ut)return"";ut=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(o){var r=o}Reflect.construct(e,[],n)}else{try{n.call()}catch(i){r=i}e.call(n.prototype)}}else{try{throw Error()}catch(a){r=a}(n=e())&&"function"==typeof n.catch&&n.catch((function(){}))}}catch(l){if(l&&r&&"string"==typeof l.stack)return[l.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=r.DetermineComponentFrameRoot(),a=i[0],l=i[1];if(a&&l){var s=a.split("\n"),u=l.split("\n");for(o=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;o<u.length&&!u[o].includes("DetermineComponentFrameRoot");)o++;if(r===s.length||o===u.length)for(r=s.length-1,o=u.length-1;1<=r&&0<=o&&s[r]!==u[o];)o--;for(;1<=r&&0<=o;r--,o--)if(s[r]!==u[o]){if(1!==r||1!==o)do{if(r--,0>--o||s[r]!==u[o]){var c="\n"+s[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=o);break}}}finally{ut=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?st(n):""}function ft(e){switch(e.tag){case 26:case 27:case 5:return st(e.type);case 16:return st("Lazy");case 13:return st("Suspense");case 19:return st("SuspenseList");case 0:case 15:return ct(e.type,!1);case 11:return ct(e.type.render,!1);case 1:return ct(e.type,!0);case 31:return st("Activity");default:return""}}function dt(e){try{var t="";do{t+=ft(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function pt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ht(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function mt(e){e._valueTracker||(e._valueTracker=function(e){var t=ht(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,i.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function gt(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ht(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function yt(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var vt=/[\n"\\]/g;function bt(e){return e.replace(vt,(function(e){return"\\"+e.charCodeAt(0).toString(16)+" "}))}function kt(e,t,n,r,o,i,a,l){e.name="",null!=a&&"function"!=typeof a&&"symbol"!=typeof a&&"boolean"!=typeof a?e.type=a:e.removeAttribute("type"),null!=t?"number"===a?(0===t&&""===e.value||e.value!=t)&&(e.value=""+pt(t)):e.value!==""+pt(t)&&(e.value=""+pt(t)):"submit"!==a&&"reset"!==a||e.removeAttribute("value"),null!=t?xt(e,a,pt(t)):null!=n?xt(e,a,pt(n)):null!=r&&e.removeAttribute("value"),null==o&&null!=i&&(e.defaultChecked=!!i),null!=o&&(e.checked=o&&"function"!=typeof o&&"symbol"!=typeof o),null!=l&&"function"!=typeof l&&"symbol"!=typeof l&&"boolean"!=typeof l?e.name=""+pt(l):e.removeAttribute("name")}function wt(e,t,n,r,o,i,a,l){if(null!=i&&"function"!=typeof i&&"symbol"!=typeof i&&"boolean"!=typeof i&&(e.type=i),null!=t||null!=n){if(("submit"===i||"reset"===i)&&null==t)return;n=null!=n?""+pt(n):"",t=null!=t?""+pt(t):n,l||t===e.value||(e.value=t),e.defaultValue=t}r="function"!=typeof(r=null!=r?r:o)&&"symbol"!=typeof r&&!!r,e.checked=l?e.checked:!!r,e.defaultChecked=!!r,null!=a&&"function"!=typeof a&&"symbol"!=typeof a&&"boolean"!=typeof a&&(e.name=a)}function xt(e,t,n){"number"===t&&yt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function St(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+pt(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function Et(e,t,n){null==t||((t=""+pt(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+pt(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function Ct(e,t,n,o){if(null==t){if(null!=o){if(null!=n)throw Error(r(92));if(M(o)){if(1<o.length)throw Error(r(93));o=o[0]}n=o}null==n&&(n=""),t=n}n=pt(t),e.defaultValue=n,(o=e.textContent)===n&&""!==o&&null!==o&&(e.value=o)}function _t(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var Tt=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Pt(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"==typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!=typeof n||0===n||Tt.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Nt(e,t,n){if(null!=t&&"object"!=typeof t)throw Error(r(62));if(e=e.style,null!=n){for(var o in n)!n.hasOwnProperty(o)||null!=t&&t.hasOwnProperty(o)||(0===o.indexOf("--")?e.setProperty(o,""):"float"===o?e.cssFloat="":e[o]="");for(var i in t)o=t[i],t.hasOwnProperty(i)&&n[i]!==o&&Pt(e,i,o)}else for(var a in t)t.hasOwnProperty(a)&&Pt(e,a,t[a])}function Ot(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Lt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Rt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function At(e){return Rt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var jt=null;function zt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Dt=null,Mt=null;function It(e){var t=qe(e);if(t&&(e=t.stateNode)){var n=e[Me]||null;e:switch(e=t.stateNode,t.type){case"input":if(kt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+bt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var o=n[t];if(o!==e&&o.form===e.form){var i=o[Me]||null;if(!i)throw Error(r(90));kt(o,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<n.length;t++)(o=n[t]).form===e.form&&gt(o)}break e;case"textarea":Et(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&St(e,!!n.multiple,t,!1)}}}var Ft=!1;function Ut(e,t,n){if(Ft)return e(t,n);Ft=!0;try{return e(t)}finally{if(Ft=!1,(null!==Dt||null!==Mt)&&(Wu(),Dt&&(t=Dt,e=Mt,Mt=Dt=null,It(t),e)))for(t=0;t<e.length;t++)It(e[t])}}function Bt(e,t){var n=e.stateNode;if(null===n)return null;var o=n[Me]||null;if(null===o)return null;n=o[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(o=!o.disabled)||(o=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!o;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(r(231,t,typeof n));return n}var Ht=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),$t=!1;if(Ht)try{var Vt={};Object.defineProperty(Vt,"passive",{get:function(){$t=!0}}),window.addEventListener("test",Vt,Vt),window.removeEventListener("test",Vt,Vt)}catch(Id){$t=!1}var Wt=null,qt=null,Qt=null;function Kt(){if(Qt)return Qt;var e,t,n=qt,r=n.length,o="value"in Wt?Wt.value:Wt.textContent,i=o.length;for(e=0;e<r&&n[e]===o[e];e++);var a=r-e;for(t=1;t<=a&&n[r-t]===o[i-t];t++);return Qt=o.slice(e,1<t?1-t:void 0)}function Yt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Xt(){return!0}function Jt(){return!1}function Gt(e){function t(t,n,r,o,i){for(var a in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(a)&&(t=e[a],this[a]=t?t(o):o[a]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?Xt:Jt,this.isPropagationStopped=Jt,this}return u(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Xt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Xt)},persist:function(){},isPersistent:Xt}),t}var Zt,en,tn,nn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},rn=Gt(nn),on=u({},nn,{view:0,detail:0}),an=Gt(on),ln=u({},on,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:vn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==tn&&(tn&&"mousemove"===e.type?(Zt=e.screenX-tn.screenX,en=e.screenY-tn.screenY):en=Zt=0,tn=e),Zt)},movementY:function(e){return"movementY"in e?e.movementY:en}}),sn=Gt(ln),un=Gt(u({},ln,{dataTransfer:0})),cn=Gt(u({},on,{relatedTarget:0})),fn=Gt(u({},nn,{animationName:0,elapsedTime:0,pseudoElement:0})),dn=Gt(u({},nn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),pn=Gt(u({},nn,{data:0})),hn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},mn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},gn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function yn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=gn[e])&&!!t[e]}function vn(){return yn}var bn=Gt(u({},on,{key:function(e){if(e.key){var t=hn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Yt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?mn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:vn,charCode:function(e){return"keypress"===e.type?Yt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Yt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),kn=Gt(u({},ln,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),wn=Gt(u({},on,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:vn})),xn=Gt(u({},nn,{propertyName:0,elapsedTime:0,pseudoElement:0})),Sn=Gt(u({},ln,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),En=Gt(u({},nn,{newState:0,oldState:0})),Cn=[9,13,27,32],_n=Ht&&"CompositionEvent"in window,Tn=null;Ht&&"documentMode"in document&&(Tn=document.documentMode);var Pn=Ht&&"TextEvent"in window&&!Tn,Nn=Ht&&(!_n||Tn&&8<Tn&&11>=Tn),On=String.fromCharCode(32),Ln=!1;function Rn(e,t){switch(e){case"keyup":return-1!==Cn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function An(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var jn=!1;var zn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Dn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!zn[e.type]:"textarea"===t}function Mn(e,t,n,r){Dt?Mt?Mt.push(r):Mt=[r]:Dt=r,0<(t=Kc(t,"onChange")).length&&(n=new rn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var In=null,Fn=null;function Un(e){Uc(e,0)}function Bn(e){if(gt(Qe(e)))return e}function Hn(e,t){if("change"===e)return t}var $n=!1;if(Ht){var Vn;if(Ht){var Wn="oninput"in document;if(!Wn){var qn=document.createElement("div");qn.setAttribute("oninput","return;"),Wn="function"==typeof qn.oninput}Vn=Wn}else Vn=!1;$n=Vn&&(!document.documentMode||9<document.documentMode)}function Qn(){In&&(In.detachEvent("onpropertychange",Kn),Fn=In=null)}function Kn(e){if("value"===e.propertyName&&Bn(Fn)){var t=[];Mn(t,Fn,e,zt(e)),Ut(Un,t)}}function Yn(e,t,n){"focusin"===e?(Qn(),Fn=n,(In=t).attachEvent("onpropertychange",Kn)):"focusout"===e&&Qn()}function Xn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Bn(Fn)}function Jn(e,t){if("click"===e)return Bn(t)}function Gn(e,t){if("input"===e||"change"===e)return Bn(t)}var Zn="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function er(e,t){if(Zn(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!ee.call(t,o)||!Zn(e[o],t[o]))return!1}return!0}function tr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function nr(e,t){var n,r=tr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=tr(r)}}function rr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?rr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function or(e){for(var t=yt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=yt((e=t.contentWindow).document)}return t}function ir(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var ar=Ht&&"documentMode"in document&&11>=document.documentMode,lr=null,sr=null,ur=null,cr=!1;function fr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;cr||null==lr||lr!==yt(r)||("selectionStart"in(r=lr)&&ir(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ur&&er(ur,r)||(ur=r,0<(r=Kc(sr,"onSelect")).length&&(t=new rn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=lr)))}function dr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var pr={animationend:dr("Animation","AnimationEnd"),animationiteration:dr("Animation","AnimationIteration"),animationstart:dr("Animation","AnimationStart"),transitionrun:dr("Transition","TransitionRun"),transitionstart:dr("Transition","TransitionStart"),transitioncancel:dr("Transition","TransitionCancel"),transitionend:dr("Transition","TransitionEnd")},hr={},mr={};function gr(e){if(hr[e])return hr[e];if(!pr[e])return e;var t,n=pr[e];for(t in n)if(n.hasOwnProperty(t)&&t in mr)return hr[e]=n[t];return e}Ht&&(mr=document.createElement("div").style,"AnimationEvent"in window||(delete pr.animationend.animation,delete pr.animationiteration.animation,delete pr.animationstart.animation),"TransitionEvent"in window||delete pr.transitionend.transition);var yr=gr("animationend"),vr=gr("animationiteration"),br=gr("animationstart"),kr=gr("transitionrun"),wr=gr("transitionstart"),xr=gr("transitioncancel"),Sr=gr("transitionend"),Er=new Map,Cr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function _r(e,t){Er.set(e,t),Ge(t,[e])}Cr.push("scrollEnd");var Tr=new WeakMap;function Pr(e,t){if("object"==typeof e&&null!==e){var n=Tr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:dt(t)},Tr.set(e,t),t)}return{value:e,source:t,stack:dt(t)}}var Nr=[],Or=0,Lr=0;function Rr(){for(var e=Or,t=Lr=Or=0;t<e;){var n=Nr[t];Nr[t++]=null;var r=Nr[t];Nr[t++]=null;var o=Nr[t];Nr[t++]=null;var i=Nr[t];if(Nr[t++]=null,null!==r&&null!==o){var a=r.pending;null===a?o.next=o:(o.next=a.next,a.next=o),r.pending=o}0!==i&&Dr(n,o,i)}}function Ar(e,t,n,r){Nr[Or++]=e,Nr[Or++]=t,Nr[Or++]=n,Nr[Or++]=r,Lr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function jr(e,t,n,r){return Ar(e,t,n,r),Mr(e)}function zr(e,t){return Ar(e,null,null,t),Mr(e)}function Dr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var o=!1,i=e.return;null!==i;)i.childLanes|=n,null!==(r=i.alternate)&&(r.childLanes|=n),22===i.tag&&(null===(e=i.stateNode)||1&e._visibility||(o=!0)),e=i,i=i.return;return 3===e.tag?(i=e.stateNode,o&&null!==t&&(o=31-ye(n),null===(r=(e=i.hiddenUpdates)[o])?e[o]=[t]:r.push(t),t.lane=536870912|n),i):null}function Mr(e){if(50<Du)throw Du=0,Mu=null,Error(r(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Ir={};function Fr(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ur(e,t,n,r){return new Fr(e,t,n,r)}function Br(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Hr(e,t){var n=e.alternate;return null===n?((n=Ur(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function $r(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Vr(e,t,n,o,i,a){var l=0;if(o=e,"function"==typeof e)Br(e)&&(l=1);else if("string"==typeof e)l=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!=typeof t.precedence||"string"!=typeof t.href||""===t.href)break;return!0;case"link":if("string"!=typeof t.rel||"string"!=typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"==typeof t.precedence&&null==e);case"script":if(t.async&&"function"!=typeof t.async&&"symbol"!=typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"==typeof t.src)return!0}return!1}(e,n,q.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case L:return(e=Ur(31,n,t,i)).elementType=L,e.lanes=a,e;case h:return Wr(n.children,i,a,t);case m:l=8,i|=24;break;case y:return(e=Ur(12,n,t,2|i)).elementType=y,e.lanes=a,e;case T:return(e=Ur(13,n,t,i)).elementType=T,e.lanes=a,e;case P:return(e=Ur(19,n,t,i)).elementType=P,e.lanes=a,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case v:case S:l=10;break e;case k:l=9;break e;case C:l=11;break e;case N:l=14;break e;case O:l=16,o=null;break e}l=29,n=Error(r(130,null===e?"null":typeof e,"")),o=null}return(t=Ur(l,n,t,i)).elementType=e,t.type=o,t.lanes=a,t}function Wr(e,t,n,r){return(e=Ur(7,e,r,t)).lanes=n,e}function qr(e,t,n){return(e=Ur(6,e,null,t)).lanes=n,e}function Qr(e,t,n){return(t=Ur(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Kr=[],Yr=0,Xr=null,Jr=0,Gr=[],Zr=0,eo=null,to=1,no="";function ro(e,t){Kr[Yr++]=Jr,Kr[Yr++]=Xr,Xr=e,Jr=t}function oo(e,t,n){Gr[Zr++]=to,Gr[Zr++]=no,Gr[Zr++]=eo,eo=e;var r=to;e=no;var o=32-ye(r)-1;r&=~(1<<o),n+=1;var i=32-ye(t)+o;if(30<i){var a=o-o%5;i=(r&(1<<a)-1).toString(32),r>>=a,o-=a,to=1<<32-ye(t)+o|n<<o|r,no=i+e}else to=1<<i|n<<o|r,no=e}function io(e){null!==e.return&&(ro(e,1),oo(e,1,0))}function ao(e){for(;e===Xr;)Xr=Kr[--Yr],Kr[Yr]=null,Jr=Kr[--Yr],Kr[Yr]=null;for(;e===eo;)eo=Gr[--Zr],Gr[Zr]=null,no=Gr[--Zr],Gr[Zr]=null,to=Gr[--Zr],Gr[Zr]=null}var lo=null,so=null,uo=!1,co=null,fo=!1,po=Error(r(519));function ho(e){throw ko(Pr(Error(r(418,"")),e)),po}function mo(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[De]=e,t[Me]=r,n){case"dialog":Bc("cancel",t),Bc("close",t);break;case"iframe":case"object":case"embed":Bc("load",t);break;case"video":case"audio":for(n=0;n<Ic.length;n++)Bc(Ic[n],t);break;case"source":Bc("error",t);break;case"img":case"image":case"link":Bc("error",t),Bc("load",t);break;case"details":Bc("toggle",t);break;case"input":Bc("invalid",t),wt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),mt(t);break;case"select":Bc("invalid",t);break;case"textarea":Bc("invalid",t),Ct(t,r.value,r.defaultValue,r.children),mt(t)}"string"!=typeof(n=r.children)&&"number"!=typeof n&&"bigint"!=typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||ef(t.textContent,n)?(null!=r.popover&&(Bc("beforetoggle",t),Bc("toggle",t)),null!=r.onScroll&&Bc("scroll",t),null!=r.onScrollEnd&&Bc("scrollend",t),null!=r.onClick&&(t.onclick=tf),t=!0):t=!1,t||ho(e)}function go(e){for(lo=e.return;lo;)switch(lo.tag){case 5:case 13:return void(fo=!1);case 27:case 3:return void(fo=!0);default:lo=lo.return}}function yo(e){if(e!==lo)return!1;if(!uo)return go(e),uo=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||ff(e.type,e.memoizedProps)),t=!t),t&&so&&ho(e),go(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(r(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){so=xf(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}so=null}}else 27===n?(n=so,vf(e.type)?(e=Sf,Sf=null,so=e):so=n):so=lo?xf(e.stateNode.nextSibling):null;return!0}function vo(){so=lo=null,uo=!1}function bo(){var e=co;return null!==e&&(null===Su?Su=e:Su.push.apply(Su,e),co=null),e}function ko(e){null===co?co=[e]:co.push(e)}var wo=$(null),xo=null,So=null;function Eo(e,t,n){W(wo,t._currentValue),t._currentValue=n}function Co(e){e._currentValue=wo.current,V(wo)}function _o(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function To(e,t,n,o){var i=e.child;for(null!==i&&(i.return=e);null!==i;){var a=i.dependencies;if(null!==a){var l=i.child;a=a.firstContext;e:for(;null!==a;){var s=a;a=i;for(var u=0;u<t.length;u++)if(s.context===t[u]){a.lanes|=n,null!==(s=a.alternate)&&(s.lanes|=n),_o(a.return,n,e),o||(l=null);break e}a=s.next}}else if(18===i.tag){if(null===(l=i.return))throw Error(r(341));l.lanes|=n,null!==(a=l.alternate)&&(a.lanes|=n),_o(l,n,e),l=null}else l=i.child;if(null!==l)l.return=i;else for(l=i;null!==l;){if(l===e){l=null;break}if(null!==(i=l.sibling)){i.return=l.return,l=i;break}l=l.return}i=l}}function Po(e,t,n,o){e=null;for(var i=t,a=!1;null!==i;){if(!a)if(524288&i.flags)a=!0;else if(262144&i.flags)break;if(10===i.tag){var l=i.alternate;if(null===l)throw Error(r(387));if(null!==(l=l.memoizedProps)){var s=i.type;Zn(i.pendingProps.value,l.value)||(null!==e?e.push(s):e=[s])}}else if(i===Y.current){if(null===(l=i.alternate))throw Error(r(387));l.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(null!==e?e.push(Zf):e=[Zf])}i=i.return}null!==e&&To(t,e,n,o),t.flags|=262144}function No(e){for(e=e.firstContext;null!==e;){if(!Zn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Oo(e){xo=e,So=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Lo(e){return Ao(xo,e)}function Ro(e,t){return null===xo&&Oo(e),Ao(e,t)}function Ao(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===So){if(null===e)throw Error(r(308));So=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else So=So.next=t;return n}var jo="undefined"!=typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach((function(e){return e()}))}},zo=e.unstable_scheduleCallback,Do=e.unstable_NormalPriority,Mo={$$typeof:S,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Io(){return{controller:new jo,data:new Map,refCount:0}}function Fo(e){e.refCount--,0===e.refCount&&zo(Do,(function(){e.controller.abort()}))}var Uo=null,Bo=0,Ho=0,$o=null;function Vo(){if(0===--Bo&&null!==Uo){null!==$o&&($o.status="fulfilled");var e=Uo;Uo=null,Ho=0,$o=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Wo=I.S;I.S=function(e,t){"object"==typeof t&&null!==t&&"function"==typeof t.then&&function(e,t){if(null===Uo){var n=Uo=[];Bo=0,Ho=Ac(),$o={status:"pending",value:void 0,then:function(e){n.push(e)}}}Bo++,t.then(Vo,Vo)}(0,t),null!==Wo&&Wo(e,t)};var qo=$(null);function Qo(){var e=qo.current;return null!==e?e:lu.pooledCache}function Ko(e,t){W(qo,null===t?qo.current:t.pool)}function Yo(){var e=Qo();return null===e?null:{parent:Mo._currentValue,pool:e}}var Xo=Error(r(460)),Jo=Error(r(474)),Go=Error(r(542)),Zo={then:function(){}};function ei(e){return"fulfilled"===(e=e.status)||"rejected"===e}function ti(){}function ni(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(ti,ti),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw ii(e=t.reason),e;default:if("string"==typeof t.status)t.then(ti,ti);else{if(null!==(e=lu)&&100<e.shellSuspendCounter)throw Error(r(482));(e=t).status="pending",e.then((function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}}),(function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}}))}switch(t.status){case"fulfilled":return t.value;case"rejected":throw ii(e=t.reason),e}throw ri=t,Xo}}var ri=null;function oi(){if(null===ri)throw Error(r(459));var e=ri;return ri=null,e}function ii(e){if(e===Xo||e===Go)throw Error(r(483))}var ai=!1;function li(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function si(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ui(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ci(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&au){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,t=Mr(e),Dr(e,null,n),t}return Ar(e,r,t,n),Mr(e)}function fi(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Le(e,n)}}function di(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,i=null;if(null!==(n=n.firstBaseUpdate)){do{var a={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===i?o=i=a:i=i.next=a,n=n.next}while(null!==n);null===i?o=i=t:i=i.next=t}else o=i=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var pi=!1;function hi(){if(pi){if(null!==$o)throw $o}}function mi(e,t,n,r){pi=!1;var o=e.updateQueue;ai=!1;var i=o.firstBaseUpdate,a=o.lastBaseUpdate,l=o.shared.pending;if(null!==l){o.shared.pending=null;var s=l,c=s.next;s.next=null,null===a?i=c:a.next=c,a=s;var f=e.alternate;null!==f&&((l=(f=f.updateQueue).lastBaseUpdate)!==a&&(null===l?f.firstBaseUpdate=c:l.next=c,f.lastBaseUpdate=s))}if(null!==i){var d=o.baseState;for(a=0,f=c=s=null,l=i;;){var p=-536870913&l.lane,h=p!==l.lane;if(h?(uu&p)===p:(r&p)===p){0!==p&&p===Ho&&(pi=!0),null!==f&&(f=f.next={lane:0,tag:l.tag,payload:l.payload,callback:null,next:null});e:{var m=e,g=l;p=t;var y=n;switch(g.tag){case 1:if("function"==typeof(m=g.payload)){d=m.call(y,d,p);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null==(p="function"==typeof(m=g.payload)?m.call(y,d,p):m))break e;d=u({},d,p);break e;case 2:ai=!0}}null!==(p=l.callback)&&(e.flags|=64,h&&(e.flags|=8192),null===(h=o.callbacks)?o.callbacks=[p]:h.push(p))}else h={lane:p,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===f?(c=f=h,s=d):f=f.next=h,a|=p;if(null===(l=l.next)){if(null===(l=o.shared.pending))break;l=(h=l).next,h.next=null,o.lastBaseUpdate=h,o.shared.pending=null}}null===f&&(s=d),o.baseState=s,o.firstBaseUpdate=c,o.lastBaseUpdate=f,null===i&&(o.shared.lanes=0),yu|=a,e.lanes=a,e.memoizedState=d}}function gi(e,t){if("function"!=typeof e)throw Error(r(191,e));e.call(t)}function yi(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)gi(n[e],t)}var vi=$(null),bi=$(0);function ki(e,t){W(bi,e=mu),W(vi,t),mu=e|t.baseLanes}function wi(){W(bi,mu),W(vi,vi.current)}function xi(){mu=bi.current,V(vi),V(bi)}var Si=0,Ei=null,Ci=null,_i=null,Ti=!1,Pi=!1,Ni=!1,Oi=0,Li=0,Ri=null,Ai=0;function ji(){throw Error(r(321))}function zi(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Zn(e[n],t[n]))return!1;return!0}function Di(e,t,n,r,o,i){return Si=i,Ei=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,I.H=null===e||null===e.memoizedState?Xa:Ja,Ni=!1,i=n(r,o),Ni=!1,Pi&&(i=Ii(t,n,r,o)),Mi(e),i}function Mi(e){I.H=Ya;var t=null!==Ci&&null!==Ci.next;if(Si=0,_i=Ci=Ei=null,Ti=!1,Li=0,Ri=null,t)throw Error(r(300));null===e||Ll||null!==(e=e.dependencies)&&No(e)&&(Ll=!0)}function Ii(e,t,n,o){Ei=e;var i=0;do{if(Pi&&(Ri=null),Li=0,Pi=!1,25<=i)throw Error(r(301));if(i+=1,_i=Ci=null,null!=e.updateQueue){var a=e.updateQueue;a.lastEffect=null,a.events=null,a.stores=null,null!=a.memoCache&&(a.memoCache.index=0)}I.H=Ga,a=t(n,o)}while(Pi);return a}function Fi(){var e=I.H,t=e.useState()[0];return t="function"==typeof t.then?Wi(t):t,e=e.useState()[0],(null!==Ci?Ci.memoizedState:null)!==e&&(Ei.flags|=1024),t}function Ui(){var e=0!==Oi;return Oi=0,e}function Bi(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Hi(e){if(Ti){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}Ti=!1}Si=0,_i=Ci=Ei=null,Pi=!1,Li=Oi=0,Ri=null}function $i(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===_i?Ei.memoizedState=_i=e:_i=_i.next=e,_i}function Vi(){if(null===Ci){var e=Ei.alternate;e=null!==e?e.memoizedState:null}else e=Ci.next;var t=null===_i?Ei.memoizedState:_i.next;if(null!==t)_i=t,Ci=e;else{if(null===e){if(null===Ei.alternate)throw Error(r(467));throw Error(r(310))}e={memoizedState:(Ci=e).memoizedState,baseState:Ci.baseState,baseQueue:Ci.baseQueue,queue:Ci.queue,next:null},null===_i?Ei.memoizedState=_i=e:_i=_i.next=e}return _i}function Wi(e){var t=Li;return Li+=1,null===Ri&&(Ri=[]),e=ni(Ri,e,t),t=Ei,null===(null===_i?t.memoizedState:_i.next)&&(t=t.alternate,I.H=null===t||null===t.memoizedState?Xa:Ja),e}function qi(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return Wi(e);if(e.$$typeof===S)return Lo(e)}throw Error(r(438,String(e)))}function Qi(e){var t=null,n=Ei.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=Ei.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map((function(e){return e.slice()})),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},Ei.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=R;return t.index++,n}function Ki(e,t){return"function"==typeof t?t(e):t}function Yi(e){return Xi(Vi(),Ci,e)}function Xi(e,t,n){var o=e.queue;if(null===o)throw Error(r(311));o.lastRenderedReducer=n;var i=e.baseQueue,a=o.pending;if(null!==a){if(null!==i){var l=i.next;i.next=a.next,a.next=l}t.baseQueue=i=a,o.pending=null}if(a=e.baseState,null===i)e.memoizedState=a;else{var s=l=null,u=null,c=t=i.next,f=!1;do{var d=-536870913&c.lane;if(d!==c.lane?(uu&d)===d:(Si&d)===d){var p=c.revertLane;if(0===p)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),d===Ho&&(f=!0);else{if((Si&p)===p){c=c.next,p===Ho&&(f=!0);continue}d={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=d,l=a):u=u.next=d,Ei.lanes|=p,yu|=p}d=c.action,Ni&&n(a,d),a=c.hasEagerState?c.eagerState:n(a,d)}else p={lane:d,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=p,l=a):u=u.next=p,Ei.lanes|=d,yu|=d;c=c.next}while(null!==c&&c!==t);if(null===u?l=a:u.next=s,!Zn(a,e.memoizedState)&&(Ll=!0,f&&null!==(n=$o)))throw n;e.memoizedState=a,e.baseState=l,e.baseQueue=u,o.lastRenderedState=a}return null===i&&(o.lanes=0),[e.memoizedState,o.dispatch]}function Ji(e){var t=Vi(),n=t.queue;if(null===n)throw Error(r(311));n.lastRenderedReducer=e;var o=n.dispatch,i=n.pending,a=t.memoizedState;if(null!==i){n.pending=null;var l=i=i.next;do{a=e(a,l.action),l=l.next}while(l!==i);Zn(a,t.memoizedState)||(Ll=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,o]}function Gi(e,t,n){var o=Ei,i=Vi(),a=uo;if(a){if(void 0===n)throw Error(r(407));n=n()}else n=t();var l=!Zn((Ci||i).memoizedState,n);if(l&&(i.memoizedState=n,Ll=!0),i=i.queue,wa(2048,8,ta.bind(null,o,i,e),[e]),i.getSnapshot!==t||l||null!==_i&&1&_i.memoizedState.tag){if(o.flags|=2048,va(9,{destroy:void 0,resource:void 0},ea.bind(null,o,i,n,t),null),null===lu)throw Error(r(349));a||124&Si||Zi(o,t,n)}return n}function Zi(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=Ei.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},Ei.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function ea(e,t,n,r){t.value=n,t.getSnapshot=r,na(t)&&ra(e)}function ta(e,t,n){return n((function(){na(t)&&ra(e)}))}function na(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Zn(e,n)}catch(r){return!0}}function ra(e){var t=zr(e,2);null!==t&&Uu(t,e,2)}function oa(e){var t=$i();if("function"==typeof e){var n=e;if(e=n(),Ni){ge(!0);try{n()}finally{ge(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ki,lastRenderedState:e},t}function ia(e,t,n,r){return e.baseState=n,Xi(e,Ci,"function"==typeof r?r:Ki)}function aa(e,t,n,o,i){if(qa(e))throw Error(r(485));if(null!==(e=t.action)){var a={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){a.listeners.push(e)}};null!==I.T?n(!0):a.isTransition=!1,o(a),null===(n=t.pending)?(a.next=t.pending=a,la(t,a)):(a.next=n.next,t.pending=n.next=a)}}function la(e,t){var n=t.action,r=t.payload,o=e.state;if(t.isTransition){var i=I.T,a={};I.T=a;try{var l=n(o,r),s=I.S;null!==s&&s(a,l),sa(e,t,l)}catch(u){ca(e,t,u)}finally{I.T=i}}else try{sa(e,t,i=n(o,r))}catch(c){ca(e,t,c)}}function sa(e,t,n){null!==n&&"object"==typeof n&&"function"==typeof n.then?n.then((function(n){ua(e,t,n)}),(function(n){return ca(e,t,n)})):ua(e,t,n)}function ua(e,t,n){t.status="fulfilled",t.value=n,fa(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,la(e,n)))}function ca(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,fa(t),t=t.next}while(t!==r)}e.action=null}function fa(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function da(e,t){return t}function pa(e,t){if(uo){var n=lu.formState;if(null!==n){e:{var r=Ei;if(uo){if(so){t:{for(var o=so,i=fo;8!==o.nodeType;){if(!i){o=null;break t}if(null===(o=xf(o.nextSibling))){o=null;break t}}o="F!"===(i=o.data)||"F"===i?o:null}if(o){so=xf(o.nextSibling),r="F!"===o.data;break e}}ho(r)}r=!1}r&&(t=n[0])}}return(n=$i()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:da,lastRenderedState:t},n.queue=r,n=$a.bind(null,Ei,r),r.dispatch=n,r=oa(!1),i=Wa.bind(null,Ei,!1,r.queue),o={state:t,dispatch:null,action:e,pending:null},(r=$i()).queue=o,n=aa.bind(null,Ei,o,i,n),o.dispatch=n,r.memoizedState=e,[t,n,!1]}function ha(e){return ma(Vi(),Ci,e)}function ma(e,t,n){if(t=Xi(e,t,da)[0],e=Yi(Ki)[0],"object"==typeof t&&null!==t&&"function"==typeof t.then)try{var r=Wi(t)}catch(a){if(a===Xo)throw Go;throw a}else r=t;var o=(t=Vi()).queue,i=o.dispatch;return n!==t.memoizedState&&(Ei.flags|=2048,va(9,{destroy:void 0,resource:void 0},ga.bind(null,o,n),null)),[r,i,e]}function ga(e,t){e.action=t}function ya(e){var t=Vi(),n=Ci;if(null!==n)return ma(t,n,e);Vi(),t=t.memoizedState;var r=(n=Vi()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function va(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=Ei.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},Ei.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function ba(){return Vi().memoizedState}function ka(e,t,n,r){var o=$i();r=void 0===r?null:r,Ei.flags|=e,o.memoizedState=va(1|t,{destroy:void 0,resource:void 0},n,r)}function wa(e,t,n,r){var o=Vi();r=void 0===r?null:r;var i=o.memoizedState.inst;null!==Ci&&null!==r&&zi(r,Ci.memoizedState.deps)?o.memoizedState=va(t,i,n,r):(Ei.flags|=e,o.memoizedState=va(1|t,i,n,r))}function xa(e,t){ka(8390656,8,e,t)}function Sa(e,t){wa(2048,8,e,t)}function Ea(e,t){return wa(4,2,e,t)}function Ca(e,t){return wa(4,4,e,t)}function _a(e,t){if("function"==typeof t){e=e();var n=t(e);return function(){"function"==typeof n?n():t(null)}}if(null!=t)return e=e(),t.current=e,function(){t.current=null}}function Ta(e,t,n){n=null!=n?n.concat([e]):null,wa(4,4,_a.bind(null,t,e),n)}function Pa(){}function Na(e,t){var n=Vi();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&zi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Oa(e,t){var n=Vi();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&zi(t,r[1]))return r[0];if(r=e(),Ni){ge(!0);try{e()}finally{ge(!1)}}return n.memoizedState=[r,t],r}function La(e,t,n){return void 0===n||1073741824&Si?e.memoizedState=t:(e.memoizedState=n,e=Fu(),Ei.lanes|=e,yu|=e,n)}function Ra(e,t,n,r){return Zn(n,t)?n:null!==vi.current?(e=La(e,n,r),Zn(e,t)||(Ll=!0),e):42&Si?(e=Fu(),Ei.lanes|=e,yu|=e,t):(Ll=!0,e.memoizedState=n)}function Aa(e,t,n,r,o){var i=F.p;F.p=0!==i&&8>i?i:8;var a,l,s,u=I.T,c={};I.T=c,Wa(e,!1,t,n);try{var f=o(),d=I.S;if(null!==d&&d(c,f),null!==f&&"object"==typeof f&&"function"==typeof f.then)Va(e,t,(a=r,l=[],s={status:"pending",value:null,reason:null,then:function(e){l.push(e)}},f.then((function(){s.status="fulfilled",s.value=a;for(var e=0;e<l.length;e++)(0,l[e])(a)}),(function(e){for(s.status="rejected",s.reason=e,e=0;e<l.length;e++)(0,l[e])(void 0)})),s),Iu());else Va(e,t,r,Iu())}catch(p){Va(e,t,{then:function(){},status:"rejected",reason:p},Iu())}finally{F.p=i,I.T=u}}function ja(){}function za(e,t,n,o){if(5!==e.tag)throw Error(r(476));var i=Da(e).queue;Aa(e,i,t,U,null===n?ja:function(){return Ma(e),n(o)})}function Da(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:U,baseState:U,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ki,lastRenderedState:U},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ki,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Ma(e){Va(e,Da(e).next.queue,{},Iu())}function Ia(){return Lo(Zf)}function Fa(){return Vi().memoizedState}function Ua(){return Vi().memoizedState}function Ba(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Iu(),r=ci(t,e=ui(n),n);return null!==r&&(Uu(r,t,n),fi(r,t,n)),t={cache:Io()},void(e.payload=t)}t=t.return}}function Ha(e,t,n){var r=Iu();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},qa(e)?Qa(t,n):null!==(n=jr(e,t,n,r))&&(Uu(n,e,r),Ka(n,t,r))}function $a(e,t,n){Va(e,t,n,Iu())}function Va(e,t,n,r){var o={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(qa(e))Qa(t,o);else{var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var a=t.lastRenderedState,l=i(a,n);if(o.hasEagerState=!0,o.eagerState=l,Zn(l,a))return Ar(e,t,o,0),null===lu&&Rr(),!1}catch(s){}if(null!==(n=jr(e,t,o,r)))return Uu(n,e,r),Ka(n,t,r),!0}return!1}function Wa(e,t,n,o){if(o={lane:2,revertLane:Ac(),action:o,hasEagerState:!1,eagerState:null,next:null},qa(e)){if(t)throw Error(r(479))}else null!==(t=jr(e,n,o,2))&&Uu(t,e,2)}function qa(e){var t=e.alternate;return e===Ei||null!==t&&t===Ei}function Qa(e,t){Pi=Ti=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ka(e,t,n){if(4194048&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Le(e,n)}}var Ya={readContext:Lo,use:qi,useCallback:ji,useContext:ji,useEffect:ji,useImperativeHandle:ji,useLayoutEffect:ji,useInsertionEffect:ji,useMemo:ji,useReducer:ji,useRef:ji,useState:ji,useDebugValue:ji,useDeferredValue:ji,useTransition:ji,useSyncExternalStore:ji,useId:ji,useHostTransitionStatus:ji,useFormState:ji,useActionState:ji,useOptimistic:ji,useMemoCache:ji,useCacheRefresh:ji},Xa={readContext:Lo,use:qi,useCallback:function(e,t){return $i().memoizedState=[e,void 0===t?null:t],e},useContext:Lo,useEffect:xa,useImperativeHandle:function(e,t,n){n=null!=n?n.concat([e]):null,ka(4194308,4,_a.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ka(4194308,4,e,t)},useInsertionEffect:function(e,t){ka(4,2,e,t)},useMemo:function(e,t){var n=$i();t=void 0===t?null:t;var r=e();if(Ni){ge(!0);try{e()}finally{ge(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=$i();if(void 0!==n){var o=n(t);if(Ni){ge(!0);try{n(t)}finally{ge(!1)}}}else o=t;return r.memoizedState=r.baseState=o,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:o},r.queue=e,e=e.dispatch=Ha.bind(null,Ei,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},$i().memoizedState=e},useState:function(e){var t=(e=oa(e)).queue,n=$a.bind(null,Ei,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Pa,useDeferredValue:function(e,t){return La($i(),e,t)},useTransition:function(){var e=oa(!1);return e=Aa.bind(null,Ei,e.queue,!0,!1),$i().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var o=Ei,i=$i();if(uo){if(void 0===n)throw Error(r(407));n=n()}else{if(n=t(),null===lu)throw Error(r(349));124&uu||Zi(o,t,n)}i.memoizedState=n;var a={value:n,getSnapshot:t};return i.queue=a,xa(ta.bind(null,o,a,e),[e]),o.flags|=2048,va(9,{destroy:void 0,resource:void 0},ea.bind(null,o,a,n,t),null),n},useId:function(){var e=$i(),t=lu.identifierPrefix;if(uo){var n=no;t="«"+t+"R"+(n=(to&~(1<<32-ye(to)-1)).toString(32)+n),0<(n=Oi++)&&(t+="H"+n.toString(32)),t+="»"}else t="«"+t+"r"+(n=Ai++).toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Ia,useFormState:pa,useActionState:pa,useOptimistic:function(e){var t=$i();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Wa.bind(null,Ei,!0,n),n.dispatch=t,[e,t]},useMemoCache:Qi,useCacheRefresh:function(){return $i().memoizedState=Ba.bind(null,Ei)}},Ja={readContext:Lo,use:qi,useCallback:Na,useContext:Lo,useEffect:Sa,useImperativeHandle:Ta,useInsertionEffect:Ea,useLayoutEffect:Ca,useMemo:Oa,useReducer:Yi,useRef:ba,useState:function(){return Yi(Ki)},useDebugValue:Pa,useDeferredValue:function(e,t){return Ra(Vi(),Ci.memoizedState,e,t)},useTransition:function(){var e=Yi(Ki)[0],t=Vi().memoizedState;return["boolean"==typeof e?e:Wi(e),t]},useSyncExternalStore:Gi,useId:Fa,useHostTransitionStatus:Ia,useFormState:ha,useActionState:ha,useOptimistic:function(e,t){return ia(Vi(),0,e,t)},useMemoCache:Qi,useCacheRefresh:Ua},Ga={readContext:Lo,use:qi,useCallback:Na,useContext:Lo,useEffect:Sa,useImperativeHandle:Ta,useInsertionEffect:Ea,useLayoutEffect:Ca,useMemo:Oa,useReducer:Ji,useRef:ba,useState:function(){return Ji(Ki)},useDebugValue:Pa,useDeferredValue:function(e,t){var n=Vi();return null===Ci?La(n,e,t):Ra(n,Ci.memoizedState,e,t)},useTransition:function(){var e=Ji(Ki)[0],t=Vi().memoizedState;return["boolean"==typeof e?e:Wi(e),t]},useSyncExternalStore:Gi,useId:Fa,useHostTransitionStatus:Ia,useFormState:ya,useActionState:ya,useOptimistic:function(e,t){var n=Vi();return null!==Ci?ia(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Qi,useCacheRefresh:Ua},Za=null,el=0;function tl(e){var t=el;return el+=1,null===Za&&(Za=[]),ni(Za,e,t)}function nl(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function rl(e,t){if(t.$$typeof===c)throw Error(r(525));throw e=Object.prototype.toString.call(t),Error(r(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ol(e){return(0,e._init)(e._payload)}function il(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function o(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function i(e,t){return(e=Hr(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=qr(n,e.mode,r)).return=e,t):((t=i(t,n)).return=e,t)}function u(e,t,n,r){var o=n.type;return o===h?f(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"==typeof o&&null!==o&&o.$$typeof===O&&ol(o)===t.type)?(nl(t=i(t,n.props),n),t.return=e,t):(nl(t=Vr(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Qr(n,e.mode,r)).return=e,t):((t=i(t,n.children||[])).return=e,t)}function f(e,t,n,r,o){return null===t||7!==t.tag?((t=Wr(n,e.mode,r,o)).return=e,t):((t=i(t,n)).return=e,t)}function m(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return(t=qr(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case d:return nl(n=Vr(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case p:return(t=Qr(t,e.mode,n)).return=e,t;case O:return m(e,t=(0,t._init)(t._payload),n)}if(M(t)||j(t))return(t=Wr(t,e.mode,n,null)).return=e,t;if("function"==typeof t.then)return m(e,tl(t),n);if(t.$$typeof===S)return m(e,Ro(e,t),n);rl(e,t)}return null}function g(e,t,n,r){var o=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return null!==o?null:s(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case d:return n.key===o?u(e,t,n,r):null;case p:return n.key===o?c(e,t,n,r):null;case O:return g(e,t,n=(o=n._init)(n._payload),r)}if(M(n)||j(n))return null!==o?null:f(e,t,n,r,null);if("function"==typeof n.then)return g(e,t,tl(n),r);if(n.$$typeof===S)return g(e,t,Ro(e,n),r);rl(e,n)}return null}function y(e,t,n,r,o){if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return s(t,e=e.get(n)||null,""+r,o);if("object"==typeof r&&null!==r){switch(r.$$typeof){case d:return u(t,e=e.get(null===r.key?n:r.key)||null,r,o);case p:return c(t,e=e.get(null===r.key?n:r.key)||null,r,o);case O:return y(e,t,n,r=(0,r._init)(r._payload),o)}if(M(r)||j(r))return f(t,e=e.get(n)||null,r,o,null);if("function"==typeof r.then)return y(e,t,n,tl(r),o);if(r.$$typeof===S)return y(e,t,n,Ro(t,r),o);rl(t,r)}return null}function v(s,u,c,f){if("object"==typeof c&&null!==c&&c.type===h&&null===c.key&&(c=c.props.children),"object"==typeof c&&null!==c){switch(c.$$typeof){case d:e:{for(var b=c.key;null!==u;){if(u.key===b){if((b=c.type)===h){if(7===u.tag){n(s,u.sibling),(f=i(u,c.props.children)).return=s,s=f;break e}}else if(u.elementType===b||"object"==typeof b&&null!==b&&b.$$typeof===O&&ol(b)===u.type){n(s,u.sibling),nl(f=i(u,c.props),c),f.return=s,s=f;break e}n(s,u);break}t(s,u),u=u.sibling}c.type===h?((f=Wr(c.props.children,s.mode,f,c.key)).return=s,s=f):(nl(f=Vr(c.type,c.key,c.props,null,s.mode,f),c),f.return=s,s=f)}return l(s);case p:e:{for(b=c.key;null!==u;){if(u.key===b){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(s,u.sibling),(f=i(u,c.children||[])).return=s,s=f;break e}n(s,u);break}t(s,u),u=u.sibling}(f=Qr(c,s.mode,f)).return=s,s=f}return l(s);case O:return v(s,u,c=(b=c._init)(c._payload),f)}if(M(c))return function(r,i,l,s){for(var u=null,c=null,f=i,d=i=0,p=null;null!==f&&d<l.length;d++){f.index>d?(p=f,f=null):p=f.sibling;var h=g(r,f,l[d],s);if(null===h){null===f&&(f=p);break}e&&f&&null===h.alternate&&t(r,f),i=a(h,i,d),null===c?u=h:c.sibling=h,c=h,f=p}if(d===l.length)return n(r,f),uo&&ro(r,d),u;if(null===f){for(;d<l.length;d++)null!==(f=m(r,l[d],s))&&(i=a(f,i,d),null===c?u=f:c.sibling=f,c=f);return uo&&ro(r,d),u}for(f=o(f);d<l.length;d++)null!==(p=y(f,r,d,l[d],s))&&(e&&null!==p.alternate&&f.delete(null===p.key?d:p.key),i=a(p,i,d),null===c?u=p:c.sibling=p,c=p);return e&&f.forEach((function(e){return t(r,e)})),uo&&ro(r,d),u}(s,u,c,f);if(j(c)){if("function"!=typeof(b=j(c)))throw Error(r(150));return function(i,l,s,u){if(null==s)throw Error(r(151));for(var c=null,f=null,d=l,p=l=0,h=null,v=s.next();null!==d&&!v.done;p++,v=s.next()){d.index>p?(h=d,d=null):h=d.sibling;var b=g(i,d,v.value,u);if(null===b){null===d&&(d=h);break}e&&d&&null===b.alternate&&t(i,d),l=a(b,l,p),null===f?c=b:f.sibling=b,f=b,d=h}if(v.done)return n(i,d),uo&&ro(i,p),c;if(null===d){for(;!v.done;p++,v=s.next())null!==(v=m(i,v.value,u))&&(l=a(v,l,p),null===f?c=v:f.sibling=v,f=v);return uo&&ro(i,p),c}for(d=o(d);!v.done;p++,v=s.next())null!==(v=y(d,i,p,v.value,u))&&(e&&null!==v.alternate&&d.delete(null===v.key?p:v.key),l=a(v,l,p),null===f?c=v:f.sibling=v,f=v);return e&&d.forEach((function(e){return t(i,e)})),uo&&ro(i,p),c}(s,u,c=b.call(c),f)}if("function"==typeof c.then)return v(s,u,tl(c),f);if(c.$$typeof===S)return v(s,u,Ro(s,c),f);rl(s,c)}return"string"==typeof c&&""!==c||"number"==typeof c||"bigint"==typeof c?(c=""+c,null!==u&&6===u.tag?(n(s,u.sibling),(f=i(u,c)).return=s,s=f):(n(s,u),(f=qr(c,s.mode,f)).return=s,s=f),l(s)):n(s,u)}return function(e,t,n,r){try{el=0;var o=v(e,t,n,r);return Za=null,o}catch(a){if(a===Xo||a===Go)throw a;var i=Ur(29,a,null,e.mode);return i.lanes=r,i.return=e,i}}}var al=il(!0),ll=il(!1),sl=$(null),ul=null;function cl(e){var t=e.alternate;W(hl,1&hl.current),W(sl,e),null===ul&&(null===t||null!==vi.current||null!==t.memoizedState)&&(ul=e)}function fl(e){if(22===e.tag){if(W(hl,hl.current),W(sl,e),null===ul){var t=e.alternate;null!==t&&null!==t.memoizedState&&(ul=e)}}else dl()}function dl(){W(hl,hl.current),W(sl,sl.current)}function pl(e){V(sl),ul===e&&(ul=null),V(hl)}var hl=$(0);function ml(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||wf(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function gl(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:u({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var yl={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Iu(),o=ui(r);o.payload=t,null!=n&&(o.callback=n),null!==(t=ci(e,o,r))&&(Uu(t,e,r),fi(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Iu(),o=ui(r);o.tag=1,o.payload=t,null!=n&&(o.callback=n),null!==(t=ci(e,o,r))&&(Uu(t,e,r),fi(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Iu(),r=ui(n);r.tag=2,null!=t&&(r.callback=t),null!==(t=ci(e,r,n))&&(Uu(t,e,n),fi(t,e,n))}};function vl(e,t,n,r,o,i,a){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,i,a):!t.prototype||!t.prototype.isPureReactComponent||(!er(n,r)||!er(o,i))}function bl(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&yl.enqueueReplaceState(t,t.state,null)}function kl(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var o in n===t&&(n=u({},n)),e)void 0===n[o]&&(n[o]=e[o]);return n}var wl="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e)};function xl(e){wl(e)}function Sl(e){}function El(e){wl(e)}function Cl(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout((function(){throw n}))}}function _l(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout((function(){throw r}))}}function Tl(e,t,n){return(n=ui(n)).tag=3,n.payload={element:null},n.callback=function(){Cl(e,t)},n}function Pl(e){return(e=ui(e)).tag=3,e}function Nl(e,t,n,r){var o=n.type.getDerivedStateFromError;if("function"==typeof o){var i=r.value;e.payload=function(){return o(i)},e.callback=function(){_l(t,n,r)}}var a=n.stateNode;null!==a&&"function"==typeof a.componentDidCatch&&(e.callback=function(){_l(t,n,r),"function"!=typeof o&&(null===Pu?Pu=new Set([this]):Pu.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Ol=Error(r(461)),Ll=!1;function Rl(e,t,n,r){t.child=null===e?ll(t,null,n,r):al(t,e.child,n,r)}function Al(e,t,n,r,o){n=n.render;var i=t.ref;if("ref"in r){var a={};for(var l in r)"ref"!==l&&(a[l]=r[l])}else a=r;return Oo(t),r=Di(e,t,n,a,i,o),l=Ui(),null===e||Ll?(uo&&l&&io(t),t.flags|=1,Rl(e,t,r,o),t.child):(Bi(e,t,o),Zl(e,t,o))}function jl(e,t,n,r,o){if(null===e){var i=n.type;return"function"!=typeof i||Br(i)||void 0!==i.defaultProps||null!==n.compare?((e=Vr(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,zl(e,t,i,r,o))}if(i=e.child,!es(e,o)){var a=i.memoizedProps;if((n=null!==(n=n.compare)?n:er)(a,r)&&e.ref===t.ref)return Zl(e,t,o)}return t.flags|=1,(e=Hr(i,r)).ref=t.ref,e.return=t,t.child=e}function zl(e,t,n,r,o){if(null!==e){var i=e.memoizedProps;if(er(i,r)&&e.ref===t.ref){if(Ll=!1,t.pendingProps=r=i,!es(e,o))return t.lanes=e.lanes,Zl(e,t,o);131072&e.flags&&(Ll=!0)}}return Fl(e,t,n,r,o)}function Dl(e,t,n){var r=t.pendingProps,o=r.children,i=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(128&t.flags){if(r=null!==i?i.baseLanes|n:n,null!==e){for(o=t.child=e.child,i=0;null!==o;)i=i|o.lanes|o.childLanes,o=o.sibling;t.childLanes=i&~r}else t.childLanes=0,t.child=null;return Ml(e,t,r,n)}if(!(536870912&n))return t.lanes=t.childLanes=536870912,Ml(e,t,null!==i?i.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Ko(0,null!==i?i.cachePool:null),null!==i?ki(t,i):wi(),fl(t)}else null!==i?(Ko(0,i.cachePool),ki(t,i),dl(),t.memoizedState=null):(null!==e&&Ko(0,null),wi(),dl());return Rl(e,t,o,n),t.child}function Ml(e,t,n,r){var o=Qo();return o=null===o?null:{parent:Mo._currentValue,pool:o},t.memoizedState={baseLanes:n,cachePool:o},null!==e&&Ko(0,null),wi(),fl(t),null!==e&&Po(e,t,r,!0),null}function Il(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!=typeof n&&"object"!=typeof n)throw Error(r(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Fl(e,t,n,r,o){return Oo(t),n=Di(e,t,n,r,void 0,o),r=Ui(),null===e||Ll?(uo&&r&&io(t),t.flags|=1,Rl(e,t,n,o),t.child):(Bi(e,t,o),Zl(e,t,o))}function Ul(e,t,n,r,o,i){return Oo(t),t.updateQueue=null,n=Ii(t,r,n,o),Mi(e),r=Ui(),null===e||Ll?(uo&&r&&io(t),t.flags|=1,Rl(e,t,n,i),t.child):(Bi(e,t,i),Zl(e,t,i))}function Bl(e,t,n,r,o){if(Oo(t),null===t.stateNode){var i=Ir,a=n.contextType;"object"==typeof a&&null!==a&&(i=Lo(a)),i=new n(r,i),t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,i.updater=yl,t.stateNode=i,i._reactInternals=t,(i=t.stateNode).props=r,i.state=t.memoizedState,i.refs={},li(t),a=n.contextType,i.context="object"==typeof a&&null!==a?Lo(a):Ir,i.state=t.memoizedState,"function"==typeof(a=n.getDerivedStateFromProps)&&(gl(t,n,a,r),i.state=t.memoizedState),"function"==typeof n.getDerivedStateFromProps||"function"==typeof i.getSnapshotBeforeUpdate||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||(a=i.state,"function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),a!==i.state&&yl.enqueueReplaceState(i,i.state,null),mi(t,r,i,o),hi(),i.state=t.memoizedState),"function"==typeof i.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){i=t.stateNode;var l=t.memoizedProps,s=kl(n,l);i.props=s;var u=i.context,c=n.contextType;a=Ir,"object"==typeof c&&null!==c&&(a=Lo(c));var f=n.getDerivedStateFromProps;c="function"==typeof f||"function"==typeof i.getSnapshotBeforeUpdate,l=t.pendingProps!==l,c||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(l||u!==a)&&bl(t,i,r,a),ai=!1;var d=t.memoizedState;i.state=d,mi(t,r,i,o),hi(),u=t.memoizedState,l||d!==u||ai?("function"==typeof f&&(gl(t,n,f,r),u=t.memoizedState),(s=ai||vl(t,n,s,r,d,u,a))?(c||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||("function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"==typeof i.componentDidMount&&(t.flags|=4194308)):("function"==typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),i.props=r,i.state=u,i.context=a,r=s):("function"==typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,si(e,t),c=kl(n,a=t.memoizedProps),i.props=c,f=t.pendingProps,d=i.context,u=n.contextType,s=Ir,"object"==typeof u&&null!==u&&(s=Lo(u)),(u="function"==typeof(l=n.getDerivedStateFromProps)||"function"==typeof i.getSnapshotBeforeUpdate)||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(a!==f||d!==s)&&bl(t,i,r,s),ai=!1,d=t.memoizedState,i.state=d,mi(t,r,i,o),hi();var p=t.memoizedState;a!==f||d!==p||ai||null!==e&&null!==e.dependencies&&No(e.dependencies)?("function"==typeof l&&(gl(t,n,l,r),p=t.memoizedState),(c=ai||vl(t,n,c,r,d,p,s)||null!==e&&null!==e.dependencies&&No(e.dependencies))?(u||"function"!=typeof i.UNSAFE_componentWillUpdate&&"function"!=typeof i.componentWillUpdate||("function"==typeof i.componentWillUpdate&&i.componentWillUpdate(r,p,s),"function"==typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,p,s)),"function"==typeof i.componentDidUpdate&&(t.flags|=4),"function"==typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof i.componentDidUpdate||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),i.props=r,i.state=p,i.context=s,r=c):("function"!=typeof i.componentDidUpdate||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return i=r,Il(e,t),r=!!(128&t.flags),i||r?(i=t.stateNode,n=r&&"function"!=typeof n.getDerivedStateFromError?null:i.render(),t.flags|=1,null!==e&&r?(t.child=al(t,e.child,null,o),t.child=al(t,null,n,o)):Rl(e,t,n,o),t.memoizedState=i.state,e=t.child):e=Zl(e,t,o),e}function Hl(e,t,n,r){return vo(),t.flags|=256,Rl(e,t,n,r),t.child}var $l={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Vl(e){return{baseLanes:e,cachePool:Yo()}}function Wl(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=ku),e}function ql(e,t,n){var o,i=t.pendingProps,a=!1,l=!!(128&t.flags);if((o=l)||(o=(null===e||null!==e.memoizedState)&&!!(2&hl.current)),o&&(a=!0,t.flags&=-129),o=!!(32&t.flags),t.flags&=-33,null===e){if(uo){if(a?cl(t):dl(),uo){var s,u=so;if(s=u){e:{for(s=u,u=fo;8!==s.nodeType;){if(!u){u=null;break e}if(null===(s=xf(s.nextSibling))){u=null;break e}}u=s}null!==u?(t.memoizedState={dehydrated:u,treeContext:null!==eo?{id:to,overflow:no}:null,retryLane:536870912,hydrationErrors:null},(s=Ur(18,null,null,0)).stateNode=u,s.return=t,t.child=s,lo=t,so=null,s=!0):s=!1}s||ho(t)}if(null!==(u=t.memoizedState)&&null!==(u=u.dehydrated))return wf(u)?t.lanes=32:t.lanes=536870912,null;pl(t)}return u=i.children,i=i.fallback,a?(dl(),u=Kl({mode:"hidden",children:u},a=t.mode),i=Wr(i,a,n,null),u.return=t,i.return=t,u.sibling=i,t.child=u,(a=t.child).memoizedState=Vl(n),a.childLanes=Wl(e,o,n),t.memoizedState=$l,i):(cl(t),Ql(t,u))}if(null!==(s=e.memoizedState)&&null!==(u=s.dehydrated)){if(l)256&t.flags?(cl(t),t.flags&=-257,t=Yl(e,t,n)):null!==t.memoizedState?(dl(),t.child=e.child,t.flags|=128,t=null):(dl(),a=i.fallback,u=t.mode,i=Kl({mode:"visible",children:i.children},u),(a=Wr(a,u,n,null)).flags|=2,i.return=t,a.return=t,i.sibling=a,t.child=i,al(t,e.child,null,n),(i=t.child).memoizedState=Vl(n),i.childLanes=Wl(e,o,n),t.memoizedState=$l,t=a);else if(cl(t),wf(u)){if(o=u.nextSibling&&u.nextSibling.dataset)var c=o.dgst;o=c,(i=Error(r(419))).stack="",i.digest=o,ko({value:i,source:null,stack:null}),t=Yl(e,t,n)}else if(Ll||Po(e,t,n,!1),o=0!==(n&e.childLanes),Ll||o){if(null!==(o=lu)&&(0!==(i=0!==((i=42&(i=n&-n)?1:Re(i))&(o.suspendedLanes|n))?0:i)&&i!==s.retryLane))throw s.retryLane=i,zr(e,i),Uu(o,e,i),Ol;"$?"===u.data||Ju(),t=Yl(e,t,n)}else"$?"===u.data?(t.flags|=192,t.child=e.child,t=null):(e=s.treeContext,so=xf(u.nextSibling),lo=t,uo=!0,co=null,fo=!1,null!==e&&(Gr[Zr++]=to,Gr[Zr++]=no,Gr[Zr++]=eo,to=e.id,no=e.overflow,eo=t),(t=Ql(t,i.children)).flags|=4096);return t}return a?(dl(),a=i.fallback,u=t.mode,c=(s=e.child).sibling,(i=Hr(s,{mode:"hidden",children:i.children})).subtreeFlags=65011712&s.subtreeFlags,null!==c?a=Hr(c,a):(a=Wr(a,u,n,null)).flags|=2,a.return=t,i.return=t,i.sibling=a,t.child=i,i=a,a=t.child,null===(u=e.child.memoizedState)?u=Vl(n):(null!==(s=u.cachePool)?(c=Mo._currentValue,s=s.parent!==c?{parent:c,pool:c}:s):s=Yo(),u={baseLanes:u.baseLanes|n,cachePool:s}),a.memoizedState=u,a.childLanes=Wl(e,o,n),t.memoizedState=$l,i):(cl(t),e=(n=e.child).sibling,(n=Hr(n,{mode:"visible",children:i.children})).return=t,n.sibling=null,null!==e&&(null===(o=t.deletions)?(t.deletions=[e],t.flags|=16):o.push(e)),t.child=n,t.memoizedState=null,n)}function Ql(e,t){return(t=Kl({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Kl(e,t){return(e=Ur(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Yl(e,t,n){return al(t,e.child,null,n),(e=Ql(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Xl(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),_o(e.return,t,n)}function Jl(e,t,n,r,o){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Gl(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Rl(e,t,r.children,n),2&(r=hl.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Xl(e,n,t);else if(19===e.tag)Xl(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(W(hl,r),o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===ml(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Jl(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===ml(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Jl(t,!0,n,null,i);break;case"together":Jl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Zl(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),yu|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Po(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(r(153));if(null!==t.child){for(n=Hr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Hr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function es(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!No(e))}function ts(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Ll=!0;else{if(!(es(e,n)||128&t.flags))return Ll=!1,function(e,t,n){switch(t.tag){case 3:X(t,t.stateNode.containerInfo),Eo(0,Mo,e.memoizedState.cache),vo();break;case 27:case 5:G(t);break;case 4:X(t,t.stateNode.containerInfo);break;case 10:Eo(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(cl(t),t.flags|=128,null):0!==(n&t.child.childLanes)?ql(e,t,n):(cl(t),null!==(e=Zl(e,t,n))?e.sibling:null);cl(t);break;case 19:var o=!!(128&e.flags);if((r=0!==(n&t.childLanes))||(Po(e,t,n,!1),r=0!==(n&t.childLanes)),o){if(r)return Gl(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),W(hl,hl.current),r)break;return null;case 22:case 23:return t.lanes=0,Dl(e,t,n);case 24:Eo(0,Mo,e.memoizedState.cache)}return Zl(e,t,n)}(e,t,n);Ll=!!(131072&e.flags)}else Ll=!1,uo&&1048576&t.flags&&oo(t,Jr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var o=t.elementType,i=o._init;if(o=i(o._payload),t.type=o,"function"!=typeof o){if(null!=o){if((i=o.$$typeof)===C){t.tag=11,t=Al(null,t,o,e,n);break e}if(i===N){t.tag=14,t=jl(null,t,o,e,n);break e}}throw t=D(o)||o,Error(r(306,t,""))}Br(o)?(e=kl(o,e),t.tag=1,t=Bl(null,t,o,e,n)):(t.tag=0,t=Fl(null,t,o,e,n))}return t;case 0:return Fl(e,t,t.type,t.pendingProps,n);case 1:return Bl(e,t,o=t.type,i=kl(o,t.pendingProps),n);case 3:e:{if(X(t,t.stateNode.containerInfo),null===e)throw Error(r(387));o=t.pendingProps;var a=t.memoizedState;i=a.element,si(e,t),mi(t,o,null,n);var l=t.memoizedState;if(o=l.cache,Eo(0,Mo,o),o!==a.cache&&To(t,[Mo],n,!0),hi(),o=l.element,a.isDehydrated){if(a={element:o,isDehydrated:!1,cache:l.cache},t.updateQueue.baseState=a,t.memoizedState=a,256&t.flags){t=Hl(e,t,o,n);break e}if(o!==i){ko(i=Pr(Error(r(424)),t)),t=Hl(e,t,o,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(so=xf(e.firstChild),lo=t,uo=!0,co=null,fo=!0,n=ll(t,null,o,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(vo(),o===i){t=Zl(e,t,n);break e}Rl(e,t,o,n)}t=t.child}return t;case 26:return Il(e,t),null===e?(n=Af(t.type,null,t.pendingProps,null))?t.memoizedState=n:uo||(n=t.type,e=t.pendingProps,(o=sf(K.current).createElement(n))[De]=t,o[Me]=e,of(o,n,e),Ye(o),t.stateNode=o):t.memoizedState=Af(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return G(t),null===e&&uo&&(o=t.stateNode=Cf(t.type,t.pendingProps,K.current),lo=t,fo=!0,i=so,vf(t.type)?(Sf=i,so=xf(o.firstChild)):so=i),Rl(e,t,t.pendingProps.children,n),Il(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&uo&&((i=o=so)&&(null!==(o=function(e,t,n,r){for(;1===e.nodeType;){var o=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[$e])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(i=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(i!==o.rel||e.getAttribute("href")!==(null==o.href||""===o.href?null:o.href)||e.getAttribute("crossorigin")!==(null==o.crossOrigin?null:o.crossOrigin)||e.getAttribute("title")!==(null==o.title?null:o.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((i=e.getAttribute("src"))!==(null==o.src?null:o.src)||e.getAttribute("type")!==(null==o.type?null:o.type)||e.getAttribute("crossorigin")!==(null==o.crossOrigin?null:o.crossOrigin))&&i&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var i=null==o.name?null:""+o.name;if("hidden"===o.type&&e.getAttribute("name")===i)return e}if(null===(e=xf(e.nextSibling)))break}return null}(o,t.type,t.pendingProps,fo))?(t.stateNode=o,lo=t,so=xf(o.firstChild),fo=!1,i=!0):i=!1),i||ho(t)),G(t),i=t.type,a=t.pendingProps,l=null!==e?e.memoizedProps:null,o=a.children,ff(i,a)?o=null:null!==l&&ff(i,l)&&(t.flags|=32),null!==t.memoizedState&&(i=Di(e,t,Fi,null,null,n),Zf._currentValue=i),Il(e,t),Rl(e,t,o,n),t.child;case 6:return null===e&&uo&&((e=n=so)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=xf(e.nextSibling)))return null}return e}(n,t.pendingProps,fo))?(t.stateNode=n,lo=t,so=null,e=!0):e=!1),e||ho(t)),null;case 13:return ql(e,t,n);case 4:return X(t,t.stateNode.containerInfo),o=t.pendingProps,null===e?t.child=al(t,null,o,n):Rl(e,t,o,n),t.child;case 11:return Al(e,t,t.type,t.pendingProps,n);case 7:return Rl(e,t,t.pendingProps,n),t.child;case 8:case 12:return Rl(e,t,t.pendingProps.children,n),t.child;case 10:return o=t.pendingProps,Eo(0,t.type,o.value),Rl(e,t,o.children,n),t.child;case 9:return i=t.type._context,o=t.pendingProps.children,Oo(t),o=o(i=Lo(i)),t.flags|=1,Rl(e,t,o,n),t.child;case 14:return jl(e,t,t.type,t.pendingProps,n);case 15:return zl(e,t,t.type,t.pendingProps,n);case 19:return Gl(e,t,n);case 31:return o=t.pendingProps,n=t.mode,o={mode:o.mode,children:o.children},null===e?((n=Kl(o,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Hr(e.child,o)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Dl(e,t,n);case 24:return Oo(t),o=Lo(Mo),null===e?(null===(i=Qo())&&(i=lu,a=Io(),i.pooledCache=a,a.refCount++,null!==a&&(i.pooledCacheLanes|=n),i=a),t.memoizedState={parent:o,cache:i},li(t),Eo(0,Mo,i)):(0!==(e.lanes&n)&&(si(e,t),mi(t,null,null,n),hi()),i=e.memoizedState,a=t.memoizedState,i.parent!==o?(i={parent:o,cache:o},t.memoizedState=i,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=i),Eo(0,Mo,o)):(o=a.cache,Eo(0,Mo,o),o!==i.cache&&To(t,[Mo],n,!0))),Rl(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(r(156,t.tag))}function ns(e){e.flags|=4}function rs(e,t){if("stylesheet"!==t.type||4&t.state.loading)e.flags&=-16777217;else if(e.flags|=16777216,!qf(t)){if(null!==(t=sl.current)&&((4194048&uu)===uu?null!==ul:(62914560&uu)!==uu&&!(536870912&uu)||t!==ul))throw ri=Zo,Jo;e.flags|=8192}}function os(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Te():536870912,e.lanes|=t,wu|=t)}function is(e,t){if(!uo)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function as(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=65011712&o.subtreeFlags,r|=65011712&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ls(e,t,n){var o=t.pendingProps;switch(ao(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return as(t),null;case 3:return n=t.stateNode,o=null,null!==e&&(o=e.memoizedState.cache),t.memoizedState.cache!==o&&(t.flags|=2048),Co(Mo),J(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(yo(t)?ns(t):null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,bo())),as(t),null;case 26:return n=t.memoizedState,null===e?(ns(t),null!==n?(as(t),rs(t,n)):(as(t),t.flags&=-16777217)):n?n!==e.memoizedState?(ns(t),as(t),rs(t,n)):(as(t),t.flags&=-16777217):(e.memoizedProps!==o&&ns(t),as(t),t.flags&=-16777217),null;case 27:Z(t),n=K.current;var i=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==o&&ns(t);else{if(!o){if(null===t.stateNode)throw Error(r(166));return as(t),null}e=q.current,yo(t)?mo(t):(e=Cf(i,o,n),t.stateNode=e,ns(t))}return as(t),null;case 5:if(Z(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==o&&ns(t);else{if(!o){if(null===t.stateNode)throw Error(r(166));return as(t),null}if(e=q.current,yo(t))mo(t);else{switch(i=sf(K.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=i.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"==typeof o.is?i.createElement("select",{is:o.is}):i.createElement("select"),o.multiple?e.multiple=!0:o.size&&(e.size=o.size);break;default:e="string"==typeof o.is?i.createElement(n,{is:o.is}):i.createElement(n)}}e[De]=t,e[Me]=o;e:for(i=t.child;null!==i;){if(5===i.tag||6===i.tag)e.appendChild(i.stateNode);else if(4!==i.tag&&27!==i.tag&&null!==i.child){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;null===i.sibling;){if(null===i.return||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(of(e,n,o),n){case"button":case"input":case"select":case"textarea":e=!!o.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&ns(t)}}return as(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==o&&ns(t);else{if("string"!=typeof o&&null===t.stateNode)throw Error(r(166));if(e=K.current,yo(t)){if(e=t.stateNode,n=t.memoizedProps,o=null,null!==(i=lo))switch(i.tag){case 27:case 5:o=i.memoizedProps}e[De]=t,(e=!!(e.nodeValue===n||null!==o&&!0===o.suppressHydrationWarning||ef(e.nodeValue,n)))||ho(t)}else(e=sf(e).createTextNode(o))[De]=t,t.stateNode=e}return as(t),null;case 13:if(o=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(i=yo(t),null!==o&&null!==o.dehydrated){if(null===e){if(!i)throw Error(r(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(r(317));i[De]=t}else vo(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;as(t),i=!1}else i=bo(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return 256&t.flags?(pl(t),t):(pl(t),null)}if(pl(t),128&t.flags)return t.lanes=n,t;if(n=null!==o,e=null!==e&&null!==e.memoizedState,n){i=null,null!==(o=t.child).alternate&&null!==o.alternate.memoizedState&&null!==o.alternate.memoizedState.cachePool&&(i=o.alternate.memoizedState.cachePool.pool);var a=null;null!==o.memoizedState&&null!==o.memoizedState.cachePool&&(a=o.memoizedState.cachePool.pool),a!==i&&(o.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),os(t,t.updateQueue),as(t),null;case 4:return J(),null===e&&Vc(t.stateNode.containerInfo),as(t),null;case 10:return Co(t.type),as(t),null;case 19:if(V(hl),null===(i=t.memoizedState))return as(t),null;if(o=!!(128&t.flags),null===(a=i.rendering))if(o)is(i,!1);else{if(0!==gu||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(a=ml(e))){for(t.flags|=128,is(i,!1),e=a.updateQueue,t.updateQueue=e,os(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)$r(n,e),n=n.sibling;return W(hl,1&hl.current|2),t.child}e=e.sibling}null!==i.tail&&ie()>_u&&(t.flags|=128,o=!0,is(i,!1),t.lanes=4194304)}else{if(!o)if(null!==(e=ml(a))){if(t.flags|=128,o=!0,e=e.updateQueue,t.updateQueue=e,os(t,e),is(i,!0),null===i.tail&&"hidden"===i.tailMode&&!a.alternate&&!uo)return as(t),null}else 2*ie()-i.renderingStartTime>_u&&536870912!==n&&(t.flags|=128,o=!0,is(i,!1),t.lanes=4194304);i.isBackwards?(a.sibling=t.child,t.child=a):(null!==(e=i.last)?e.sibling=a:t.child=a,i.last=a)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ie(),t.sibling=null,e=hl.current,W(hl,o?1&e|2:1&e),t):(as(t),null);case 22:case 23:return pl(t),xi(),o=null!==t.memoizedState,null!==e?null!==e.memoizedState!==o&&(t.flags|=8192):o&&(t.flags|=8192),o?!!(536870912&n)&&!(128&t.flags)&&(as(t),6&t.subtreeFlags&&(t.flags|=8192)):as(t),null!==(n=t.updateQueue)&&os(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),o=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(o=t.memoizedState.cachePool.pool),o!==n&&(t.flags|=2048),null!==e&&V(qo),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),Co(Mo),as(t),null;case 25:case 30:return null}throw Error(r(156,t.tag))}function ss(e,t){switch(ao(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Co(Mo),J(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return Z(t),null;case 13:if(pl(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(r(340));vo()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return V(hl),null;case 4:return J(),null;case 10:return Co(t.type),null;case 22:case 23:return pl(t),xi(),null!==e&&V(qo),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return Co(Mo),null;default:return null}}function us(e,t){switch(ao(t),t.tag){case 3:Co(Mo),J();break;case 26:case 27:case 5:Z(t);break;case 4:J();break;case 13:pl(t);break;case 19:V(hl);break;case 10:Co(t.type);break;case 22:case 23:pl(t),xi(),null!==e&&V(qo);break;case 24:Co(Mo)}}function cs(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var o=r.next;n=o;do{if((n.tag&e)===e){r=void 0;var i=n.create,a=n.inst;r=i(),a.destroy=r}n=n.next}while(n!==o)}}catch(l){hc(t,t.return,l)}}function fs(e,t,n){try{var r=t.updateQueue,o=null!==r?r.lastEffect:null;if(null!==o){var i=o.next;r=i;do{if((r.tag&e)===e){var a=r.inst,l=a.destroy;if(void 0!==l){a.destroy=void 0,o=t;var s=n,u=l;try{u()}catch(c){hc(o,s,c)}}}r=r.next}while(r!==i)}}catch(c){hc(t,t.return,c)}}function ds(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{yi(t,n)}catch(r){hc(e,e.return,r)}}}function ps(e,t,n){n.props=kl(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){hc(e,t,r)}}function hs(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"==typeof n?e.refCleanup=n(r):n.current=r}}catch(o){hc(e,t,o)}}function ms(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"==typeof r)try{r()}catch(o){hc(e,t,o)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof n)try{n(null)}catch(i){hc(e,t,i)}else n.current=null}function gs(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(o){hc(e,e.return,o)}}function ys(e,t,n){try{var o=e.stateNode;!function(e,t,n,o){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,a=null,l=null,s=null,u=null,c=null,f=null;for(h in n){var d=n[h];if(n.hasOwnProperty(h)&&null!=d)switch(h){case"checked":case"value":break;case"defaultValue":u=d;default:o.hasOwnProperty(h)||nf(e,t,h,null,o,d)}}for(var p in o){var h=o[p];if(d=n[p],o.hasOwnProperty(p)&&(null!=h||null!=d))switch(p){case"type":a=h;break;case"name":i=h;break;case"checked":c=h;break;case"defaultChecked":f=h;break;case"value":l=h;break;case"defaultValue":s=h;break;case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(r(137,t));break;default:h!==d&&nf(e,t,p,h,o,d)}}return void kt(e,l,s,u,c,f,a,i);case"select":for(a in h=l=s=p=null,n)if(u=n[a],n.hasOwnProperty(a)&&null!=u)switch(a){case"value":break;case"multiple":h=u;default:o.hasOwnProperty(a)||nf(e,t,a,null,o,u)}for(i in o)if(a=o[i],u=n[i],o.hasOwnProperty(i)&&(null!=a||null!=u))switch(i){case"value":p=a;break;case"defaultValue":s=a;break;case"multiple":l=a;default:a!==u&&nf(e,t,i,a,o,u)}return t=s,n=l,o=h,void(null!=p?St(e,!!n,p,!1):!!o!=!!n&&(null!=t?St(e,!!n,t,!0):St(e,!!n,n?[]:"",!1)));case"textarea":for(s in h=p=null,n)if(i=n[s],n.hasOwnProperty(s)&&null!=i&&!o.hasOwnProperty(s))switch(s){case"value":case"children":break;default:nf(e,t,s,null,o,i)}for(l in o)if(i=o[l],a=n[l],o.hasOwnProperty(l)&&(null!=i||null!=a))switch(l){case"value":p=i;break;case"defaultValue":h=i;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=i)throw Error(r(91));break;default:i!==a&&nf(e,t,l,i,o,a)}return void Et(e,p,h);case"option":for(var m in n)if(p=n[m],n.hasOwnProperty(m)&&null!=p&&!o.hasOwnProperty(m))if("selected"===m)e.selected=!1;else nf(e,t,m,null,o,p);for(u in o)if(p=o[u],h=n[u],o.hasOwnProperty(u)&&p!==h&&(null!=p||null!=h))if("selected"===u)e.selected=p&&"function"!=typeof p&&"symbol"!=typeof p;else nf(e,t,u,p,o,h);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)p=n[g],n.hasOwnProperty(g)&&null!=p&&!o.hasOwnProperty(g)&&nf(e,t,g,null,o,p);for(c in o)if(p=o[c],h=n[c],o.hasOwnProperty(c)&&p!==h&&(null!=p||null!=h))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(r(137,t));break;default:nf(e,t,c,p,o,h)}return;default:if(Ot(t)){for(var y in n)p=n[y],n.hasOwnProperty(y)&&void 0!==p&&!o.hasOwnProperty(y)&&rf(e,t,y,void 0,o,p);for(f in o)p=o[f],h=n[f],!o.hasOwnProperty(f)||p===h||void 0===p&&void 0===h||rf(e,t,f,p,o,h);return}}for(var v in n)p=n[v],n.hasOwnProperty(v)&&null!=p&&!o.hasOwnProperty(v)&&nf(e,t,v,null,o,p);for(d in o)p=o[d],h=n[d],!o.hasOwnProperty(d)||p===h||null==p&&null==h||nf(e,t,d,p,o,h)}(o,e.type,n,t),o[Me]=t}catch(i){hc(e,e.return,i)}}function vs(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&vf(e.type)||4===e.tag}function bs(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||vs(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&vf(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ks(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=tf));else if(4!==r&&(27===r&&vf(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(ks(e,t,n),e=e.sibling;null!==e;)ks(e,t,n),e=e.sibling}function ws(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&vf(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(ws(e,t,n),e=e.sibling;null!==e;)ws(e,t,n),e=e.sibling}function xs(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,o=t.attributes;o.length;)t.removeAttributeNode(o[0]);of(t,r,n),t[De]=e,t[Me]=n}catch(i){hc(e,e.return,i)}}var Ss=!1,Es=!1,Cs=!1,_s="function"==typeof WeakSet?WeakSet:Set,Ts=null;function Ps(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Bs(e,n),4&r&&cs(5,n);break;case 1:if(Bs(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(a){hc(n,n.return,a)}else{var o=kl(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(o,t,e.__reactInternalSnapshotBeforeUpdate)}catch(l){hc(n,n.return,l)}}64&r&&ds(n),512&r&&hs(n,n.return);break;case 3:if(Bs(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{yi(e,t)}catch(a){hc(n,n.return,a)}}break;case 27:null===t&&4&r&&xs(n);case 26:case 5:Bs(e,n),null===t&&4&r&&gs(n),512&r&&hs(n,n.return);break;case 12:Bs(e,n);break;case 13:Bs(e,n),4&r&&js(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=vc.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||Ss)){t=null!==t&&null!==t.memoizedState||Es,o=Ss;var i=Es;Ss=r,(Es=t)&&!i?$s(e,n,!!(8772&n.subtreeFlags)):Bs(e,n),Ss=o,Es=i}break;case 30:break;default:Bs(e,n)}}function Ns(e){var t=e.alternate;null!==t&&(e.alternate=null,Ns(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Ve(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Os=null,Ls=!1;function Rs(e,t,n){for(n=n.child;null!==n;)As(e,t,n),n=n.sibling}function As(e,t,n){if(me&&"function"==typeof me.onCommitFiberUnmount)try{me.onCommitFiberUnmount(he,n)}catch(i){}switch(n.tag){case 26:Es||ms(n,t),Rs(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:Es||ms(n,t);var r=Os,o=Ls;vf(n.type)&&(Os=n.stateNode,Ls=!1),Rs(e,t,n),_f(n.stateNode),Os=r,Ls=o;break;case 5:Es||ms(n,t);case 6:if(r=Os,o=Ls,Os=null,Rs(e,t,n),Ls=o,null!==(Os=r))if(Ls)try{(9===Os.nodeType?Os.body:"HTML"===Os.nodeName?Os.ownerDocument.body:Os).removeChild(n.stateNode)}catch(a){hc(n,t,a)}else try{Os.removeChild(n.stateNode)}catch(a){hc(n,t,a)}break;case 18:null!==Os&&(Ls?(bf(9===(e=Os).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Rd(e)):bf(Os,n.stateNode));break;case 4:r=Os,o=Ls,Os=n.stateNode.containerInfo,Ls=!0,Rs(e,t,n),Os=r,Ls=o;break;case 0:case 11:case 14:case 15:Es||fs(2,n,t),Es||fs(4,n,t),Rs(e,t,n);break;case 1:Es||(ms(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount&&ps(n,t,r)),Rs(e,t,n);break;case 21:Rs(e,t,n);break;case 22:Es=(r=Es)||null!==n.memoizedState,Rs(e,t,n),Es=r;break;default:Rs(e,t,n)}}function js(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Rd(e)}catch(n){hc(t,t.return,n)}}function zs(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new _s),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new _s),t;default:throw Error(r(435,e.tag))}}(e);t.forEach((function(t){var r=bc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}function Ds(e,t){var n=t.deletions;if(null!==n)for(var o=0;o<n.length;o++){var i=n[o],a=e,l=t,s=l;e:for(;null!==s;){switch(s.tag){case 27:if(vf(s.type)){Os=s.stateNode,Ls=!1;break e}break;case 5:Os=s.stateNode,Ls=!1;break e;case 3:case 4:Os=s.stateNode.containerInfo,Ls=!0;break e}s=s.return}if(null===Os)throw Error(r(160));As(a,l,i),Os=null,Ls=!1,null!==(a=i.alternate)&&(a.return=null),i.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Is(t,e),t=t.sibling}var Ms=null;function Is(e,t){var n=e.alternate,o=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ds(t,e),Fs(e),4&o&&(fs(3,e,e.return),cs(3,e),fs(5,e,e.return));break;case 1:Ds(t,e),Fs(e),512&o&&(Es||null===n||ms(n,n.return)),64&o&&Ss&&(null!==(e=e.updateQueue)&&(null!==(o=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?o:n.concat(o))));break;case 26:var i=Ms;if(Ds(t,e),Fs(e),512&o&&(Es||null===n||ms(n,n.return)),4&o){var a=null!==n?n.memoizedState:null;if(o=e.memoizedState,null===n)if(null===o)if(null===e.stateNode){e:{o=e.type,n=e.memoizedProps,i=i.ownerDocument||i;t:switch(o){case"title":(!(a=i.getElementsByTagName("title")[0])||a[$e]||a[De]||"http://www.w3.org/2000/svg"===a.namespaceURI||a.hasAttribute("itemprop"))&&(a=i.createElement(o),i.head.insertBefore(a,i.querySelector("head > title"))),of(a,o,n),a[De]=e,Ye(a),o=a;break e;case"link":var l=Vf("link","href",i).get(o+(n.href||""));if(l)for(var s=0;s<l.length;s++)if((a=l[s]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&a.getAttribute("rel")===(null==n.rel?null:n.rel)&&a.getAttribute("title")===(null==n.title?null:n.title)&&a.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){l.splice(s,1);break t}of(a=i.createElement(o),o,n),i.head.appendChild(a);break;case"meta":if(l=Vf("meta","content",i).get(o+(n.content||"")))for(s=0;s<l.length;s++)if((a=l[s]).getAttribute("content")===(null==n.content?null:""+n.content)&&a.getAttribute("name")===(null==n.name?null:n.name)&&a.getAttribute("property")===(null==n.property?null:n.property)&&a.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&a.getAttribute("charset")===(null==n.charSet?null:n.charSet)){l.splice(s,1);break t}of(a=i.createElement(o),o,n),i.head.appendChild(a);break;default:throw Error(r(468,o))}a[De]=e,Ye(a),o=a}e.stateNode=o}else Wf(i,e.type,e.stateNode);else e.stateNode=Ff(i,o,e.memoizedProps);else a!==o?(null===a?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):a.count--,null===o?Wf(i,e.type,e.stateNode):Ff(i,o,e.memoizedProps)):null===o&&null!==e.stateNode&&ys(e,e.memoizedProps,n.memoizedProps)}break;case 27:Ds(t,e),Fs(e),512&o&&(Es||null===n||ms(n,n.return)),null!==n&&4&o&&ys(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Ds(t,e),Fs(e),512&o&&(Es||null===n||ms(n,n.return)),32&e.flags){i=e.stateNode;try{_t(i,"")}catch(h){hc(e,e.return,h)}}4&o&&null!=e.stateNode&&ys(e,i=e.memoizedProps,null!==n?n.memoizedProps:i),1024&o&&(Cs=!0);break;case 6:if(Ds(t,e),Fs(e),4&o){if(null===e.stateNode)throw Error(r(162));o=e.memoizedProps,n=e.stateNode;try{n.nodeValue=o}catch(h){hc(e,e.return,h)}}break;case 3:if($f=null,i=Ms,Ms=Nf(t.containerInfo),Ds(t,e),Ms=i,Fs(e),4&o&&null!==n&&n.memoizedState.isDehydrated)try{Rd(t.containerInfo)}catch(h){hc(e,e.return,h)}Cs&&(Cs=!1,Us(e));break;case 4:o=Ms,Ms=Nf(e.stateNode.containerInfo),Ds(t,e),Fs(e),Ms=o;break;case 12:default:Ds(t,e),Fs(e);break;case 13:Ds(t,e),Fs(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==n&&null!==n.memoizedState)&&(Cu=ie()),4&o&&(null!==(o=e.updateQueue)&&(e.updateQueue=null,zs(e,o)));break;case 22:i=null!==e.memoizedState;var u=null!==n&&null!==n.memoizedState,c=Ss,f=Es;if(Ss=c||i,Es=f||u,Ds(t,e),Es=f,Ss=c,Fs(e),8192&o)e:for(t=e.stateNode,t._visibility=i?-2&t._visibility:1|t._visibility,i&&(null===n||u||Ss||Es||Hs(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){u=n=t;try{if(a=u.stateNode,i)"function"==typeof(l=a.style).setProperty?l.setProperty("display","none","important"):l.display="none";else{s=u.stateNode;var d=u.memoizedProps.style,p=null!=d&&d.hasOwnProperty("display")?d.display:null;s.style.display=null==p||"boolean"==typeof p?"":(""+p).trim()}}catch(h){hc(u,u.return,h)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=i?"":u.memoizedProps}catch(h){hc(u,u.return,h)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&o&&(null!==(o=e.updateQueue)&&(null!==(n=o.retryQueue)&&(o.retryQueue=null,zs(e,n))));break;case 19:Ds(t,e),Fs(e),4&o&&(null!==(o=e.updateQueue)&&(e.updateQueue=null,zs(e,o)));case 30:case 21:}}function Fs(e){var t=e.flags;if(2&t){try{for(var n,o=e.return;null!==o;){if(vs(o)){n=o;break}o=o.return}if(null==n)throw Error(r(160));switch(n.tag){case 27:var i=n.stateNode;ws(e,bs(e),i);break;case 5:var a=n.stateNode;32&n.flags&&(_t(a,""),n.flags&=-33),ws(e,bs(e),a);break;case 3:case 4:var l=n.stateNode.containerInfo;ks(e,bs(e),l);break;default:throw Error(r(161))}}catch(s){hc(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Us(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Us(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Bs(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)Ps(e,t.alternate,t),t=t.sibling}function Hs(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:fs(4,t,t.return),Hs(t);break;case 1:ms(t,t.return);var n=t.stateNode;"function"==typeof n.componentWillUnmount&&ps(t,t.return,n),Hs(t);break;case 27:_f(t.stateNode);case 26:case 5:ms(t,t.return),Hs(t);break;case 22:null===t.memoizedState&&Hs(t);break;default:Hs(t)}e=e.sibling}}function $s(e,t,n){for(n=n&&!!(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,o=e,i=t,a=i.flags;switch(i.tag){case 0:case 11:case 15:$s(o,i,n),cs(4,i);break;case 1:if($s(o,i,n),"function"==typeof(o=(r=i).stateNode).componentDidMount)try{o.componentDidMount()}catch(u){hc(r,r.return,u)}if(null!==(o=(r=i).updateQueue)){var l=r.stateNode;try{var s=o.shared.hiddenCallbacks;if(null!==s)for(o.shared.hiddenCallbacks=null,o=0;o<s.length;o++)gi(s[o],l)}catch(u){hc(r,r.return,u)}}n&&64&a&&ds(i),hs(i,i.return);break;case 27:xs(i);case 26:case 5:$s(o,i,n),n&&null===r&&4&a&&gs(i),hs(i,i.return);break;case 12:$s(o,i,n);break;case 13:$s(o,i,n),n&&4&a&&js(o,i);break;case 22:null===i.memoizedState&&$s(o,i,n),hs(i,i.return);break;case 30:break;default:$s(o,i,n)}t=t.sibling}}function Vs(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Fo(n))}function Ws(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Fo(e))}function qs(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Qs(e,t,n,r),t=t.sibling}function Qs(e,t,n,r){var o=t.flags;switch(t.tag){case 0:case 11:case 15:qs(e,t,n,r),2048&o&&cs(9,t);break;case 1:case 13:default:qs(e,t,n,r);break;case 3:qs(e,t,n,r),2048&o&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Fo(e)));break;case 12:if(2048&o){qs(e,t,n,r),e=t.stateNode;try{var i=t.memoizedProps,a=i.id,l=i.onPostCommit;"function"==typeof l&&l(a,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(s){hc(t,t.return,s)}}else qs(e,t,n,r);break;case 23:break;case 22:i=t.stateNode,a=t.alternate,null!==t.memoizedState?2&i._visibility?qs(e,t,n,r):Ys(e,t):2&i._visibility?qs(e,t,n,r):(i._visibility|=2,Ks(e,t,n,r,!!(10256&t.subtreeFlags))),2048&o&&Vs(a,t);break;case 24:qs(e,t,n,r),2048&o&&Ws(t.alternate,t)}}function Ks(e,t,n,r,o){for(o=o&&!!(10256&t.subtreeFlags),t=t.child;null!==t;){var i=e,a=t,l=n,s=r,u=a.flags;switch(a.tag){case 0:case 11:case 15:Ks(i,a,l,s,o),cs(8,a);break;case 23:break;case 22:var c=a.stateNode;null!==a.memoizedState?2&c._visibility?Ks(i,a,l,s,o):Ys(i,a):(c._visibility|=2,Ks(i,a,l,s,o)),o&&2048&u&&Vs(a.alternate,a);break;case 24:Ks(i,a,l,s,o),o&&2048&u&&Ws(a.alternate,a);break;default:Ks(i,a,l,s,o)}t=t.sibling}}function Ys(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,o=r.flags;switch(r.tag){case 22:Ys(n,r),2048&o&&Vs(r.alternate,r);break;case 24:Ys(n,r),2048&o&&Ws(r.alternate,r);break;default:Ys(n,r)}t=t.sibling}}var Xs=8192;function Js(e){if(e.subtreeFlags&Xs)for(e=e.child;null!==e;)Gs(e),e=e.sibling}function Gs(e){switch(e.tag){case 26:Js(e),e.flags&Xs&&null!==e.memoizedState&&function(e,t,n){if(null===Qf)throw Error(r(475));var o=Qf;if(!("stylesheet"!==t.type||"string"==typeof n.media&&!1===matchMedia(n.media).matches||4&t.state.loading)){if(null===t.instance){var i=jf(n.href),a=e.querySelector(zf(i));if(a)return null!==(e=a._p)&&"object"==typeof e&&"function"==typeof e.then&&(o.count++,o=Yf.bind(o),e.then(o,o)),t.state.loading|=4,t.instance=a,void Ye(a);a=e.ownerDocument||e,n=Df(n),(i=Tf.get(i))&&Bf(n,i),Ye(a=a.createElement("link"));var l=a;l._p=new Promise((function(e,t){l.onload=e,l.onerror=t})),of(a,"link",n),t.instance=a}null===o.stylesheets&&(o.stylesheets=new Map),o.stylesheets.set(t,e),(e=t.state.preload)&&!(3&t.state.loading)&&(o.count++,t=Yf.bind(o),e.addEventListener("load",t),e.addEventListener("error",t))}}(Ms,e.memoizedState,e.memoizedProps);break;case 5:default:Js(e);break;case 3:case 4:var t=Ms;Ms=Nf(e.stateNode.containerInfo),Js(e),Ms=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Xs,Xs=16777216,Js(e),Xs=t):Js(e))}}function Zs(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function eu(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Ts=r,ru(r,e)}Zs(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)tu(e),e=e.sibling}function tu(e){switch(e.tag){case 0:case 11:case 15:eu(e),2048&e.flags&&fs(9,e,e.return);break;case 3:case 12:default:eu(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,nu(e)):eu(e)}}function nu(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Ts=r,ru(r,e)}Zs(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:fs(8,t,t.return),nu(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,nu(t));break;default:nu(t)}e=e.sibling}}function ru(e,t){for(;null!==Ts;){var n=Ts;switch(n.tag){case 0:case 11:case 15:fs(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Fo(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Ts=r;else e:for(n=e;null!==Ts;){var o=(r=Ts).sibling,i=r.return;if(Ns(r),r===n){Ts=null;break e}if(null!==o){o.return=i,Ts=o;break e}Ts=i}}}var ou={getCacheForType:function(e){var t=Lo(Mo),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},iu="function"==typeof WeakMap?WeakMap:Map,au=0,lu=null,su=null,uu=0,cu=0,fu=null,du=!1,pu=!1,hu=!1,mu=0,gu=0,yu=0,vu=0,bu=0,ku=0,wu=0,xu=null,Su=null,Eu=!1,Cu=0,_u=1/0,Tu=null,Pu=null,Nu=0,Ou=null,Lu=null,Ru=0,Au=0,ju=null,zu=null,Du=0,Mu=null;function Iu(){if(2&au&&0!==uu)return uu&-uu;if(null!==I.T){return 0!==Ho?Ho:Ac()}return je()}function Fu(){0===ku&&(ku=536870912&uu&&!uo?536870912:_e());var e=sl.current;return null!==e&&(e.flags|=32),ku}function Uu(e,t,n){(e!==lu||2!==cu&&9!==cu)&&null===e.cancelPendingCommit||(Qu(e,0),Vu(e,uu,ku,!1)),Ne(e,n),2&au&&e===lu||(e===lu&&(!(2&au)&&(vu|=n),4===gu&&Vu(e,uu,ku,!1)),_c(e))}function Bu(e,t,n){if(6&au)throw Error(r(327));for(var o=!n&&!(124&t)&&0===(t&e.expiredLanes)||Ee(e,t),i=o?function(e,t){var n=au;au|=2;var o=Yu(),i=Xu();lu!==e||uu!==t?(Tu=null,_u=ie()+500,Qu(e,t)):pu=Ee(e,t);e:for(;;)try{if(0!==cu&&null!==su){t=su;var a=fu;t:switch(cu){case 1:cu=0,fu=null,rc(e,t,a,1);break;case 2:case 9:if(ei(a)){cu=0,fu=null,nc(t);break}t=function(){2!==cu&&9!==cu||lu!==e||(cu=7),_c(e)},a.then(t,t);break e;case 3:cu=7;break e;case 4:cu=5;break e;case 7:ei(a)?(cu=0,fu=null,nc(t)):(cu=0,fu=null,rc(e,t,a,7));break;case 5:var l=null;switch(su.tag){case 26:l=su.memoizedState;case 5:case 27:var s=su;if(!l||qf(l)){cu=0,fu=null;var u=s.sibling;if(null!==u)su=u;else{var c=s.return;null!==c?(su=c,oc(c)):su=null}break t}}cu=0,fu=null,rc(e,t,a,5);break;case 6:cu=0,fu=null,rc(e,t,a,6);break;case 8:qu(),gu=6;break e;default:throw Error(r(462))}}ec();break}catch(f){Ku(e,f)}return So=xo=null,I.H=o,I.A=i,au=n,null!==su?0:(lu=null,uu=0,Rr(),gu)}(e,t):Gu(e,t,!0),a=o;;){if(0===i){pu&&!o&&Vu(e,t,0,!1);break}if(n=e.current.alternate,!a||$u(n)){if(2===i){if(a=t,e.errorRecoveryDisabledLanes&a)var l=0;else l=0!==(l=-536870913&e.pendingLanes)?l:536870912&l?536870912:0;if(0!==l){t=l;e:{var s=e;i=xu;var u=s.current.memoizedState.isDehydrated;if(u&&(Qu(s,l).flags|=256),2!==(l=Gu(s,l,!1))){if(hu&&!u){s.errorRecoveryDisabledLanes|=a,vu|=a,i=4;break e}a=Su,Su=i,null!==a&&(null===Su?Su=a:Su.push.apply(Su,a))}i=l}if(a=!1,2!==i)continue}}if(1===i){Qu(e,0),Vu(e,t,0,!0);break}e:{switch(o=e,a=i){case 0:case 1:throw Error(r(345));case 4:if((4194048&t)!==t)break;case 6:Vu(o,t,ku,!du);break e;case 2:Su=null;break;case 3:case 5:break;default:throw Error(r(329))}if((62914560&t)===t&&10<(i=Cu+300-ie())){if(Vu(o,t,ku,!du),0!==Se(o,0,!0))break e;o.timeoutHandle=pf(Hu.bind(null,o,n,Su,Tu,Eu,t,ku,vu,wu,du,a,2,-0,0),i)}else Hu(o,n,Su,Tu,Eu,t,ku,vu,wu,du,a,0,-0,0)}break}i=Gu(e,t,!1),a=!1}_c(e)}function Hu(e,t,n,o,i,a,l,s,u,c,f,d,p,h){if(e.timeoutHandle=-1,(8192&(d=t.subtreeFlags)||!(16785408&~d))&&(Qf={stylesheets:null,count:0,unsuspend:Kf},Gs(t),null!==(d=function(){if(null===Qf)throw Error(r(475));var e=Qf;return e.stylesheets&&0===e.count&&Jf(e,e.stylesheets),0<e.count?function(t){var n=setTimeout((function(){if(e.stylesheets&&Jf(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}}),6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=d(ac.bind(null,e,t,a,n,o,i,l,s,u,f,1,p,h)),void Vu(e,a,l,!c);ac(e,t,a,n,o,i,l,s,u)}function $u(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Zn(i(),o))return!1}catch(a){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Vu(e,t,n,r){t&=~bu,t&=~vu,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var o=t;0<o;){var i=31-ye(o),a=1<<i;r[i]=-1,o&=~a}0!==n&&Oe(e,n,t)}function Wu(){return!!(6&au)||(Tc(0),!1)}function qu(){if(null!==su){if(0===cu)var e=su.return;else So=xo=null,Hi(e=su),Za=null,el=0,e=su;for(;null!==e;)us(e.alternate,e),e=e.return;su=null}}function Qu(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,hf(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),qu(),lu=e,su=n=Hr(e.current,null),uu=t,cu=0,fu=null,du=!1,pu=Ee(e,t),hu=!1,wu=ku=bu=vu=yu=gu=0,Su=xu=null,Eu=!1,8&t&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var o=31-ye(r),i=1<<o;t|=e[o],r&=~i}return mu=t,Rr(),n}function Ku(e,t){Ei=null,I.H=Ya,t===Xo||t===Go?(t=oi(),cu=3):t===Jo?(t=oi(),cu=4):cu=t===Ol?8:null!==t&&"object"==typeof t&&"function"==typeof t.then?6:1,fu=t,null===su&&(gu=1,Cl(e,Pr(t,e.current)))}function Yu(){var e=I.H;return I.H=Ya,null===e?Ya:e}function Xu(){var e=I.A;return I.A=ou,e}function Ju(){gu=4,du||(4194048&uu)!==uu&&null!==sl.current||(pu=!0),!(134217727&yu)&&!(134217727&vu)||null===lu||Vu(lu,uu,ku,!1)}function Gu(e,t,n){var r=au;au|=2;var o=Yu(),i=Xu();lu===e&&uu===t||(Tu=null,Qu(e,t)),t=!1;var a=gu;e:for(;;)try{if(0!==cu&&null!==su){var l=su,s=fu;switch(cu){case 8:qu(),a=6;break e;case 3:case 2:case 9:case 6:null===sl.current&&(t=!0);var u=cu;if(cu=0,fu=null,rc(e,l,s,u),n&&pu){a=0;break e}break;default:u=cu,cu=0,fu=null,rc(e,l,s,u)}}Zu(),a=gu;break}catch(c){Ku(e,c)}return t&&e.shellSuspendCounter++,So=xo=null,au=r,I.H=o,I.A=i,null===su&&(lu=null,uu=0,Rr()),a}function Zu(){for(;null!==su;)tc(su)}function ec(){for(;null!==su&&!re();)tc(su)}function tc(e){var t=ts(e.alternate,e,mu);e.memoizedProps=e.pendingProps,null===t?oc(e):su=t}function nc(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Ul(n,t,t.pendingProps,t.type,void 0,uu);break;case 11:t=Ul(n,t,t.pendingProps,t.type.render,t.ref,uu);break;case 5:Hi(t);default:us(n,t),t=ts(n,t=su=$r(t,mu),mu)}e.memoizedProps=e.pendingProps,null===t?oc(e):su=t}function rc(e,t,n,o){So=xo=null,Hi(t),Za=null,el=0;var i=t.return;try{if(function(e,t,n,o,i){if(n.flags|=32768,null!==o&&"object"==typeof o&&"function"==typeof o.then){if(null!==(t=n.alternate)&&Po(t,n,i,!0),null!==(n=sl.current)){switch(n.tag){case 13:return null===ul?Ju():null===n.alternate&&0===gu&&(gu=3),n.flags&=-257,n.flags|=65536,n.lanes=i,o===Zo?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([o]):t.add(o),mc(e,o,i)),!1;case 22:return n.flags|=65536,o===Zo?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([o])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([o]):n.add(o),mc(e,o,i)),!1}throw Error(r(435,n.tag))}return mc(e,o,i),Ju(),!1}if(uo)return null!==(t=sl.current)?(!(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=i,o!==po&&ko(Pr(e=Error(r(422),{cause:o}),n))):(o!==po&&ko(Pr(t=Error(r(423),{cause:o}),n)),(e=e.current.alternate).flags|=65536,i&=-i,e.lanes|=i,o=Pr(o,n),di(e,i=Tl(e.stateNode,o,i)),4!==gu&&(gu=2)),!1;var a=Error(r(520),{cause:o});if(a=Pr(a,n),null===xu?xu=[a]:xu.push(a),4!==gu&&(gu=2),null===t)return!0;o=Pr(o,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=i&-i,n.lanes|=e,di(n,e=Tl(n.stateNode,o,e)),!1;case 1:if(t=n.type,a=n.stateNode,!(128&n.flags||"function"!=typeof t.getDerivedStateFromError&&(null===a||"function"!=typeof a.componentDidCatch||null!==Pu&&Pu.has(a))))return n.flags|=65536,i&=-i,n.lanes|=i,Nl(i=Pl(i),e,n,o),di(n,i),!1}n=n.return}while(null!==n);return!1}(e,i,t,n,uu))return gu=1,Cl(e,Pr(n,e.current)),void(su=null)}catch(a){if(null!==i)throw su=i,a;return gu=1,Cl(e,Pr(n,e.current)),void(su=null)}32768&t.flags?(uo||1===o?e=!0:pu||536870912&uu?e=!1:(du=e=!0,(2===o||9===o||3===o||6===o)&&(null!==(o=sl.current)&&13===o.tag&&(o.flags|=16384))),ic(t,e)):oc(t)}function oc(e){var t=e;do{if(32768&t.flags)return void ic(t,du);e=t.return;var n=ls(t.alternate,t,mu);if(null!==n)return void(su=n);if(null!==(t=t.sibling))return void(su=t);su=t=e}while(null!==t);0===gu&&(gu=5)}function ic(e,t){do{var n=ss(e.alternate,e);if(null!==n)return n.flags&=32767,void(su=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(su=e);su=e=n}while(null!==e);gu=6,su=null}function ac(e,t,n,o,i,a,l,s,u){e.cancelPendingCommit=null;do{fc()}while(0!==Nu);if(6&au)throw Error(r(327));if(null!==t){if(t===e.current)throw Error(r(177));if(a=t.lanes|t.childLanes,function(e,t,n,r,o,i){var a=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var l=e.entanglements,s=e.expirationTimes,u=e.hiddenUpdates;for(n=a&~n;0<n;){var c=31-ye(n),f=1<<c;l[c]=0,s[c]=-1;var d=u[c];if(null!==d)for(u[c]=null,c=0;c<d.length;c++){var p=d[c];null!==p&&(p.lane&=-536870913)}n&=~f}0!==r&&Oe(e,r,0),0!==i&&0===o&&0!==e.tag&&(e.suspendedLanes|=i&~(a&~t))}(e,n,a|=Lr,l,s,u),e===lu&&(su=lu=null,uu=0),Lu=t,Ou=e,Ru=n,Au=a,ju=i,zu=o,10256&t.subtreeFlags||10256&t.flags?(e.callbackNode=null,e.callbackPriority=0,te(ue,(function(){return dc(),null}))):(e.callbackNode=null,e.callbackPriority=0),o=!!(13878&t.flags),13878&t.subtreeFlags||o){o=I.T,I.T=null,i=F.p,F.p=2,l=au,au|=4;try{!function(e,t){if(e=e.containerInfo,af=ld,ir(e=or(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var o=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(o&&0!==o.rangeCount){n=o.anchorNode;var i=o.anchorOffset,a=o.focusNode;o=o.focusOffset;try{n.nodeType,a.nodeType}catch(g){n=null;break e}var l=0,s=-1,u=-1,c=0,f=0,d=e,p=null;t:for(;;){for(var h;d!==n||0!==i&&3!==d.nodeType||(s=l+i),d!==a||0!==o&&3!==d.nodeType||(u=l+o),3===d.nodeType&&(l+=d.nodeValue.length),null!==(h=d.firstChild);)p=d,d=h;for(;;){if(d===e)break t;if(p===n&&++c===i&&(s=l),p===a&&++f===o&&(u=l),null!==(h=d.nextSibling))break;p=(d=p).parentNode}d=h}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(lf={focusedElem:e,selectionRange:n},ld=!1,Ts=t;null!==Ts;)if(e=(t=Ts).child,1024&t.subtreeFlags&&null!==e)e.return=t,Ts=e;else for(;null!==Ts;){switch(a=(t=Ts).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(1024&e&&null!==a){e=void 0,n=t,i=a.memoizedProps,a=a.memoizedState,o=n.stateNode;try{var m=kl(n.type,i,(n.elementType,n.type));e=o.getSnapshotBeforeUpdate(m,a),o.__reactInternalSnapshotBeforeUpdate=e}catch(y){hc(n,n.return,y)}}break;case 3:if(1024&e)if(9===(n=(e=t.stateNode.containerInfo).nodeType))kf(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":kf(e);break;default:e.textContent=""}break;default:if(1024&e)throw Error(r(163))}if(null!==(e=t.sibling)){e.return=t.return,Ts=e;break}Ts=t.return}}(e,t)}finally{au=l,F.p=i,I.T=o}}Nu=1,lc(),sc(),uc()}}function lc(){if(1===Nu){Nu=0;var e=Ou,t=Lu,n=!!(13878&t.flags);if(13878&t.subtreeFlags||n){n=I.T,I.T=null;var r=F.p;F.p=2;var o=au;au|=4;try{Is(t,e);var i=lf,a=or(e.containerInfo),l=i.focusedElem,s=i.selectionRange;if(a!==l&&l&&l.ownerDocument&&rr(l.ownerDocument.documentElement,l)){if(null!==s&&ir(l)){var u=s.start,c=s.end;if(void 0===c&&(c=u),"selectionStart"in l)l.selectionStart=u,l.selectionEnd=Math.min(c,l.value.length);else{var f=l.ownerDocument||document,d=f&&f.defaultView||window;if(d.getSelection){var p=d.getSelection(),h=l.textContent.length,m=Math.min(s.start,h),g=void 0===s.end?m:Math.min(s.end,h);!p.extend&&m>g&&(a=g,g=m,m=a);var y=nr(l,m),v=nr(l,g);if(y&&v&&(1!==p.rangeCount||p.anchorNode!==y.node||p.anchorOffset!==y.offset||p.focusNode!==v.node||p.focusOffset!==v.offset)){var b=f.createRange();b.setStart(y.node,y.offset),p.removeAllRanges(),m>g?(p.addRange(b),p.extend(v.node,v.offset)):(b.setEnd(v.node,v.offset),p.addRange(b))}}}}for(f=[],p=l;p=p.parentNode;)1===p.nodeType&&f.push({element:p,left:p.scrollLeft,top:p.scrollTop});for("function"==typeof l.focus&&l.focus(),l=0;l<f.length;l++){var k=f[l];k.element.scrollLeft=k.left,k.element.scrollTop=k.top}}ld=!!af,lf=af=null}finally{au=o,F.p=r,I.T=n}}e.current=t,Nu=2}}function sc(){if(2===Nu){Nu=0;var e=Ou,t=Lu,n=!!(8772&t.flags);if(8772&t.subtreeFlags||n){n=I.T,I.T=null;var r=F.p;F.p=2;var o=au;au|=4;try{Ps(e,t.alternate,t)}finally{au=o,F.p=r,I.T=n}}Nu=3}}function uc(){if(4===Nu||3===Nu){Nu=0,oe();var e=Ou,t=Lu,n=Ru,r=zu;10256&t.subtreeFlags||10256&t.flags?Nu=5:(Nu=0,Lu=Ou=null,cc(e,e.pendingLanes));var o=e.pendingLanes;if(0===o&&(Pu=null),Ae(n),t=t.stateNode,me&&"function"==typeof me.onCommitFiberRoot)try{me.onCommitFiberRoot(he,t,void 0,!(128&~t.current.flags))}catch(s){}if(null!==r){t=I.T,o=F.p,F.p=2,I.T=null;try{for(var i=e.onRecoverableError,a=0;a<r.length;a++){var l=r[a];i(l.value,{componentStack:l.stack})}}finally{I.T=t,F.p=o}}3&Ru&&fc(),_c(e),o=e.pendingLanes,4194090&n&&42&o?e===Mu?Du++:(Du=0,Mu=e):Du=0,Tc(0)}}function cc(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Fo(t)))}function fc(e){return lc(),sc(),uc(),dc()}function dc(){if(5!==Nu)return!1;var e=Ou,t=Au;Au=0;var n=Ae(Ru),o=I.T,i=F.p;try{F.p=32>n?32:n,I.T=null,n=ju,ju=null;var a=Ou,l=Ru;if(Nu=0,Lu=Ou=null,Ru=0,6&au)throw Error(r(331));var s=au;if(au|=4,tu(a.current),Qs(a,a.current,l,n),au=s,Tc(0,!1),me&&"function"==typeof me.onPostCommitFiberRoot)try{me.onPostCommitFiberRoot(he,a)}catch(u){}return!0}finally{F.p=i,I.T=o,cc(e,t)}}function pc(e,t,n){t=Pr(n,t),null!==(e=ci(e,t=Tl(e.stateNode,t,2),2))&&(Ne(e,2),_c(e))}function hc(e,t,n){if(3===e.tag)pc(e,e,n);else for(;null!==t;){if(3===t.tag){pc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Pu||!Pu.has(r))){e=Pr(n,e),null!==(r=ci(t,n=Pl(2),2))&&(Nl(n,r,t,e),Ne(r,2),_c(r));break}}t=t.return}}function mc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new iu;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(hu=!0,o.add(n),e=gc.bind(null,e,t,n),t.then(e,e))}function gc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,lu===e&&(uu&n)===n&&(4===gu||3===gu&&(62914560&uu)===uu&&300>ie()-Cu?!(2&au)&&Qu(e,0):bu|=n,wu===uu&&(wu=0)),_c(e)}function yc(e,t){0===t&&(t=Te()),null!==(e=zr(e,t))&&(Ne(e,t),_c(e))}function vc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),yc(e,n)}function bc(e,t){var n=0;switch(e.tag){case 13:var o=e.stateNode,i=e.memoizedState;null!==i&&(n=i.retryLane);break;case 19:o=e.stateNode;break;case 22:o=e.stateNode._retryCache;break;default:throw Error(r(314))}null!==o&&o.delete(t),yc(e,n)}var kc=null,wc=null,xc=!1,Sc=!1,Ec=!1,Cc=0;function _c(e){e!==wc&&null===e.next&&(null===wc?kc=wc=e:wc=wc.next=e),Sc=!0,xc||(xc=!0,gf((function(){6&au?te(le,Pc):Nc()})))}function Tc(e,t){if(!Ec&&Sc){Ec=!0;do{for(var n=!1,r=kc;null!==r;){if(0!==e){var o=r.pendingLanes;if(0===o)var i=0;else{var a=r.suspendedLanes,l=r.pingedLanes;i=(1<<31-ye(42|e)+1)-1,i=201326741&(i&=o&~(a&~l))?201326741&i|1:i?2|i:0}0!==i&&(n=!0,Rc(r,i))}else i=uu,!(3&(i=Se(r,r===lu?i:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||Ee(r,i)||(n=!0,Rc(r,i));r=r.next}}while(n);Ec=!1}}function Pc(){Nc()}function Nc(){Sc=xc=!1;var e=0;0!==Cc&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==df&&(df=e,!0);return df=null,!1}()&&(e=Cc),Cc=0);for(var t=ie(),n=null,r=kc;null!==r;){var o=r.next,i=Oc(r,t);0===i?(r.next=null,null===n?kc=o:n.next=o,null===o&&(wc=n)):(n=r,(0!==e||3&i)&&(Sc=!0)),r=o}Tc(e)}function Oc(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=-62914561&e.pendingLanes;0<i;){var a=31-ye(i),l=1<<a,s=o[a];-1===s?0!==(l&n)&&0===(l&r)||(o[a]=Ce(l,t)):s<=t&&(e.expiredLanes|=l),i&=~l}if(n=uu,n=Se(e,e===(t=lu)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===cu||9===cu)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&ne(r),e.callbackNode=null,e.callbackPriority=0;if(!(3&n)||Ee(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&ne(r),Ae(n)){case 2:case 8:n=se;break;case 32:default:n=ue;break;case 268435456:n=fe}return r=Lc.bind(null,e),n=te(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&ne(r),e.callbackPriority=2,e.callbackNode=null,2}function Lc(e,t){if(0!==Nu&&5!==Nu)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(fc()&&e.callbackNode!==n)return null;var r=uu;return 0===(r=Se(e,e===lu?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Bu(e,r,t),Oc(e,ie()),null!=e.callbackNode&&e.callbackNode===n?Lc.bind(null,e):null)}function Rc(e,t){if(fc())return null;Bu(e,t,!0)}function Ac(){return 0===Cc&&(Cc=_e()),Cc}function jc(e){return null==e||"symbol"==typeof e||"boolean"==typeof e?null:"function"==typeof e?e:At(""+e)}function zc(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Dc=0;Dc<Cr.length;Dc++){var Mc=Cr[Dc];_r(Mc.toLowerCase(),"on"+(Mc[0].toUpperCase()+Mc.slice(1)))}_r(yr,"onAnimationEnd"),_r(vr,"onAnimationIteration"),_r(br,"onAnimationStart"),_r("dblclick","onDoubleClick"),_r("focusin","onFocus"),_r("focusout","onBlur"),_r(kr,"onTransitionRun"),_r(wr,"onTransitionStart"),_r(xr,"onTransitionCancel"),_r(Sr,"onTransitionEnd"),Ze("onMouseEnter",["mouseout","mouseover"]),Ze("onMouseLeave",["mouseout","mouseover"]),Ze("onPointerEnter",["pointerout","pointerover"]),Ze("onPointerLeave",["pointerout","pointerover"]),Ge("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ge("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ge("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ge("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ge("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ge("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ic="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fc=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ic));function Uc(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var a=r.length-1;0<=a;a--){var l=r[a],s=l.instance,u=l.currentTarget;if(l=l.listener,s!==i&&o.isPropagationStopped())break e;i=l,o.currentTarget=u;try{i(o)}catch(c){wl(c)}o.currentTarget=null,i=s}else for(a=0;a<r.length;a++){if(s=(l=r[a]).instance,u=l.currentTarget,l=l.listener,s!==i&&o.isPropagationStopped())break e;i=l,o.currentTarget=u;try{i(o)}catch(c){wl(c)}o.currentTarget=null,i=s}}}}function Bc(e,t){var n=t[Fe];void 0===n&&(n=t[Fe]=new Set);var r=e+"__bubble";n.has(r)||(Wc(t,e,2,!1),n.add(r))}function Hc(e,t,n){var r=0;t&&(r|=4),Wc(n,e,r,t)}var $c="_reactListening"+Math.random().toString(36).slice(2);function Vc(e){if(!e[$c]){e[$c]=!0,Xe.forEach((function(t){"selectionchange"!==t&&(Fc.has(t)||Hc(t,!1,e),Hc(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[$c]||(t[$c]=!0,Hc("selectionchange",!1,t))}}function Wc(e,t,n,r){switch(hd(t)){case 2:var o=sd;break;case 8:o=ud;break;default:o=cd}n=o.bind(null,t,n,e),o=void 0,!$t||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function qc(e,t,n,r,o){var a=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var l=r.tag;if(3===l||4===l){var s=r.stateNode.containerInfo;if(s===o)break;if(4===l)for(l=r.return;null!==l;){var u=l.tag;if((3===u||4===u)&&l.stateNode.containerInfo===o)return;l=l.return}for(;null!==s;){if(null===(l=We(s)))return;if(5===(u=l.tag)||6===u||26===u||27===u){r=a=l;continue e}s=s.parentNode}}r=r.return}Ut((function(){var r=a,o=zt(n),l=[];e:{var s=Er.get(e);if(void 0!==s){var u=rn,c=e;switch(e){case"keypress":if(0===Yt(n))break e;case"keydown":case"keyup":u=bn;break;case"focusin":c="focus",u=cn;break;case"focusout":c="blur",u=cn;break;case"beforeblur":case"afterblur":u=cn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=sn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=un;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=wn;break;case yr:case vr:case br:u=fn;break;case Sr:u=xn;break;case"scroll":case"scrollend":u=an;break;case"wheel":u=Sn;break;case"copy":case"cut":case"paste":u=dn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=kn;break;case"toggle":case"beforetoggle":u=En}var f=!!(4&t),d=!f&&("scroll"===e||"scrollend"===e),p=f?null!==s?s+"Capture":null:s;f=[];for(var h,m=r;null!==m;){var g=m;if(h=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===h||null===p||null!=(g=Bt(m,p))&&f.push(Qc(m,g,h)),d)break;m=m.return}0<f.length&&(s=new u(s,c,null,n,o),l.push({event:s,listeners:f}))}}if(!(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===jt||!(c=n.relatedTarget||n.fromElement)||!We(c)&&!c[Ie])&&(u||s)&&(s=o.window===o?o:(s=o.ownerDocument)?s.defaultView||s.parentWindow:window,u?(u=r,null!==(c=(c=n.relatedTarget||n.toElement)?We(c):null)&&(d=i(c),f=c.tag,c!==d||5!==f&&27!==f&&6!==f)&&(c=null)):(u=null,c=r),u!==c)){if(f=sn,g="onMouseLeave",p="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(f=kn,g="onPointerLeave",p="onPointerEnter",m="pointer"),d=null==u?s:Qe(u),h=null==c?s:Qe(c),(s=new f(g,m+"leave",u,n,o)).target=d,s.relatedTarget=h,g=null,We(o)===r&&((f=new f(p,m+"enter",c,n,o)).target=h,f.relatedTarget=d,g=f),d=g,u&&c)e:{for(p=c,m=0,h=f=u;h;h=Yc(h))m++;for(h=0,g=p;g;g=Yc(g))h++;for(;0<m-h;)f=Yc(f),m--;for(;0<h-m;)p=Yc(p),h--;for(;m--;){if(f===p||null!==p&&f===p.alternate)break e;f=Yc(f),p=Yc(p)}f=null}else f=null;null!==u&&Xc(l,s,u,f,!1),null!==c&&null!==d&&Xc(l,d,c,f,!0)}if("select"===(u=(s=r?Qe(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===u&&"file"===s.type)var y=Hn;else if(Dn(s))if($n)y=Gn;else{y=Xn;var v=Yn}else!(u=s.nodeName)||"input"!==u.toLowerCase()||"checkbox"!==s.type&&"radio"!==s.type?r&&Ot(r.elementType)&&(y=Hn):y=Jn;switch(y&&(y=y(e,r))?Mn(l,y,n,o):(v&&v(e,s,r),"focusout"===e&&r&&"number"===s.type&&null!=r.memoizedProps.value&&xt(s,"number",s.value)),v=r?Qe(r):window,e){case"focusin":(Dn(v)||"true"===v.contentEditable)&&(lr=v,sr=r,ur=null);break;case"focusout":ur=sr=lr=null;break;case"mousedown":cr=!0;break;case"contextmenu":case"mouseup":case"dragend":cr=!1,fr(l,n,o);break;case"selectionchange":if(ar)break;case"keydown":case"keyup":fr(l,n,o)}var b;if(_n)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else jn?Rn(e,n)&&(k="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(k="onCompositionStart");k&&(Nn&&"ko"!==n.locale&&(jn||"onCompositionStart"!==k?"onCompositionEnd"===k&&jn&&(b=Kt()):(qt="value"in(Wt=o)?Wt.value:Wt.textContent,jn=!0)),0<(v=Kc(r,k)).length&&(k=new pn(k,e,null,n,o),l.push({event:k,listeners:v}),b?k.data=b:null!==(b=An(n))&&(k.data=b))),(b=Pn?function(e,t){switch(e){case"compositionend":return An(t);case"keypress":return 32!==t.which?null:(Ln=!0,On);case"textInput":return(e=t.data)===On&&Ln?null:e;default:return null}}(e,n):function(e,t){if(jn)return"compositionend"===e||!_n&&Rn(e,t)?(e=Kt(),Qt=qt=Wt=null,jn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Nn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(k=Kc(r,"onBeforeInput")).length&&(v=new pn("onBeforeInput","beforeinput",null,n,o),l.push({event:v,listeners:k}),v.data=b)),function(e,t,n,r,o){if("submit"===t&&n&&n.stateNode===o){var i=jc((o[Me]||null).action),a=r.submitter;a&&null!==(t=(t=a[Me]||null)?jc(t.formAction):a.getAttribute("formAction"))&&(i=t,a=null);var l=new rn("action","action",null,r,o);e.push({event:l,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==Cc){var e=a?zc(o,a):new FormData(o);za(n,{pending:!0,data:e,method:o.method,action:i},null,e)}}else"function"==typeof i&&(l.preventDefault(),e=a?zc(o,a):new FormData(o),za(n,{pending:!0,data:e,method:o.method,action:i},i,e))},currentTarget:o}]})}}(l,e,r,n,o)}Uc(l,t)}))}function Qc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Kc(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,i=o.stateNode;if(5!==(o=o.tag)&&26!==o&&27!==o||null===i||(null!=(o=Bt(e,n))&&r.unshift(Qc(e,o,i)),null!=(o=Bt(e,t))&&r.push(Qc(e,o,i))),3===e.tag)return r;e=e.return}return[]}function Yc(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Xc(e,t,n,r,o){for(var i=t._reactName,a=[];null!==n&&n!==r;){var l=n,s=l.alternate,u=l.stateNode;if(l=l.tag,null!==s&&s===r)break;5!==l&&26!==l&&27!==l||null===u||(s=u,o?null!=(u=Bt(n,i))&&a.unshift(Qc(n,u,s)):o||null!=(u=Bt(n,i))&&a.push(Qc(n,u,s))),n=n.return}0!==a.length&&e.push({event:t,listeners:a})}var Jc=/\r\n?/g,Gc=/\u0000|\uFFFD/g;function Zc(e){return("string"==typeof e?e:""+e).replace(Jc,"\n").replace(Gc,"")}function ef(e,t){return t=Zc(t),Zc(e)===t}function tf(){}function nf(e,t,n,o,i,a){switch(n){case"children":"string"==typeof o?"body"===t||"textarea"===t&&""===o||_t(e,o):("number"==typeof o||"bigint"==typeof o)&&"body"!==t&&_t(e,""+o);break;case"className":at(e,"class",o);break;case"tabIndex":at(e,"tabindex",o);break;case"dir":case"role":case"viewBox":case"width":case"height":at(e,n,o);break;case"style":Nt(e,o,a);break;case"data":if("object"!==t){at(e,"data",o);break}case"src":case"href":if(""===o&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==o||"function"==typeof o||"symbol"==typeof o||"boolean"==typeof o){e.removeAttribute(n);break}o=At(""+o),e.setAttribute(n,o);break;case"action":case"formAction":if("function"==typeof o){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"==typeof a&&("formAction"===n?("input"!==t&&nf(e,t,"name",i.name,i,null),nf(e,t,"formEncType",i.formEncType,i,null),nf(e,t,"formMethod",i.formMethod,i,null),nf(e,t,"formTarget",i.formTarget,i,null)):(nf(e,t,"encType",i.encType,i,null),nf(e,t,"method",i.method,i,null),nf(e,t,"target",i.target,i,null))),null==o||"symbol"==typeof o||"boolean"==typeof o){e.removeAttribute(n);break}o=At(""+o),e.setAttribute(n,o);break;case"onClick":null!=o&&(e.onclick=tf);break;case"onScroll":null!=o&&Bc("scroll",e);break;case"onScrollEnd":null!=o&&Bc("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=o){if("object"!=typeof o||!("__html"in o))throw Error(r(61));if(null!=(n=o.__html)){if(null!=i.children)throw Error(r(60));e.innerHTML=n}}break;case"multiple":e.multiple=o&&"function"!=typeof o&&"symbol"!=typeof o;break;case"muted":e.muted=o&&"function"!=typeof o&&"symbol"!=typeof o;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==o||"function"==typeof o||"boolean"==typeof o||"symbol"==typeof o){e.removeAttribute("xlink:href");break}n=At(""+o),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=o&&"function"!=typeof o&&"symbol"!=typeof o?e.setAttribute(n,""+o):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":o&&"function"!=typeof o&&"symbol"!=typeof o?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===o?e.setAttribute(n,""):!1!==o&&null!=o&&"function"!=typeof o&&"symbol"!=typeof o?e.setAttribute(n,o):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=o&&"function"!=typeof o&&"symbol"!=typeof o&&!isNaN(o)&&1<=o?e.setAttribute(n,o):e.removeAttribute(n);break;case"rowSpan":case"start":null==o||"function"==typeof o||"symbol"==typeof o||isNaN(o)?e.removeAttribute(n):e.setAttribute(n,o);break;case"popover":Bc("beforetoggle",e),Bc("toggle",e),it(e,"popover",o);break;case"xlinkActuate":lt(e,"http://www.w3.org/1999/xlink","xlink:actuate",o);break;case"xlinkArcrole":lt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",o);break;case"xlinkRole":lt(e,"http://www.w3.org/1999/xlink","xlink:role",o);break;case"xlinkShow":lt(e,"http://www.w3.org/1999/xlink","xlink:show",o);break;case"xlinkTitle":lt(e,"http://www.w3.org/1999/xlink","xlink:title",o);break;case"xlinkType":lt(e,"http://www.w3.org/1999/xlink","xlink:type",o);break;case"xmlBase":lt(e,"http://www.w3.org/XML/1998/namespace","xml:base",o);break;case"xmlLang":lt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",o);break;case"xmlSpace":lt(e,"http://www.w3.org/XML/1998/namespace","xml:space",o);break;case"is":it(e,"is",o);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&it(e,n=Lt.get(n)||n,o)}}function rf(e,t,n,o,i,a){switch(n){case"style":Nt(e,o,a);break;case"dangerouslySetInnerHTML":if(null!=o){if("object"!=typeof o||!("__html"in o))throw Error(r(61));if(null!=(n=o.__html)){if(null!=i.children)throw Error(r(60));e.innerHTML=n}}break;case"children":"string"==typeof o?_t(e,o):("number"==typeof o||"bigint"==typeof o)&&_t(e,""+o);break;case"onScroll":null!=o&&Bc("scroll",e);break;case"onScrollEnd":null!=o&&Bc("scrollend",e);break;case"onClick":null!=o&&(e.onclick=tf);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Je.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(i=n.endsWith("Capture"),t=n.slice(2,i?n.length-7:void 0),"function"==typeof(a=null!=(a=e[Me]||null)?a[n]:null)&&e.removeEventListener(t,a,i),"function"!=typeof o)?n in e?e[n]=o:!0===o?e.setAttribute(n,""):it(e,n,o):("function"!=typeof a&&null!==a&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,o,i)))}}function of(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Bc("error",e),Bc("load",e);var o,i=!1,a=!1;for(o in n)if(n.hasOwnProperty(o)){var l=n[o];if(null!=l)switch(o){case"src":i=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:nf(e,t,o,l,n,null)}}return a&&nf(e,t,"srcSet",n.srcSet,n,null),void(i&&nf(e,t,"src",n.src,n,null));case"input":Bc("invalid",e);var s=o=l=a=null,u=null,c=null;for(i in n)if(n.hasOwnProperty(i)){var f=n[i];if(null!=f)switch(i){case"name":a=f;break;case"type":l=f;break;case"checked":u=f;break;case"defaultChecked":c=f;break;case"value":o=f;break;case"defaultValue":s=f;break;case"children":case"dangerouslySetInnerHTML":if(null!=f)throw Error(r(137,t));break;default:nf(e,t,i,f,n,null)}}return wt(e,o,s,u,c,l,a,!1),void mt(e);case"select":for(a in Bc("invalid",e),i=l=o=null,n)if(n.hasOwnProperty(a)&&null!=(s=n[a]))switch(a){case"value":o=s;break;case"defaultValue":l=s;break;case"multiple":i=s;default:nf(e,t,a,s,n,null)}return t=o,n=l,e.multiple=!!i,void(null!=t?St(e,!!i,t,!1):null!=n&&St(e,!!i,n,!0));case"textarea":for(l in Bc("invalid",e),o=a=i=null,n)if(n.hasOwnProperty(l)&&null!=(s=n[l]))switch(l){case"value":i=s;break;case"defaultValue":a=s;break;case"children":o=s;break;case"dangerouslySetInnerHTML":if(null!=s)throw Error(r(91));break;default:nf(e,t,l,s,n,null)}return Ct(e,i,a,o),void mt(e);case"option":for(u in n)if(n.hasOwnProperty(u)&&null!=(i=n[u]))if("selected"===u)e.selected=i&&"function"!=typeof i&&"symbol"!=typeof i;else nf(e,t,u,i,n,null);return;case"dialog":Bc("beforetoggle",e),Bc("toggle",e),Bc("cancel",e),Bc("close",e);break;case"iframe":case"object":Bc("load",e);break;case"video":case"audio":for(i=0;i<Ic.length;i++)Bc(Ic[i],e);break;case"image":Bc("error",e),Bc("load",e);break;case"details":Bc("toggle",e);break;case"embed":case"source":case"link":Bc("error",e),Bc("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(i=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:nf(e,t,c,i,n,null)}return;default:if(Ot(t)){for(f in n)n.hasOwnProperty(f)&&(void 0!==(i=n[f])&&rf(e,t,f,i,n,void 0));return}}for(s in n)n.hasOwnProperty(s)&&(null!=(i=n[s])&&nf(e,t,s,i,n,null))}var af=null,lf=null;function sf(e){return 9===e.nodeType?e:e.ownerDocument}function uf(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function cf(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function ff(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"bigint"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var df=null;var pf="function"==typeof setTimeout?setTimeout:void 0,hf="function"==typeof clearTimeout?clearTimeout:void 0,mf="function"==typeof Promise?Promise:void 0,gf="function"==typeof queueMicrotask?queueMicrotask:void 0!==mf?function(e){return mf.resolve(null).then(e).catch(yf)}:pf;function yf(e){setTimeout((function(){throw e}))}function vf(e){return"head"===e}function bf(e,t){var n=t,r=0,o=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&8===i.nodeType)if("/$"===(n=i.data)){if(0<r&&8>r){n=r;var a=e.ownerDocument;if(1&n&&_f(a.documentElement),2&n&&_f(a.body),4&n)for(_f(n=a.head),a=n.firstChild;a;){var l=a.nextSibling,s=a.nodeName;a[$e]||"SCRIPT"===s||"STYLE"===s||"LINK"===s&&"stylesheet"===a.rel.toLowerCase()||n.removeChild(a),a=l}}if(0===o)return e.removeChild(i),void Rd(t);o--}else"$"===n||"$?"===n||"$!"===n?o++:r=n.charCodeAt(0)-48;else r=0;n=i}while(n);Rd(t)}function kf(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":kf(n),Ve(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function wf(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function xf(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var Sf=null;function Ef(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function Cf(e,t,n){switch(t=sf(n),e){case"html":if(!(e=t.documentElement))throw Error(r(452));return e;case"head":if(!(e=t.head))throw Error(r(453));return e;case"body":if(!(e=t.body))throw Error(r(454));return e;default:throw Error(r(451))}}function _f(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ve(e)}var Tf=new Map,Pf=new Set;function Nf(e){return"function"==typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Of=F.d;F.d={f:function(){var e=Of.f(),t=Wu();return e||t},r:function(e){var t=qe(e);null!==t&&5===t.tag&&"form"===t.type?Ma(t):Of.r(e)},D:function(e){Of.D(e),Rf("dns-prefetch",e,null)},C:function(e,t){Of.C(e,t),Rf("preconnect",e,t)},L:function(e,t,n){Of.L(e,t,n);var r=Lf;if(r&&e&&t){var o='link[rel="preload"][as="'+bt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(o+='[imagesrcset="'+bt(n.imageSrcSet)+'"]',"string"==typeof n.imageSizes&&(o+='[imagesizes="'+bt(n.imageSizes)+'"]')):o+='[href="'+bt(e)+'"]';var i=o;switch(t){case"style":i=jf(e);break;case"script":i=Mf(e)}Tf.has(i)||(e=u({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),Tf.set(i,e),null!==r.querySelector(o)||"style"===t&&r.querySelector(zf(i))||"script"===t&&r.querySelector(If(i))||(of(t=r.createElement("link"),"link",e),Ye(t),r.head.appendChild(t)))}},m:function(e,t){Of.m(e,t);var n=Lf;if(n&&e){var r=t&&"string"==typeof t.as?t.as:"script",o='link[rel="modulepreload"][as="'+bt(r)+'"][href="'+bt(e)+'"]',i=o;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Mf(e)}if(!Tf.has(i)&&(e=u({rel:"modulepreload",href:e},t),Tf.set(i,e),null===n.querySelector(o))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(If(i)))return}of(r=n.createElement("link"),"link",e),Ye(r),n.head.appendChild(r)}}},X:function(e,t){Of.X(e,t);var n=Lf;if(n&&e){var r=Ke(n).hoistableScripts,o=Mf(e),i=r.get(o);i||((i=n.querySelector(If(o)))||(e=u({src:e,async:!0},t),(t=Tf.get(o))&&Hf(e,t),Ye(i=n.createElement("script")),of(i,"link",e),n.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},r.set(o,i))}},S:function(e,t,n){Of.S(e,t,n);var r=Lf;if(r&&e){var o=Ke(r).hoistableStyles,i=jf(e);t=t||"default";var a=o.get(i);if(!a){var l={loading:0,preload:null};if(a=r.querySelector(zf(i)))l.loading=5;else{e=u({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Tf.get(i))&&Bf(e,n);var s=a=r.createElement("link");Ye(s),of(s,"link",e),s._p=new Promise((function(e,t){s.onload=e,s.onerror=t})),s.addEventListener("load",(function(){l.loading|=1})),s.addEventListener("error",(function(){l.loading|=2})),l.loading|=4,Uf(a,t,r)}a={type:"stylesheet",instance:a,count:1,state:l},o.set(i,a)}}},M:function(e,t){Of.M(e,t);var n=Lf;if(n&&e){var r=Ke(n).hoistableScripts,o=Mf(e),i=r.get(o);i||((i=n.querySelector(If(o)))||(e=u({src:e,async:!0,type:"module"},t),(t=Tf.get(o))&&Hf(e,t),Ye(i=n.createElement("script")),of(i,"link",e),n.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},r.set(o,i))}}};var Lf="undefined"==typeof document?null:document;function Rf(e,t,n){var r=Lf;if(r&&"string"==typeof t&&t){var o=bt(t);o='link[rel="'+e+'"][href="'+o+'"]',"string"==typeof n&&(o+='[crossorigin="'+n+'"]'),Pf.has(o)||(Pf.add(o),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(o)&&(of(t=r.createElement("link"),"link",e),Ye(t),r.head.appendChild(t)))}}function Af(e,t,n,o){var i,a,l,s,u=(u=K.current)?Nf(u):null;if(!u)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return"string"==typeof n.precedence&&"string"==typeof n.href?(t=jf(n.href),(o=(n=Ke(u).hoistableStyles).get(t))||(o={type:"style",instance:null,count:0,state:null},n.set(t,o)),o):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"==typeof n.href&&"string"==typeof n.precedence){e=jf(n.href);var c=Ke(u).hoistableStyles,f=c.get(e);if(f||(u=u.ownerDocument||u,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,f),(c=u.querySelector(zf(e)))&&!c._p&&(f.instance=c,f.state.loading=5),Tf.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Tf.set(e,n),c||(i=u,a=e,l=n,s=f.state,i.querySelector('link[rel="preload"][as="style"]['+a+"]")?s.loading=1:(a=i.createElement("link"),s.preload=a,a.addEventListener("load",(function(){return s.loading|=1})),a.addEventListener("error",(function(){return s.loading|=2})),of(a,"link",l),Ye(a),i.head.appendChild(a))))),t&&null===o)throw Error(r(528,""));return f}if(t&&null!==o)throw Error(r(529,""));return null;case"script":return t=n.async,"string"==typeof(n=n.src)&&t&&"function"!=typeof t&&"symbol"!=typeof t?(t=Mf(n),(o=(n=Ke(u).hoistableScripts).get(t))||(o={type:"script",instance:null,count:0,state:null},n.set(t,o)),o):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function jf(e){return'href="'+bt(e)+'"'}function zf(e){return'link[rel="stylesheet"]['+e+"]"}function Df(e){return u({},e,{"data-precedence":e.precedence,precedence:null})}function Mf(e){return'[src="'+bt(e)+'"]'}function If(e){return"script[async]"+e}function Ff(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var o=e.querySelector('style[data-href~="'+bt(n.href)+'"]');if(o)return t.instance=o,Ye(o),o;var i=u({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Ye(o=(e.ownerDocument||e).createElement("style")),of(o,"style",i),Uf(o,n.precedence,e),t.instance=o;case"stylesheet":i=jf(n.href);var a=e.querySelector(zf(i));if(a)return t.state.loading|=4,t.instance=a,Ye(a),a;o=Df(n),(i=Tf.get(i))&&Bf(o,i),Ye(a=(e.ownerDocument||e).createElement("link"));var l=a;return l._p=new Promise((function(e,t){l.onload=e,l.onerror=t})),of(a,"link",o),t.state.loading|=4,Uf(a,n.precedence,e),t.instance=a;case"script":return a=Mf(n.src),(i=e.querySelector(If(a)))?(t.instance=i,Ye(i),i):(o=n,(i=Tf.get(a))&&Hf(o=u({},n),i),Ye(i=(e=e.ownerDocument||e).createElement("script")),of(i,"link",o),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(r(443,t.type))}else"stylesheet"===t.type&&!(4&t.state.loading)&&(o=t.instance,t.state.loading|=4,Uf(o,n.precedence,e));return t.instance}function Uf(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=r.length?r[r.length-1]:null,i=o,a=0;a<r.length;a++){var l=r[a];if(l.dataset.precedence===t)i=l;else if(i!==o)break}i?i.parentNode.insertBefore(e,i.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Bf(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Hf(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var $f=null;function Vf(e,t,n){if(null===$f){var r=new Map,o=$f=new Map;o.set(n,r)}else(r=(o=$f).get(n))||(r=new Map,o.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),o=0;o<n.length;o++){var i=n[o];if(!(i[$e]||i[De]||"link"===e&&"stylesheet"===i.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==i.namespaceURI){var a=i.getAttribute(t)||"";a=e+a;var l=r.get(a);l?l.push(i):r.set(a,[i])}}return r}function Wf(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function qf(e){return!!("stylesheet"!==e.type||3&e.state.loading)}var Qf=null;function Kf(){}function Yf(){if(this.count--,0===this.count)if(this.stylesheets)Jf(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Xf=null;function Jf(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Xf=new Map,t.forEach(Gf,e),Xf=null,Yf.call(e))}function Gf(e,t){if(!(4&t.state.loading)){var n=Xf.get(e);if(n)var r=n.get(null);else{n=new Map,Xf.set(e,n);for(var o=e.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<o.length;i++){var a=o[i];"LINK"!==a.nodeName&&"not all"===a.getAttribute("media")||(n.set(a.dataset.precedence,a),r=a)}r&&n.set(null,r)}a=(o=t.instance).getAttribute("data-precedence"),(i=n.get(a)||r)===r&&n.set(null,o),n.set(a,o),this.count++,r=Yf.bind(this),o.addEventListener("load",r),o.addEventListener("error",r),i?i.parentNode.insertBefore(o,i.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(o,e.firstChild),t.state.loading|=4}}var Zf={$$typeof:S,Provider:null,Consumer:null,_currentValue:U,_currentValue2:U,_threadCount:0};function ed(e,t,n,r,o,i,a,l){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Pe(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Pe(0),this.hiddenUpdates=Pe(null),this.identifierPrefix=r,this.onUncaughtError=o,this.onCaughtError=i,this.onRecoverableError=a,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=l,this.incompleteTransitions=new Map}function td(e,t,n,r,o,i,a,l,s,u,c,f){return e=new ed(e,t,n,a,l,s,u,f),t=1,!0===i&&(t|=24),i=Ur(3,null,null,t),e.current=i,i.stateNode=e,(t=Io()).refCount++,e.pooledCache=t,t.refCount++,i.memoizedState={element:r,isDehydrated:n,cache:t},li(i),e}function nd(e){return e?e=Ir:Ir}function rd(e,t,n,r,o,i){o=nd(o),null===r.context?r.context=o:r.pendingContext=o,(r=ui(t)).payload={element:n},null!==(i=void 0===i?null:i)&&(r.callback=i),null!==(n=ci(e,r,t))&&(Uu(n,0,t),fi(n,e,t))}function od(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function id(e,t){od(e,t),(e=e.alternate)&&od(e,t)}function ad(e){if(13===e.tag){var t=zr(e,67108864);null!==t&&Uu(t,0,67108864),id(e,67108864)}}var ld=!0;function sd(e,t,n,r){var o=I.T;I.T=null;var i=F.p;try{F.p=2,cd(e,t,n,r)}finally{F.p=i,I.T=o}}function ud(e,t,n,r){var o=I.T;I.T=null;var i=F.p;try{F.p=8,cd(e,t,n,r)}finally{F.p=i,I.T=o}}function cd(e,t,n,r){if(ld){var o=fd(r);if(null===o)qc(e,t,r,dd,n),Sd(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return gd=Ed(gd,e,t,n,r,o),!0;case"dragenter":return yd=Ed(yd,e,t,n,r,o),!0;case"mouseover":return vd=Ed(vd,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return bd.set(i,Ed(bd.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,kd.set(i,Ed(kd.get(i)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(Sd(e,r),4&t&&-1<xd.indexOf(e)){for(;null!==o;){var i=qe(o);if(null!==i)switch(i.tag){case 3:if((i=i.stateNode).current.memoizedState.isDehydrated){var a=xe(i.pendingLanes);if(0!==a){var l=i;for(l.pendingLanes|=2,l.entangledLanes|=2;a;){var s=1<<31-ye(a);l.entanglements[1]|=s,a&=~s}_c(i),!(6&au)&&(_u=ie()+500,Tc(0))}}break;case 13:null!==(l=zr(i,2))&&Uu(l,0,2),Wu(),id(i,2)}if(null===(i=fd(r))&&qc(e,t,r,dd,n),i===o)break;o=i}null!==o&&r.stopPropagation()}else qc(e,t,r,null,n)}}function fd(e){return pd(e=zt(e))}var dd=null;function pd(e){if(dd=null,null!==(e=We(e))){var t=i(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=a(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return dd=e,null}function hd(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ae()){case le:return 2;case se:return 8;case ue:case ce:return 32;case fe:return 268435456;default:return 32}default:return 32}}var md=!1,gd=null,yd=null,vd=null,bd=new Map,kd=new Map,wd=[],xd="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Sd(e,t){switch(e){case"focusin":case"focusout":gd=null;break;case"dragenter":case"dragleave":yd=null;break;case"mouseover":case"mouseout":vd=null;break;case"pointerover":case"pointerout":bd.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":kd.delete(t.pointerId)}}function Ed(e,t,n,r,o,i){return null===e||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},null!==t&&(null!==(t=qe(t))&&ad(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function Cd(e){var t=We(e.target);if(null!==t){var n=i(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=a(n)))return e.blockedOn=t,void function(e,t){var n=F.p;try{return F.p=e,t()}finally{F.p=n}}(e.priority,(function(){if(13===n.tag){var e=Iu();e=Re(e);var t=zr(n,e);null!==t&&Uu(t,0,e),id(n,e)}}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function _d(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=fd(e.nativeEvent);if(null!==n)return null!==(t=qe(n))&&ad(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);jt=r,n.target.dispatchEvent(r),jt=null,t.shift()}return!0}function Td(e,t,n){_d(e)&&n.delete(t)}function Pd(){md=!1,null!==gd&&_d(gd)&&(gd=null),null!==yd&&_d(yd)&&(yd=null),null!==vd&&_d(vd)&&(vd=null),bd.forEach(Td),kd.forEach(Td)}function Nd(t,n){t.blockedOn===n&&(t.blockedOn=null,md||(md=!0,e.unstable_scheduleCallback(e.unstable_NormalPriority,Pd)))}var Od=null;function Ld(t){Od!==t&&(Od=t,e.unstable_scheduleCallback(e.unstable_NormalPriority,(function(){Od===t&&(Od=null);for(var e=0;e<t.length;e+=3){var n=t[e],r=t[e+1],o=t[e+2];if("function"!=typeof r){if(null===pd(r||n))continue;break}var i=qe(n);null!==i&&(t.splice(e,3),e-=3,za(i,{pending:!0,data:o,method:n.method,action:r},r,o))}})))}function Rd(e){function t(t){return Nd(t,e)}null!==gd&&Nd(gd,e),null!==yd&&Nd(yd,e),null!==vd&&Nd(vd,e),bd.forEach(t),kd.forEach(t);for(var n=0;n<wd.length;n++){var r=wd[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<wd.length&&null===(n=wd[0]).blockedOn;)Cd(n),null===n.blockedOn&&wd.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var o=n[r],i=n[r+1],a=o[Me]||null;if("function"==typeof i)a||Ld(n);else if(a){var l=null;if(i&&i.hasAttribute("formAction")){if(o=i,a=i[Me]||null)l=a.formAction;else if(null!==pd(o))continue}else l=a.action;"function"==typeof l?n[r+1]=l:(n.splice(r,3),r-=3),Ld(n)}}}function Ad(e){this._internalRoot=e}function jd(e){this._internalRoot=e}jd.prototype.render=Ad.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(r(409));rd(t.current,Iu(),e,t,null,null)},jd.prototype.unmount=Ad.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;rd(e.current,2,null,e,null,null),Wu(),t[Ie]=null}},jd.prototype.unstable_scheduleHydration=function(e){if(e){var t=je();e={blockedOn:null,target:e,priority:t};for(var n=0;n<wd.length&&0!==t&&t<wd[n].priority;n++);wd.splice(n,0,e),0===n&&Cd(e)}};var zd=t.version;if("19.1.0"!==zd)throw Error(r(527,zd,"19.1.0"));F.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(r(188));throw e=Object.keys(e).join(","),Error(r(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=i(e)))throw Error(r(188));return t!==e?null:e}for(var n=e,o=t;;){var a=n.return;if(null===a)break;var s=a.alternate;if(null===s){if(null!==(o=a.return)){n=o;continue}break}if(a.child===s.child){for(s=a.child;s;){if(s===n)return l(a),e;if(s===o)return l(a),t;s=s.sibling}throw Error(r(188))}if(n.return!==o.return)n=a,o=s;else{for(var u=!1,c=a.child;c;){if(c===n){u=!0,n=a,o=s;break}if(c===o){u=!0,o=a,n=s;break}c=c.sibling}if(!u){for(c=s.child;c;){if(c===n){u=!0,n=s,o=a;break}if(c===o){u=!0,o=s,n=a;break}c=c.sibling}if(!u)throw Error(r(189))}}if(n.alternate!==o)throw Error(r(190))}if(3!==n.tag)throw Error(r(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?s(e):null)?null:e.stateNode};var Dd={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:I,reconcilerVersion:"19.1.0"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Md=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Md.isDisabled&&Md.supportsFiber)try{he=Md.inject(Dd),me=Md}catch(Fd){}}return g.createRoot=function(e,t){if(!o(e))throw Error(r(299));var n=!1,i="",a=xl,l=Sl,s=El;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(i=t.identifierPrefix),void 0!==t.onUncaughtError&&(a=t.onUncaughtError),void 0!==t.onCaughtError&&(l=t.onCaughtError),void 0!==t.onRecoverableError&&(s=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=td(e,1,!1,null,0,n,i,a,l,s,0,null),e[Ie]=t.current,Vc(e),new Ad(t)},g.hydrateRoot=function(e,t,n){if(!o(e))throw Error(r(299));var i=!1,a="",l=xl,s=Sl,u=El,c=null;return null!=n&&(!0===n.unstable_strictMode&&(i=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onUncaughtError&&(l=n.onUncaughtError),void 0!==n.onCaughtError&&(s=n.onCaughtError),void 0!==n.onRecoverableError&&(u=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(c=n.formState)),(t=td(e,1,!0,t,0,i,a,l,s,u,0,c)).context=nd(null),n=t.current,(a=ui(i=Re(i=Iu()))).callback=null,ci(n,a,i),n=i,t.current.lanes=n,Ne(t,n),_c(t),e[Ie]=t.current,Vc(e),new jd(t)},g.version="19.1.0",g}var P,N,O=(S||(S=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){}}(),m.exports=T()),m.exports),L={exports:{}},R={};var A=(N||(N=1,L.exports=function(){if(P)return R;P=1;var e=f(),t="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},n=e.useSyncExternalStore,r=e.useRef,o=e.useEffect,i=e.useMemo,a=e.useDebugValue;return R.useSyncExternalStoreWithSelector=function(e,l,s,u,c){var f=r(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;f=i((function(){function e(e){if(!o){if(o=!0,n=e,e=u(e),void 0!==c&&d.hasValue){var i=d.value;if(c(i,e))return r=i}return r=e}if(i=r,t(n,e))return i;var a=u(e);return void 0!==c&&c(i,a)?(n=e,i):(n=e,r=a)}var n,r,o=!1,i=void 0===s?null:s;return[function(){return e(l())},null===i?void 0:function(){return e(i())}]}),[l,s,u,c]);var p=n(e,f[0],f[1]);return o((function(){d.hasValue=!0,d.value=p}),[p]),a(p),p},R}()),L.exports);var j={notify(){},get:()=>[]};var z=(()=>!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement))(),D=(()=>"undefined"!=typeof navigator&&"ReactNative"===navigator.product)(),M=(()=>z||D?h.useLayoutEffect:h.useEffect)(),I=Symbol.for("react-redux-context"),F="undefined"!=typeof globalThis?globalThis:{};function U(){if(!h.createContext)return{};const e=F[I]??(F[I]=new Map);let t=e.get(h.createContext);return t||(t=h.createContext(null),e.set(h.createContext,t)),t}var B=U();var H=function(e){const{children:t,context:n,serverState:r,store:o}=e,i=h.useMemo((()=>{const e=function(e){let t,n=j,r=0,o=!1;function i(){s.onStateChange&&s.onStateChange()}function a(){r++,t||(t=e.subscribe(i),n=function(){let e=null,t=null;return{clear(){e=null,t=null},notify(){(()=>{let t=e;for(;t;)t.callback(),t=t.next})()},get(){const t=[];let n=e;for(;n;)t.push(n),n=n.next;return t},subscribe(n){let r=!0;const o=t={callback:n,next:null,prev:t};return o.prev?o.prev.next=o:e=o,function(){r&&null!==e&&(r=!1,o.next?o.next.prev=o.prev:t=o.prev,o.prev?o.prev.next=o.next:e=o.next)}}}}())}function l(){r--,t&&0===r&&(t(),t=void 0,n.clear(),n=j)}const s={addNestedSub:function(e){a();const t=n.subscribe(e);let r=!1;return()=>{r||(r=!0,t(),l())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:i,isSubscribed:function(){return o},trySubscribe:function(){o||(o=!0,a())},tryUnsubscribe:function(){o&&(o=!1,l())},getListeners:()=>n};return s}(o);return{store:o,subscription:e,getServerState:r?()=>r:void 0}}),[o,r]),a=h.useMemo((()=>o.getState()),[o]);M((()=>{const{subscription:e}=i;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),a!==o.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}}),[i,a]);const l=n||B;return h.createElement(l.Provider,{value:i},t)};function $(e=B){return function(){return h.useContext(e)}}var V=$();function W(e=B){const t=e===B?V:$(e),n=()=>{const{store:e}=t();return e};return Object.assign(n,{withTypes:()=>n}),n}var q=W();function Q(e=B){const t=e===B?q:W(e),n=()=>t().dispatch;return Object.assign(n,{withTypes:()=>n}),n}var K=Q(),Y=(e,t)=>e===t;function X(e=B){const t=e===B?V:$(e),n=(e,n={})=>{const{equalityFn:r=Y}="function"==typeof n?{equalityFn:n}:n,o=t(),{store:i,subscription:a,getServerState:l}=o;h.useRef(!0);const s=h.useCallback({[e.name]:t=>e(t)}[e.name],[e]),u=A.useSyncExternalStoreWithSelector(a.addNestedSub,i.getState,l||i.getState,s,r);return h.useDebugValue(u),u};return Object.assign(n,{withTypes:()=>n}),n}var J=X();function G(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var Z=(()=>"function"==typeof Symbol&&Symbol.observable||"@@observable")(),ee=()=>Math.random().toString(36).substring(7).split("").join("."),te={INIT:`@@redux/INIT${ee()}`,REPLACE:`@@redux/REPLACE${ee()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${ee()}`};function ne(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function re(e,t,n){if("function"!=typeof e)throw new Error(G(2));if("function"==typeof t&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw new Error(G(0));if("function"==typeof t&&void 0===n&&(n=t,t=void 0),void 0!==n){if("function"!=typeof n)throw new Error(G(1));return n(re)(e,t)}let r=e,o=t,i=new Map,a=i,l=0,s=!1;function u(){a===i&&(a=new Map,i.forEach(((e,t)=>{a.set(t,e)})))}function c(){if(s)throw new Error(G(3));return o}function f(e){if("function"!=typeof e)throw new Error(G(4));if(s)throw new Error(G(5));let t=!0;u();const n=l++;return a.set(n,e),function(){if(t){if(s)throw new Error(G(6));t=!1,u(),a.delete(n),i=null}}}function d(e){if(!ne(e))throw new Error(G(7));if(void 0===e.type)throw new Error(G(8));if("string"!=typeof e.type)throw new Error(G(17));if(s)throw new Error(G(9));try{s=!0,o=r(o,e)}finally{s=!1}return(i=a).forEach((e=>{e()})),e}d({type:te.INIT});return{dispatch:d,subscribe:f,getState:c,replaceReducer:function(e){if("function"!=typeof e)throw new Error(G(10));r=e,d({type:te.REPLACE})},[Z]:function(){const e=f;return{subscribe(t){if("object"!=typeof t||null===t)throw new Error(G(11));function n(){const e=t;e.next&&e.next(c())}n();return{unsubscribe:e(n)}},[Z](){return this}}}}}function oe(e){const t=Object.keys(e),n={};for(let a=0;a<t.length;a++){const r=t[a];"function"==typeof e[r]&&(n[r]=e[r])}const r=Object.keys(n);let o;try{!function(e){Object.keys(e).forEach((t=>{const n=e[t];if(void 0===n(void 0,{type:te.INIT}))throw new Error(G(12));if(void 0===n(void 0,{type:te.PROBE_UNKNOWN_ACTION()}))throw new Error(G(13))}))}(n)}catch(i){o=i}return function(e={},t){if(o)throw o;let i=!1;const a={};for(let o=0;o<r.length;o++){const l=r[o],s=n[l],u=e[l],c=s(u,t);if(void 0===c)throw t&&t.type,new Error(G(14));a[l]=c,i=i||c!==u}return i=i||r.length!==Object.keys(e).length,i?a:e}}function ie(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce(((e,t)=>(...n)=>e(t(...n))))}var ae=Symbol.for("immer-nothing"),le=Symbol.for("immer-draftable"),se=Symbol.for("immer-state");function ue(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var ce=Object.getPrototypeOf;function fe(e){return!!e&&!!e[se]}function de(e){var t;return!!e&&(he(e)||Array.isArray(e)||!!e[le]||!!(null==(t=e.constructor)?void 0:t[le])||be(e)||ke(e))}var pe=Object.prototype.constructor.toString();function he(e){if(!e||"object"!=typeof e)return!1;const t=ce(e);if(null===t)return!0;const n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===pe}function me(e,t){0===ge(e)?Reflect.ownKeys(e).forEach((n=>{t(n,e[n],e)})):e.forEach(((n,r)=>t(r,n,e)))}function ge(e){const t=e[se];return t?t.type_:Array.isArray(e)?1:be(e)?2:ke(e)?3:0}function ye(e,t){return 2===ge(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function ve(e,t,n){const r=ge(e);2===r?e.set(t,n):3===r?e.add(n):e[t]=n}function be(e){return e instanceof Map}function ke(e){return e instanceof Set}function we(e){return e.copy_||e.base_}function xe(e,t){if(be(e))return new Map(e);if(ke(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const n=he(e);if(!0===t||"class_only"===t&&!n){const t=Object.getOwnPropertyDescriptors(e);delete t[se];let n=Reflect.ownKeys(t);for(let r=0;r<n.length;r++){const o=n[r],i=t[o];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(t[o]={configurable:!0,writable:!0,enumerable:i.enumerable,value:e[o]})}return Object.create(ce(e),t)}{const t=ce(e);if(null!==t&&n)return{...e};const r=Object.create(t);return Object.assign(r,e)}}function Se(e,t=!1){return Ce(e)||fe(e)||!de(e)||(ge(e)>1&&(e.set=e.add=e.clear=e.delete=Ee),Object.freeze(e),t&&Object.entries(e).forEach((([e,t])=>Se(t,!0)))),e}function Ee(){ue(2)}function Ce(e){return Object.isFrozen(e)}var _e,Te={};function Pe(e){const t=Te[e];return t||ue(0),t}function Ne(){return _e}function Oe(e,t){t&&(Pe("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function Le(e){Re(e),e.drafts_.forEach(je),e.drafts_=null}function Re(e){e===_e&&(_e=e.parent_)}function Ae(e){return _e={drafts_:[],parent_:_e,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function je(e){const t=e[se];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function ze(e,t){t.unfinalizedDrafts_=t.drafts_.length;const n=t.drafts_[0];return void 0!==e&&e!==n?(n[se].modified_&&(Le(t),ue(4)),de(e)&&(e=De(t,e),t.parent_||Ie(t,e)),t.patches_&&Pe("Patches").generateReplacementPatches_(n[se].base_,e,t.patches_,t.inversePatches_)):e=De(t,n,[]),Le(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==ae?e:void 0}function De(e,t,n){if(Ce(t))return t;const r=t[se];if(!r)return me(t,((o,i)=>Me(e,r,t,o,i,n))),t;if(r.scope_!==e)return t;if(!r.modified_)return Ie(e,r.base_,!0),r.base_;if(!r.finalized_){r.finalized_=!0,r.scope_.unfinalizedDrafts_--;const t=r.copy_;let o=t,i=!1;3===r.type_&&(o=new Set(t),t.clear(),i=!0),me(o,((o,a)=>Me(e,r,t,o,a,n,i))),Ie(e,t,!1),n&&e.patches_&&Pe("Patches").generatePatches_(r,n,e.patches_,e.inversePatches_)}return r.copy_}function Me(e,t,n,r,o,i,a){if(fe(o)){const a=De(e,o,i&&t&&3!==t.type_&&!ye(t.assigned_,r)?i.concat(r):void 0);if(ve(n,r,a),!fe(a))return;e.canAutoFreeze_=!1}else a&&n.add(o);if(de(o)&&!Ce(o)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;De(e,o),t&&t.scope_.parent_||"symbol"==typeof r||!Object.prototype.propertyIsEnumerable.call(n,r)||Ie(e,o)}}function Ie(e,t,n=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&Se(t,n)}var Fe={get(e,t){if(t===se)return e;const n=we(e);if(!ye(n,t))return function(e,t,n){var r;const o=He(t,n);return o?"value"in o?o.value:null==(r=o.get)?void 0:r.call(e.draft_):void 0}(e,n,t);const r=n[t];return e.finalized_||!de(r)?r:r===Be(e.base_,t)?(Ve(e),e.copy_[t]=We(r,e)):r},has:(e,t)=>t in we(e),ownKeys:e=>Reflect.ownKeys(we(e)),set(e,t,n){const r=He(we(e),t);if(null==r?void 0:r.set)return r.set.call(e.draft_,n),!0;if(!e.modified_){const r=Be(we(e),t),a=null==r?void 0:r[se];if(a&&a.base_===n)return e.copy_[t]=n,e.assigned_[t]=!1,!0;if(((o=n)===(i=r)?0!==o||1/o==1/i:o!=o&&i!=i)&&(void 0!==n||ye(e.base_,t)))return!0;Ve(e),$e(e)}var o,i;return e.copy_[t]===n&&(void 0!==n||t in e.copy_)||Number.isNaN(n)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=n,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==Be(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,Ve(e),$e(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const n=we(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty(){ue(11)},getPrototypeOf:e=>ce(e.base_),setPrototypeOf(){ue(12)}},Ue={};function Be(e,t){const n=e[se];return(n?we(n):e)[t]}function He(e,t){if(!(t in e))return;let n=ce(e);for(;n;){const e=Object.getOwnPropertyDescriptor(n,t);if(e)return e;n=ce(n)}}function $e(e){e.modified_||(e.modified_=!0,e.parent_&&$e(e.parent_))}function Ve(e){e.copy_||(e.copy_=xe(e.base_,e.scope_.immer_.useStrictShallowCopy_))}me(Fe,((e,t)=>{Ue[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),Ue.deleteProperty=function(e,t){return Ue.set.call(this,e,t,void 0)},Ue.set=function(e,t,n){return Fe.set.call(this,e[0],t,n,e[0])};function We(e,t){const n=be(e)?Pe("MapSet").proxyMap_(e,t):ke(e)?Pe("MapSet").proxySet_(e,t):function(e,t){const n=Array.isArray(e),r={type_:n?1:0,scope_:t?t.scope_:Ne(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let o=r,i=Fe;n&&(o=[r],i=Ue);const{revoke:a,proxy:l}=Proxy.revocable(o,i);return r.draft_=l,r.revoke_=a,l}(e,t);return(t?t.scope_:Ne()).drafts_.push(n),n}function qe(e){if(!de(e)||Ce(e))return e;const t=e[se];let n;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,n=xe(e,t.scope_.immer_.useStrictShallowCopy_)}else n=xe(e,!0);return me(n,((e,t)=>{ve(n,e,qe(t))})),t&&(t.finalized_=!1),n}var Qe=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,n)=>{if("function"==typeof e&&"function"!=typeof t){const n=t;t=e;const r=this;return function(e=n,...o){return r.produce(e,(e=>t.call(this,e,...o)))}}let r;if("function"!=typeof t&&ue(6),void 0!==n&&"function"!=typeof n&&ue(7),de(e)){const o=Ae(this),i=We(e,void 0);let a=!0;try{r=t(i),a=!1}finally{a?Le(o):Re(o)}return Oe(o,n),ze(r,o)}if(!e||"object"!=typeof e){if(r=t(e),void 0===r&&(r=e),r===ae&&(r=void 0),this.autoFreeze_&&Se(r,!0),n){const t=[],o=[];Pe("Patches").generateReplacementPatches_(e,r,t,o),n(t,o)}return r}ue(1)},this.produceWithPatches=(e,t)=>{if("function"==typeof e)return(t,...n)=>this.produceWithPatches(t,(t=>e(t,...n)));let n,r;return[this.produce(e,t,((e,t)=>{n=e,r=t})),n,r]},"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof(null==e?void 0:e.useStrictShallowCopy)&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){de(e)||ue(8),fe(e)&&(e=function(e){fe(e)||ue(10);return qe(e)}(e));const t=Ae(this),n=We(e,void 0);return n[se].isManual_=!0,Re(t),n}finishDraft(e,t){const n=e&&e[se];n&&n.isManual_||ue(9);const{scope_:r}=n;return Oe(r,t),ze(void 0,r)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let n;for(n=t.length-1;n>=0;n--){const r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));const r=Pe("Patches").applyPatches_;return fe(e)?r(e,t):this.produce(e,(e=>r(e,t)))}},Ke=Qe.produce;function Ye(e){return({dispatch:t,getState:n})=>r=>o=>"function"==typeof o?o(t,n,e):r(o)}Qe.produceWithPatches.bind(Qe),Qe.setAutoFreeze.bind(Qe),Qe.setUseStrictShallowCopy.bind(Qe),Qe.applyPatches.bind(Qe),Qe.createDraft.bind(Qe),Qe.finishDraft.bind(Qe);var Xe=Ye(),Je=Ye,Ge="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?ie:ie.apply(null,arguments)};function Ze(e,t){function n(...n){if(t){let r=t(...n);if(!r)throw new Error(ft(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return n.toString=()=>`${e}`,n.type=e,n.match=t=>function(e){return ne(e)&&"type"in e&&"string"==typeof e.type}(t)&&t.type===e,n}var et=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function tt(e){return de(e)?Ke(e,(()=>{})):e}function nt(e,t,n){return e.has(t)?e.get(t):e.set(t,n(t)).get(t)}var rt=e=>t=>{setTimeout(t,e)},ot=e=>function(t){const{autoBatch:n=!0}=t??{};let r=new et(e);return n&&r.push(((e={type:"raf"})=>t=>(...n)=>{const r=t(...n);let o=!0,i=!1,a=!1;const l=new Set,s="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:rt(10):"callback"===e.type?e.queueNotification:rt(e.timeout),u=()=>{a=!1,i&&(i=!1,l.forEach((e=>e())))};return Object.assign({},r,{subscribe(e){const t=r.subscribe((()=>o&&e()));return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){var t;try{return o=!(null==(t=null==e?void 0:e.meta)?void 0:t.RTK_autoBatch),i=!o,i&&(a||(a=!0,s(u))),r.dispatch(e)}finally{o=!0}}})})("object"==typeof n?n:void 0)),r};function it(e){const t={},n=[];let r;const o={addCase(e,n){const r="string"==typeof e?e:e.type;if(!r)throw new Error(ft(28));if(r in t)throw new Error(ft(29));return t[r]=n,o},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),o),addDefaultCase:e=>(r=e,o)};return e(o),[t,n,r]}var at=Symbol.for("rtk-slice-createasyncthunk");function lt(e,t){return`${e}/${t}`}function st({creators:e}={}){var t;const n=null==(t=null==e?void 0:e.asyncThunk)?void 0:t[at];return function(e){const{name:t,reducerPath:r=t}=e;if(!t)throw new Error(ft(11));const o=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},i=Object.keys(o),a={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},l={addCase(e,t){const n="string"==typeof e?e:e.type;if(!n)throw new Error(ft(12));if(n in a.sliceCaseReducersByType)throw new Error(ft(13));return a.sliceCaseReducersByType[n]=t,l},addMatcher:(e,t)=>(a.sliceMatchers.push({matcher:e,reducer:t}),l),exposeAction:(e,t)=>(a.actionCreators[e]=t,l),exposeCaseReducer:(e,t)=>(a.sliceCaseReducersByName[e]=t,l)};function s(){const[t={},n=[],r]="function"==typeof e.extraReducers?it(e.extraReducers):[e.extraReducers],o={...t,...a.sliceCaseReducersByType};return function(e,t){let n,[r,o,i]=it(t);if("function"==typeof e)n=()=>tt(e());else{const t=tt(e);n=()=>t}function a(e=n(),t){let a=[r[t.type],...o.filter((({matcher:e})=>e(t))).map((({reducer:e})=>e))];return 0===a.filter((e=>!!e)).length&&(a=[i]),a.reduce(((e,n)=>{if(n){if(fe(e)){const r=n(e,t);return void 0===r?e:r}if(de(e))return Ke(e,(e=>n(e,t)));{const r=n(e,t);if(void 0===r){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return r}}return e}),e)}return a.getInitialState=n,a}(e.initialState,(e=>{for(let t in o)e.addCase(t,o[t]);for(let t of a.sliceMatchers)e.addMatcher(t.matcher,t.reducer);for(let t of n)e.addMatcher(t.matcher,t.reducer);r&&e.addDefaultCase(r)}))}i.forEach((r=>{const i=o[r],a={reducerName:r,type:lt(t,r),createNotation:"function"==typeof e.reducers};!function(e){return"asyncThunk"===e._reducerDefinitionType}(i)?function({type:e,reducerName:t,createNotation:n},r,o){let i,a;if("reducer"in r){if(n&&!function(e){return"reducerWithPrepare"===e._reducerDefinitionType}(r))throw new Error(ft(17));i=r.reducer,a=r.prepare}else i=r;o.addCase(e,i).exposeCaseReducer(t,i).exposeAction(t,a?Ze(e,a):Ze(e))}(a,i,l):function({type:e,reducerName:t},n,r,o){if(!o)throw new Error(ft(18));const{payloadCreator:i,fulfilled:a,pending:l,rejected:s,settled:u,options:c}=n,f=o(e,i,c);r.exposeAction(t,f),a&&r.addCase(f.fulfilled,a);l&&r.addCase(f.pending,l);s&&r.addCase(f.rejected,s);u&&r.addMatcher(f.settled,u);r.exposeCaseReducer(t,{fulfilled:a||ct,pending:l||ct,rejected:s||ct,settled:u||ct})}(a,i,l,n)}));const u=e=>e,c=new Map,f=new WeakMap;let d;function p(e,t){return d||(d=s()),d(e,t)}function h(){return d||(d=s()),d.getInitialState()}function m(t,n=!1){function r(e){let o=e[t];return void 0===o&&n&&(o=nt(f,r,h)),o}function o(t=u){const r=nt(c,n,(()=>new WeakMap));return nt(r,t,(()=>{const r={};for(const[o,i]of Object.entries(e.selectors??{}))r[o]=ut(i,t,(()=>nt(f,t,h)),n);return r}))}return{reducerPath:t,getSelectors:o,get selectors(){return o(r)},selectSlice:r}}const g={name:t,reducer:p,actions:a.actionCreators,caseReducers:a.sliceCaseReducersByName,getInitialState:h,...m(r),injectInto(e,{reducerPath:t,...n}={}){const o=t??r;return e.inject({reducerPath:o,reducer:p},n),{...g,...m(o,!0)}}};return g}}function ut(e,t,n,r){function o(o,...i){let a=t(o);return void 0===a&&r&&(a=n()),e(a,...i)}return o.unwrapped=e,o}function ct(){}function ft(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}const dt=st()({name:"auth",initialState:{accessToken:localStorage.getItem("access_token")||null,refreshToken:localStorage.getItem("refresh_token")||null,sessionId:localStorage.getItem("session_id")||null,userId:localStorage.getItem("user_id")||null,tokenExpiry:localStorage.getItem("token_expiry")||null,isLoading:!1,error:null},reducers:{loginStart:e=>{e.isLoading=!0,e.error=null},loginSuccess:(e,t)=>{const{access_token:n,refresh_token:r,session_id:o,user_id:i,expires_in:a}=t.payload;e.accessToken=n,e.refreshToken=r,e.sessionId=o,e.userId=i,e.tokenExpiry=Date.now()+1e3*a,e.isLoading=!1,e.error=null,localStorage.setItem("access_token",n),localStorage.setItem("refresh_token",r),o&&localStorage.setItem("session_id",o),i&&localStorage.setItem("user_id",i),localStorage.setItem("token_expiry",e.tokenExpiry)},loginFailure:(e,t)=>{e.isLoading=!1,e.error=t.payload},logout:e=>{e.accessToken=null,e.refreshToken=null,e.sessionId=null,e.userId=null,e.tokenExpiry=null,localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("session_id"),localStorage.removeItem("user_id"),localStorage.removeItem("token_expiry")},tokenRefreshed:(e,t)=>{const{access_token:n,expires_in:r}=t.payload;e.accessToken=n,e.tokenExpiry=Date.now()+1e3*r,localStorage.setItem("access_token",n),localStorage.setItem("token_expiry",e.tokenExpiry)}}}),{loginStart:pt,loginSuccess:ht,loginFailure:mt,logout:gt,tokenRefreshed:yt}=dt.actions,vt=function(e){const t=function(e){const{thunk:t=!0,immutableCheck:n=!0,serializableCheck:r=!0,actionCreatorCheck:o=!0}=e??{};let i=new et;return t&&("boolean"==typeof t?i.push(Xe):i.push(Je(t.extraArgument))),i},{reducer:n,middleware:r,devTools:o=!0,preloadedState:i,enhancers:a}=e||{};let l,s;if("function"==typeof n)l=n;else{if(!ne(n))throw new Error(ft(1));l=oe(n)}s="function"==typeof r?r(t):t();let u=ie;o&&(u=Ge({trace:!1,..."object"==typeof o&&o}));const c=function(...e){return t=>(n,r)=>{const o=t(n,r);let i=()=>{throw new Error(G(15))};const a={getState:o.getState,dispatch:(e,...t)=>i(e,...t)},l=e.map((e=>e(a)));return i=ie(...l)(o.dispatch),{...o,dispatch:i}}}(...s),f=ot(c);return re(l,i,u(..."function"==typeof a?a(f):f()))}({reducer:{auth:dt.reducer},middleware:e=>e({serializableCheck:!1})});var bt,kt={};!function(){if(bt)return kt;bt=1,Object.defineProperty(kt,"__esModule",{value:!0}),kt.parse=function(e,t){const n=new i,r=e.length;if(r<2)return n;const o=(null==t?void 0:t.decode)||s;let u=0;do{const t=e.indexOf("=",u);if(-1===t)break;const i=e.indexOf(";",u),s=-1===i?r:i;if(t>s){u=e.lastIndexOf(";",t-1)+1;continue}const c=a(e,u,t),f=l(e,t,c),d=e.slice(c,f);if(void 0===n[d]){let r=a(e,t+1,s),i=l(e,s,r);const u=o(e.slice(r,i));n[d]=u}u=s+1}while(u<r);return n},kt.serialize=function(i,a,l){const s=(null==l?void 0:l.encode)||encodeURIComponent;if(!e.test(i))throw new TypeError(`argument name is invalid: ${i}`);const u=s(a);if(!t.test(u))throw new TypeError(`argument val is invalid: ${a}`);let c=i+"="+u;if(!l)return c;if(void 0!==l.maxAge){if(!Number.isInteger(l.maxAge))throw new TypeError(`option maxAge is invalid: ${l.maxAge}`);c+="; Max-Age="+l.maxAge}if(l.domain){if(!n.test(l.domain))throw new TypeError(`option domain is invalid: ${l.domain}`);c+="; Domain="+l.domain}if(l.path){if(!r.test(l.path))throw new TypeError(`option path is invalid: ${l.path}`);c+="; Path="+l.path}if(l.expires){if(!function(e){return"[object Date]"===o.call(e)}(l.expires)||!Number.isFinite(l.expires.valueOf()))throw new TypeError(`option expires is invalid: ${l.expires}`);c+="; Expires="+l.expires.toUTCString()}l.httpOnly&&(c+="; HttpOnly");l.secure&&(c+="; Secure");l.partitioned&&(c+="; Partitioned");if(l.priority){switch("string"==typeof l.priority?l.priority.toLowerCase():void 0){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${l.priority}`)}}if(l.sameSite){switch("string"==typeof l.sameSite?l.sameSite.toLowerCase():l.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${l.sameSite}`)}}return c};const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,n=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,r=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,i=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function a(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function l(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function s(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}}();var wt="popstate";function xt(e={}){return function(e,t,n,r={}){let{window:o=document.defaultView,v5Compat:i=!1}=r,a=o.history,l="POP",s=null,u=c();null==u&&(u=0,a.replaceState({...a.state,idx:u},""));function c(){return(a.state||{idx:null}).idx}function f(){l="POP";let e=c(),t=null==e?null:e-u;u=e,s&&s({action:l,location:m.location,delta:t})}function d(e,t){l="PUSH";let r=_t(m.location,e,t);n&&n(r,e),u=c()+1;let f=Ct(r,u),d=m.createHref(r);try{a.pushState(f,"",d)}catch(p){if(p instanceof DOMException&&"DataCloneError"===p.name)throw p;o.location.assign(d)}i&&s&&s({action:l,location:m.location,delta:1})}function p(e,t){l="REPLACE";let r=_t(m.location,e,t);n&&n(r,e),u=c();let o=Ct(r,u),f=m.createHref(r);a.replaceState(o,"",f),i&&s&&s({action:l,location:m.location,delta:0})}function h(e){return function(e,t=!1){let n="http://localhost";"undefined"!=typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href);St(n,"No window.location.(origin|href) available to create URL");let r="string"==typeof e?e:Tt(e);r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r);return new URL(r,n)}(e)}let m={get action(){return l},get location(){return e(o,a)},listen(e){if(s)throw new Error("A history only accepts one active listener");return o.addEventListener(wt,f),s=e,()=>{o.removeEventListener(wt,f),s=null}},createHref:e=>t(o,e),createURL:h,encodeLocation(e){let t=h(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:d,replace:p,go:e=>a.go(e)};return m}((function(e,t){let{pathname:n="/",search:r="",hash:o=""}=Pt(e.location.hash.substring(1));return n.startsWith("/")||n.startsWith(".")||(n="/"+n),_t("",{pathname:n,search:r,hash:o},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){let n=e.document.querySelector("base"),r="";if(n&&n.getAttribute("href")){let t=e.location.href,n=t.indexOf("#");r=-1===n?t:t.slice(0,n)}return r+"#"+("string"==typeof t?t:Tt(t))}),(function(e,t){Et("/"===e.pathname.charAt(0),`relative pathnames are not supported in hash history.push(${JSON.stringify(t)})`)}),e)}function St(e,t){if(!1===e||null==e)throw new Error(t)}function Et(e,t){if(!e)try{throw new Error(t)}catch(n){}}function Ct(e,t){return{usr:e.state,key:e.key,idx:t}}function _t(e,t,n=null,r){return{pathname:"string"==typeof e?e:e.pathname,search:"",hash:"",..."string"==typeof t?Pt(t):t,state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)}}function Tt({pathname:e="/",search:t="",hash:n=""}){return t&&"?"!==t&&(e+="?"===t.charAt(0)?t:"?"+t),n&&"#"!==n&&(e+="#"===n.charAt(0)?n:"#"+n),e}function Pt(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function Nt(e,t,n="/"){return function(e,t,n,r){let o="string"==typeof t?Pt(t):t,i=$t(o.pathname||"/",n);if(null==i)return null;let a=Ot(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(a);let l=null;for(let s=0;null==l&&s<a.length;++s){let e=Ht(i);l=Ut(a[s],e,r)}return l}(e,t,n,!1)}function Ot(e,t=[],n=[],r=""){let o=(e,o,i)=>{let a={relativePath:void 0===i?e.path||"":i,caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};a.relativePath.startsWith("/")&&(St(a.relativePath.startsWith(r),`Absolute route path "${a.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),a.relativePath=a.relativePath.slice(r.length));let l=Qt([r,a.relativePath]),s=n.concat(a);e.children&&e.children.length>0&&(St(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${l}".`),Ot(e.children,t,s,l)),(null!=e.path||e.index)&&t.push({path:l,score:Ft(l,e.index),routesMeta:s})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&(null==(n=e.path)?void 0:n.includes("?")))for(let r of Lt(e.path))o(e,t,r);else o(e,t)})),t}function Lt(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(0===r.length)return o?[i,""]:[i];let a=Lt(r.join("/")),l=[];return l.push(...a.map((e=>""===e?i:[i,e].join("/")))),o&&l.push(...a),l.map((t=>e.startsWith("/")&&""===t?"/":t))}var Rt=/^:[\w-]+$/,At=3,jt=2,zt=1,Dt=10,Mt=-2,It=e=>"*"===e;function Ft(e,t){let n=e.split("/"),r=n.length;return n.some(It)&&(r+=Mt),t&&(r+=jt),n.filter((e=>!It(e))).reduce(((e,t)=>e+(Rt.test(t)?At:""===t?zt:Dt)),r)}function Ut(e,t,n=!1){let{routesMeta:r}=e,o={},i="/",a=[];for(let l=0;l<r.length;++l){let e=r[l],s=l===r.length-1,u="/"===i?t:t.slice(i.length)||"/",c=Bt({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},u),f=e.route;if(!c&&s&&n&&!r[r.length-1].route.index&&(c=Bt({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(o,c.params),a.push({params:o,pathname:Qt([i,c.pathname]),pathnameBase:Kt(Qt([i,c.pathnameBase])),route:f}),"/"!==c.pathnameBase&&(i=Qt([i,c.pathnameBase]))}return a}function Bt(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t=!1,n=!0){Et("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),o+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":""!==e&&"/"!==e&&(o+="(?:(?=\\/|$))");let i=new RegExp(o,t?void 0:"i");return[i,r]}(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],a=i.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce(((e,{paramName:t,isOptional:n},r)=>{if("*"===t){let e=l[r]||"";a=i.slice(0,i.length-e.length).replace(/(.)\/+$/,"$1")}const o=l[r];return e[t]=n&&!o?void 0:(o||"").replace(/%2F/g,"/"),e}),{}),pathname:i,pathnameBase:a,pattern:e}}function Ht(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return Et(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function $t(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function Vt(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Wt(e){let t=function(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}(e);return t.map(((e,n)=>n===t.length-1?e.pathname:e.pathnameBase))}function qt(e,t,n,r=!1){let o;"string"==typeof e?o=Pt(e):(o={...e},St(!o.pathname||!o.pathname.includes("?"),Vt("?","pathname","search",o)),St(!o.pathname||!o.pathname.includes("#"),Vt("#","pathname","hash",o)),St(!o.search||!o.search.includes("#"),Vt("#","search","hash",o)));let i,a=""===e||""===o.pathname,l=a?"/":o.pathname;if(null==l)i=n;else{let e=t.length-1;if(!r&&l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e-=1;o.pathname=t.join("/")}i=e>=0?t[e]:"/"}let s=function(e,t="/"){let{pathname:n,search:r="",hash:o=""}="string"==typeof e?Pt(e):e,i=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:i,search:Yt(r),hash:Xt(o)}}(o,i),u=l&&"/"!==l&&l.endsWith("/"),c=(a||"."===l)&&n.endsWith("/");return s.pathname.endsWith("/")||!u&&!c||(s.pathname+="/"),s}var Qt=e=>e.join("/").replace(/\/\/+/g,"/"),Kt=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Yt=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",Xt=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";var Jt=["POST","PUT","PATCH","DELETE"];new Set(Jt);var Gt=["GET",...Jt];new Set(Gt);var Zt=h.createContext(null);Zt.displayName="DataRouter";var en=h.createContext(null);en.displayName="DataRouterState";var tn=h.createContext({isTransitioning:!1});tn.displayName="ViewTransition",h.createContext(new Map).displayName="Fetchers",h.createContext(null).displayName="Await";var nn=h.createContext(null);nn.displayName="Navigation";var rn=h.createContext(null);rn.displayName="Location";var on=h.createContext({outlet:null,matches:[],isDataRoute:!1});on.displayName="Route";var an=h.createContext(null);function ln(){return null!=h.useContext(rn)}function sn(){return St(ln(),"useLocation() may be used only in the context of a <Router> component."),h.useContext(rn).location}an.displayName="RouteError";var un="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function cn(e){h.useContext(nn).static||h.useLayoutEffect(e)}function fn(){let{isDataRoute:e}=h.useContext(on);return e?function(){let{router:e}=function(e){let t=h.useContext(Zt);return St(t,vn(e)),t}("useNavigate"),t=bn("useNavigate"),n=h.useRef(!1);return cn((()=>{n.current=!0})),h.useCallback((async(r,o={})=>{Et(n.current,un),n.current&&("number"==typeof r?e.navigate(r):await e.navigate(r,{fromRouteId:t,...o}))}),[e,t])}():function(){St(ln(),"useNavigate() may be used only in the context of a <Router> component.");let e=h.useContext(Zt),{basename:t,navigator:n}=h.useContext(nn),{matches:r}=h.useContext(on),{pathname:o}=sn(),i=JSON.stringify(Wt(r)),a=h.useRef(!1);return cn((()=>{a.current=!0})),h.useCallback(((r,l={})=>{if(Et(a.current,un),!a.current)return;if("number"==typeof r)return void n.go(r);let s=qt(r,JSON.parse(i),o,"path"===l.relative);null==e&&"/"!==t&&(s.pathname="/"===s.pathname?t:Qt([t,s.pathname])),(l.replace?n.replace:n.push)(s,l.state,l)}),[t,n,i,o,e])}()}function dn(e,{relative:t}={}){let{matches:n}=h.useContext(on),{pathname:r}=sn(),o=JSON.stringify(Wt(n));return h.useMemo((()=>qt(e,JSON.parse(o),r,"path"===t)),[e,o,r,t])}function pn(e,t,n,r){var o;St(ln(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:i}=h.useContext(nn),{matches:a}=h.useContext(on),l=a[a.length-1],s=l?l.params:{},u=l?l.pathname:"/",c=l?l.pathnameBase:"/",f=l&&l.route;{let e=f&&f.path||"";wn(u,!f||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${u}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let d,p=sn();if(t){let e="string"==typeof t?Pt(t):t;St("/"===c||(null==(o=e.pathname)?void 0:o.startsWith(c)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${c}" but pathname "${e.pathname}" was given in the \`location\` prop.`),d=e}else d=p;let m=d.pathname||"/",g=m;if("/"!==c){let e=c.replace(/^\//,"").split("/");g="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=Nt(e,{pathname:g});Et(f||null!=y,`No routes matched location "${d.pathname}${d.search}${d.hash}" `),Et(null==y||void 0!==y[y.length-1].route.element||void 0!==y[y.length-1].route.Component||void 0!==y[y.length-1].route.lazy,`Matched leaf route at location "${d.pathname}${d.search}${d.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let v=function(e,t=[],n=null){if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let r=e,o=null==n?void 0:n.errors;if(null!=o){let e=r.findIndex((e=>e.route.id&&void 0!==(null==o?void 0:o[e.route.id])));St(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),r=r.slice(0,Math.min(r.length,e+1))}let i=!1,a=-1;if(n)for(let l=0;l<r.length;l++){let e=r[l];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(a=l),e.route.id){let{loaderData:t,errors:o}=n,l=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!o||void 0===o[e.route.id]);if(e.route.lazy||l){i=!0,r=a>=0?r.slice(0,a+1):[r[0]];break}}}return r.reduceRight(((e,l,s)=>{let u,c=!1,f=null,d=null;n&&(u=o&&l.route.id?o[l.route.id]:void 0,f=l.route.errorElement||mn,i&&(a<0&&0===s?(wn("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),c=!0,d=null):a===s&&(c=!0,d=l.route.hydrateFallbackElement||null)));let p=t.concat(r.slice(0,s+1)),m=()=>{let t;return t=u?f:c?d:l.route.Component?h.createElement(l.route.Component,null):l.route.element?l.route.element:e,h.createElement(yn,{match:l,routeContext:{outlet:e,matches:p,isDataRoute:null!=n},children:t})};return n&&(l.route.ErrorBoundary||l.route.errorElement||0===s)?h.createElement(gn,{location:n.location,revalidation:n.revalidation,component:f,error:u,children:m(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):m()}),null)}(y&&y.map((e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:Qt([c,i.encodeLocation?i.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:Qt([c,i.encodeLocation?i.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),a,n,r);return t&&v?h.createElement(rn.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...d},navigationType:"POP"}},v):v}function hn(){let e=function(){var e;let t=h.useContext(an),n=function(e){let t=h.useContext(en);return St(t,vn(e)),t}("useRouteError"),r=bn("useRouteError");if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r},i={padding:"2px 4px",backgroundColor:r},a=null;return a=h.createElement(h.Fragment,null,h.createElement("p",null,"💿 Hey developer 👋"),h.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",h.createElement("code",{style:i},"ErrorBoundary")," or"," ",h.createElement("code",{style:i},"errorElement")," prop on your route.")),h.createElement(h.Fragment,null,h.createElement("h2",null,"Unexpected Application Error!"),h.createElement("h3",{style:{fontStyle:"italic"}},t),n?h.createElement("pre",{style:o},n):null,a)}h.createContext(null);var mn=h.createElement(hn,null),gn=class extends h.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){}render(){return void 0!==this.state.error?h.createElement(on.Provider,{value:this.props.routeContext},h.createElement(an.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function yn({routeContext:e,match:t,children:n}){let r=h.useContext(Zt);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),h.createElement(on.Provider,{value:e},n)}function vn(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function bn(e){let t=function(e){let t=h.useContext(on);return St(t,vn(e)),t}(e),n=t.matches[t.matches.length-1];return St(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}var kn={};function wn(e,t,n){t||kn[e]||(kn[e]=!0,Et(!1,n))}function xn({to:e,replace:t,state:n,relative:r}){St(ln(),"<Navigate> may be used only in the context of a <Router> component.");let{static:o}=h.useContext(nn);Et(!o,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:i}=h.useContext(on),{pathname:a}=sn(),l=fn(),s=qt(e,Wt(i),a,"path"===r),u=JSON.stringify(s);return h.useEffect((()=>{l(JSON.parse(u),{replace:t,state:n,relative:r})}),[l,u,r,t,n]),null}function Sn(e){St(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function En({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:o,static:i=!1}){St(!ln(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let a=e.replace(/^\/*/,"/"),l=h.useMemo((()=>({basename:a,navigator:o,static:i,future:{}})),[a,o,i]);"string"==typeof n&&(n=Pt(n));let{pathname:s="/",search:u="",hash:c="",state:f=null,key:d="default"}=n,p=h.useMemo((()=>{let e=$t(s,a);return null==e?null:{location:{pathname:e,search:u,hash:c,state:f,key:d},navigationType:r}}),[a,s,u,c,f,d,r]);return Et(null!=p,`<Router basename="${a}"> is not able to match the URL "${s}${u}${c}" because it does not start with the basename, so the <Router> won't render anything.`),null==p?null:h.createElement(nn.Provider,{value:l},h.createElement(rn.Provider,{children:t,value:p}))}function Cn({children:e,location:t}){return pn(_n(e),t)}function _n(e,t=[]){let n=[];return h.Children.forEach(e,((e,r)=>{if(!h.isValidElement(e))return;let o=[...t,r];if(e.type===h.Fragment)return void n.push.apply(n,_n(e.props.children,o));St(e.type===Sn,`[${"string"==typeof e.type?e.type:e.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),St(!e.props.index||!e.props.children,"An index route cannot have child routes.");let i={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=_n(e.props.children,o)),n.push(i)})),n}h.memo((function({routes:e,future:t,state:n}){return pn(e,void 0,n,t)}));var Tn="get",Pn="application/x-www-form-urlencoded";function Nn(e){return null!=e&&"string"==typeof e.tagName}var On=null;var Ln=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Rn(e){return null==e||Ln.has(e)?e:(Et(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Pn}"`),null)}function An(e,t){let n,r,o,i,a;if(Nn(l=e)&&"form"===l.tagName.toLowerCase()){let a=e.getAttribute("action");r=a?$t(a,t):null,n=e.getAttribute("method")||Tn,o=Rn(e.getAttribute("enctype"))||Pn,i=new FormData(e)}else if(function(e){return Nn(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Nn(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let a=e.form;if(null==a)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||a.getAttribute("action");if(r=l?$t(l,t):null,n=e.getAttribute("formmethod")||a.getAttribute("method")||Tn,o=Rn(e.getAttribute("formenctype"))||Rn(a.getAttribute("enctype"))||Pn,i=new FormData(a,e),!function(){if(null===On)try{new FormData(document.createElement("form"),0),On=!1}catch(e){On=!0}return On}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?`${t}.`:"";i.append(`${e}x`,"0"),i.append(`${e}y`,"0")}else t&&i.append(t,r)}}else{if(Nn(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=Tn,r=null,o=Pn,a=e}var l;return i&&"text/plain"===o&&(a=i,i=void 0),{action:r,method:n.toLowerCase(),encType:o,formData:i,body:a}}function jn(e,t){if(!1===e||null==e)throw new Error(t)}function zn(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"==typeof e.imageSrcSet&&"string"==typeof e.imageSizes:"string"==typeof e.rel&&"string"==typeof e.href)}async function Dn(e,t,n){return function(e,t){let n=new Set;return new Set(t),e.reduce(((e,t)=>{let r=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(t));return n.has(r)||(n.add(r),e.push({key:r,link:t})),e}),[])}((await Promise.all(e.map((async e=>{let r=t.routes[e.route.id];if(r){let e=await async function(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}(r,n);return e.links?e.links():[]}return[]})))).flat(1).filter(zn).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"})))}function Mn(e,t,n,r,o,i){let a=(e,t)=>!n[t]||e.route.id!==n[t].route.id,l=(e,t)=>{var r;return n[t].pathname!==e.pathname||(null==(r=n[t].route.path)?void 0:r.endsWith("*"))&&n[t].params["*"]!==e.params["*"]};return"assets"===i?t.filter(((e,t)=>a(e,t)||l(e,t))):"data"===i?t.filter(((t,i)=>{var s;let u=r.routes[t.route.id];if(!u||!u.hasLoader)return!1;if(a(t,i)||l(t,i))return!0;if(t.route.shouldRevalidate){let r=t.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:(null==(s=n[0])?void 0:s.params)||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"==typeof r)return r}return!0})):[]}function In(e,t,{includeHydrateFallback:n}={}){return r=e.map((e=>{let r=t.routes[e.route.id];if(!r)return[];let o=[r.module];return r.clientActionModule&&(o=o.concat(r.clientActionModule)),r.clientLoaderModule&&(o=o.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(o=o.concat(r.hydrateFallbackModule)),r.imports&&(o=o.concat(r.imports)),o})).flat(1),[...new Set(r)];var r}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function Fn(){let e=h.useContext(Zt);return jn(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}var Un=h.createContext(void 0);function Bn(){let e=h.useContext(Un);return jn(e,"You must render this element inside a <HydratedRouter> element"),e}function Hn(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function $n({page:e,...t}){let{router:n}=Fn(),r=h.useMemo((()=>Nt(n.routes,e,n.basename)),[n.routes,e,n.basename]);return r?h.createElement(Vn,{page:e,matches:r,...t}):null}function Vn({page:e,matches:t,...n}){let r=sn(),{manifest:o,routeModules:i}=Bn(),{basename:a}=Fn(),{loaderData:l,matches:s}=function(){let e=h.useContext(en);return jn(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}(),u=h.useMemo((()=>Mn(e,t,s,o,r,"data")),[e,t,s,o,r]),c=h.useMemo((()=>Mn(e,t,s,o,r,"assets")),[e,t,s,o,r]),f=h.useMemo((()=>{if(e===r.pathname+r.search+r.hash)return[];let n=new Set,s=!1;if(t.forEach((e=>{var t;let r=o.routes[e.route.id];r&&r.hasLoader&&(!u.some((t=>t.route.id===e.route.id))&&e.route.id in l&&(null==(t=i[e.route.id])?void 0:t.shouldRevalidate)||r.hasClientLoader?s=!0:n.add(e.route.id))})),0===n.size)return[];let c=function(e,t){let n="string"==typeof e?new URL(e,"undefined"==typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===$t(n.pathname,t)?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}(e,a);return s&&n.size>0&&c.searchParams.set("_routes",t.filter((e=>n.has(e.route.id))).map((e=>e.route.id)).join(",")),[c.pathname+c.search]}),[a,l,r,o,u,t,e,i]),d=h.useMemo((()=>In(c,o)),[c,o]),p=function(e){let{manifest:t,routeModules:n}=Bn(),[r,o]=h.useState([]);return h.useEffect((()=>{let r=!1;return Dn(e,t,n).then((e=>{r||o(e)})),()=>{r=!0}}),[e,t,n]),r}(c);return h.createElement(h.Fragment,null,f.map((e=>h.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...n}))),d.map((e=>h.createElement("link",{key:e,rel:"modulepreload",href:e,...n}))),p.map((({key:e,link:t})=>h.createElement("link",{key:e,...t}))))}function Wn(...e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}Un.displayName="FrameworkContext";var qn="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;try{qn&&(window.__reactRouterVersion="7.6.1")}catch(ld){}function Qn({basename:e,children:t,window:n}){let r=h.useRef();null==r.current&&(r.current=xt({window:n,v5Compat:!0}));let o=r.current,[i,a]=h.useState({action:o.action,location:o.location}),l=h.useCallback((e=>{h.startTransition((()=>a(e)))}),[a]);return h.useLayoutEffect((()=>o.listen(l)),[o,l]),h.createElement(En,{basename:e,children:t,location:i.location,navigationType:i.action,navigator:o})}var Kn=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Yn=h.forwardRef((function({onClick:e,discover:t="render",prefetch:n="none",relative:r,reloadDocument:o,replace:i,state:a,target:l,to:s,preventScrollReset:u,viewTransition:c,...f},d){let p,{basename:m}=h.useContext(nn),g="string"==typeof s&&Kn.test(s),y=!1;if("string"==typeof s&&g&&(p=s,qn))try{let e=new URL(window.location.href),t=s.startsWith("//")?new URL(e.protocol+s):new URL(s),n=$t(t.pathname,m);t.origin===e.origin&&null!=n?s=n+t.search+t.hash:y=!0}catch(ld){Et(!1,`<Link to="${s}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let v=function(e,{relative:t}={}){St(ln(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=h.useContext(nn),{hash:o,pathname:i,search:a}=dn(e,{relative:t}),l=i;return"/"!==n&&(l="/"===i?n:Qt([n,i])),r.createHref({pathname:l,search:a,hash:o})}(s,{relative:r}),[b,k,w]=function(e,t){let n=h.useContext(Un),[r,o]=h.useState(!1),[i,a]=h.useState(!1),{onFocus:l,onBlur:s,onMouseEnter:u,onMouseLeave:c,onTouchStart:f}=t,d=h.useRef(null);h.useEffect((()=>{if("render"===e&&a(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{a(e.isIntersecting)}))}),{threshold:.5});return d.current&&e.observe(d.current),()=>{e.disconnect()}}}),[e]),h.useEffect((()=>{if(r){let e=setTimeout((()=>{a(!0)}),100);return()=>{clearTimeout(e)}}}),[r]);let p=()=>{o(!0)},m=()=>{o(!1),a(!1)};return n?"intent"!==e?[i,d,{}]:[i,d,{onFocus:Hn(l,p),onBlur:Hn(s,m),onMouseEnter:Hn(u,p),onMouseLeave:Hn(c,m),onTouchStart:Hn(f,p)}]:[!1,d,{}]}(n,f),x=function(e,{target:t,replace:n,state:r,preventScrollReset:o,relative:i,viewTransition:a}={}){let l=fn(),s=sn(),u=dn(e,{relative:i});return h.useCallback((c=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(c,t)){c.preventDefault();let t=void 0!==n?n:Tt(s)===Tt(u);l(e,{replace:t,state:r,preventScrollReset:o,relative:i,viewTransition:a})}}),[s,l,u,n,r,t,e,o,i,a])}(s,{replace:i,state:a,target:l,preventScrollReset:u,relative:r,viewTransition:c});let S=h.createElement("a",{...f,...w,href:p||v,onClick:y||o?e:function(t){e&&e(t),t.defaultPrevented||x(t)},ref:Wn(d,k),target:l,"data-discover":g||"render"!==t?void 0:"true"});return b&&!g?h.createElement(h.Fragment,null,S,h.createElement($n,{page:v})):S}));function Xn(e){let t=h.useContext(Zt);return St(t,function(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}(e)),t}Yn.displayName="Link",h.forwardRef((function({"aria-current":e="page",caseSensitive:t=!1,className:n="",end:r=!1,style:o,to:i,viewTransition:a,children:l,...s},u){let c=dn(i,{relative:s.relative}),f=sn(),d=h.useContext(en),{navigator:p,basename:m}=h.useContext(nn),g=null!=d&&function(e,t={}){let n=h.useContext(tn);St(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=Xn("useViewTransitionState"),o=dn(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=$t(n.currentLocation.pathname,r)||n.currentLocation.pathname,a=$t(n.nextLocation.pathname,r)||n.nextLocation.pathname;return null!=Bt(o.pathname,a)||null!=Bt(o.pathname,i)}(c)&&!0===a,y=p.encodeLocation?p.encodeLocation(c).pathname:c.pathname,v=f.pathname,b=d&&d.navigation&&d.navigation.location?d.navigation.location.pathname:null;t||(v=v.toLowerCase(),b=b?b.toLowerCase():null,y=y.toLowerCase()),b&&m&&(b=$t(b,m)||b);const k="/"!==y&&y.endsWith("/")?y.length-1:y.length;let w,x=v===y||!r&&v.startsWith(y)&&"/"===v.charAt(k),S=null!=b&&(b===y||!r&&b.startsWith(y)&&"/"===b.charAt(y.length)),E={isActive:x,isPending:S,isTransitioning:g},C=x?e:void 0;w="function"==typeof n?n(E):[n,x?"active":null,S?"pending":null,g?"transitioning":null].filter(Boolean).join(" ");let _="function"==typeof o?o(E):o;return h.createElement(Yn,{...s,"aria-current":C,className:w,ref:u,style:_,to:i,viewTransition:a},"function"==typeof l?l(E):l)})).displayName="NavLink",h.forwardRef((({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:r,replace:o,state:i,method:a=Tn,action:l,onSubmit:s,relative:u,preventScrollReset:c,viewTransition:f,...d},p)=>{let m=function(){let{router:e}=Xn("useSubmit"),{basename:t}=h.useContext(nn),n=bn("useRouteId");return h.useCallback((async(r,o={})=>{let{action:i,method:a,encType:l,formData:s,body:u}=An(r,t);if(!1===o.navigate){let t=o.fetcherKey||Gn();await e.fetch(t,n,o.action||i,{preventScrollReset:o.preventScrollReset,formData:s,body:u,formMethod:o.method||a,formEncType:o.encType||l,flushSync:o.flushSync})}else await e.navigate(o.action||i,{preventScrollReset:o.preventScrollReset,formData:s,body:u,formMethod:o.method||a,formEncType:o.encType||l,replace:o.replace,state:o.state,fromRouteId:n,flushSync:o.flushSync,viewTransition:o.viewTransition})}),[e,t,n])}(),g=function(e,{relative:t}={}){let{basename:n}=h.useContext(nn),r=h.useContext(on);St(r,"useFormAction must be used inside a RouteContext");let[o]=r.matches.slice(-1),i={...dn(e||".",{relative:t})},a=sn();if(null==e){i.search=a.search;let e=new URLSearchParams(i.search),t=e.getAll("index");if(t.some((e=>""===e))){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let n=e.toString();i.search=n?`?${n}`:""}}e&&"."!==e||!o.route.index||(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(i.pathname="/"===i.pathname?n:Qt([n,i.pathname]));return Tt(i)}(l,{relative:u}),y="get"===a.toLowerCase()?"get":"post",v="string"==typeof l&&Kn.test(l);return h.createElement("form",{ref:p,method:y,action:g,onSubmit:r?s:e=>{if(s&&s(e),e.defaultPrevented)return;e.preventDefault();let r=e.nativeEvent.submitter,l=(null==r?void 0:r.getAttribute("formmethod"))||a;m(r||e.currentTarget,{fetcherKey:t,method:l,navigate:n,replace:o,state:i,relative:u,preventScrollReset:c,viewTransition:f})},...d,"data-discover":v||"render"!==e?void 0:"true"})})).displayName="Form";var Jn=0,Gn=()=>`__${String(++Jn)}__`;const Zn=""+new URL("../png/logo-Cnr8EStj.png",import.meta.url).href,er=()=>{var e;const t=K(),n=fn(),r=J((e=>e.auth.userInfo)),[o,i]=h.useState(!1),[a,s]=h.useState(window.innerWidth<=750),u=!!localStorage.getItem("access_token");h.useEffect((()=>{const e=()=>s(window.innerWidth<=750);return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]);return l.jsxs("header",{className:"header "+(o?"menu-open":""),children:[l.jsx("div",{className:"header-left",children:l.jsx("div",{className:"logo",children:l.jsx("img",{src:Zn,alt:"Logo",className:"logo-image"})})}),a&&l.jsx("button",{className:"mobile-menu-button",onClick:()=>{i(!o)},children:l.jsx("svg",{viewBox:"0 0 24 24",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:o?l.jsx("path",{d:"M6 18L18 6M6 6l12 12"}):l.jsxs(l.Fragment,{children:[l.jsx("line",{x1:"3",y1:"12",x2:"21",y2:"12"}),l.jsx("line",{x1:"3",y1:"6",x2:"21",y2:"6"}),l.jsx("line",{x1:"3",y1:"18",x2:"21",y2:"18"})]})})}),l.jsx("div",{className:"header-right "+(o?"show":""),children:l.jsx("div",{className:"auth-section",children:u?l.jsxs("div",{className:"user-dropdown",children:[l.jsxs("button",{className:"user-button",children:[l.jsx("span",{className:"user-avatar",children:(null==r?void 0:r.avatar)?l.jsx("img",{src:r.avatar,alt:"用户头像"}):l.jsx("span",{className:"avatar-placeholder",children:(null==(e=null==r?void 0:r.name)?void 0:e[0])||"U"})}),l.jsx("span",{className:"user-name",children:(null==r?void 0:r.name)||"用户"})]}),l.jsxs("div",{className:"dropdown-menu",children:[l.jsx("a",{href:"/profile",className:"dropdown-item",children:"个人信息"}),l.jsx("a",{href:"/profile",onClick:()=>{localStorage.removeItem("access_token"),t(gt()),n("/login"),i(!1)},className:"dropdown-item",children:"退出登录"})]})]}):l.jsx("a",{className:"nav-link login-button",onClick:()=>{n("/login"),i(!1)},children:"登录"})})})]})},tr=()=>{const e=fn(),[t]=h.useState([{id:1,title:"AI智能分析",lastMessage:"利用先进的人工智能技术，分析您的需求并提供最佳建筑方案"},{id:2,title:"实时响应",lastMessage:"系统实时响应您的咨询，提供即时的专业建议和解决方案"},{id:3,title:"数据可视化",lastMessage:"直观展示建筑数据和分析结果，帮助您做出更明智的决策"},{id:4,title:"专业设计",lastMessage:"提供专业的建筑设计方案，兼顾美观与实用性"},{id:5,title:"沟通记录",lastMessage:"自动保存所有咨询记录，方便您随时查看和继续之前的咨询"},{id:6,title:"安全可靠",lastMessage:"采用先进的安全技术，保护您的数据和隐私安全"}]);return l.jsxs("div",{className:"home",children:[l.jsx(er,{}),l.jsxs("div",{className:"home-container",children:[l.jsxs("div",{className:"banner-section",children:[l.jsx("div",{className:"banner-title",children:l.jsx("span",{children:"AI智能建筑设计模型"})}),l.jsx("div",{className:"banner-description",children:"利用人工智能技术，为您的农村自建房提供专业设计方案"}),l.jsx("button",{className:"consult-button",onClick:()=>{const t=localStorage.getItem("token");e(t?"/chat":"/login")},children:"立即咨询"})]}),l.jsx("div",{className:"features-grid",children:t.map((e=>l.jsxs("div",{className:"feature-card",children:[l.jsx("h3",{children:e.title}),l.jsx("p",{children:e.lastMessage})]},e.id)))})]})]})};function nr(e,t){return function(){return e.apply(t,arguments)}}const{toString:rr}=Object.prototype,{getPrototypeOf:or}=Object,{iterator:ir,toStringTag:ar}=Symbol,lr=(e=>t=>{const n=rr.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),sr=e=>(e=e.toLowerCase(),t=>lr(t)===e),ur=e=>t=>typeof t===e,{isArray:cr}=Array,fr=ur("undefined");const dr=sr("ArrayBuffer");const pr=ur("string"),hr=ur("function"),mr=ur("number"),gr=e=>null!==e&&"object"==typeof e,yr=e=>{if("object"!==lr(e))return!1;const t=or(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||ar in e||ir in e)},vr=sr("Date"),br=sr("File"),kr=sr("Blob"),wr=sr("FileList"),xr=sr("URLSearchParams"),[Sr,Er,Cr,_r]=["ReadableStream","Request","Response","Headers"].map(sr);function Tr(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,o;if("object"!=typeof e&&(e=[e]),cr(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let a;for(r=0;r<i;r++)a=o[r],t.call(null,e[a],a,e)}}function Pr(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,o=n.length;for(;o-- >0;)if(r=n[o],t===r.toLowerCase())return r;return null}const Nr="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,Or=e=>!fr(e)&&e!==Nr;const Lr=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&or(Uint8Array)),Rr=sr("HTMLFormElement"),Ar=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),jr=sr("RegExp"),zr=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Tr(n,((n,o)=>{let i;!1!==(i=t(n,o,e))&&(r[o]=i||n)})),Object.defineProperties(e,r)};const Dr=sr("AsyncFunction"),Mr=(Ir="function"==typeof setImmediate,Fr=hr(Nr.postMessage),Ir?setImmediate:Fr?(Ur=`axios@${Math.random()}`,Br=[],Nr.addEventListener("message",(({source:e,data:t})=>{e===Nr&&t===Ur&&Br.length&&Br.shift()()}),!1),e=>{Br.push(e),Nr.postMessage(Ur,"*")}):e=>setTimeout(e));var Ir,Fr,Ur,Br;const Hr="undefined"!=typeof queueMicrotask?queueMicrotask.bind(Nr):"undefined"!=typeof process&&process.nextTick||Mr,$r={isArray:cr,isArrayBuffer:dr,isBuffer:function(e){return null!==e&&!fr(e)&&null!==e.constructor&&!fr(e.constructor)&&hr(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||hr(e.append)&&("formdata"===(t=lr(e))||"object"===t&&hr(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&dr(e.buffer),t},isString:pr,isNumber:mr,isBoolean:e=>!0===e||!1===e,isObject:gr,isPlainObject:yr,isReadableStream:Sr,isRequest:Er,isResponse:Cr,isHeaders:_r,isUndefined:fr,isDate:vr,isFile:br,isBlob:kr,isRegExp:jr,isFunction:hr,isStream:e=>gr(e)&&hr(e.pipe),isURLSearchParams:xr,isTypedArray:Lr,isFileList:wr,forEach:Tr,merge:function e(){const{caseless:t}=Or(this)&&this||{},n={},r=(r,o)=>{const i=t&&Pr(n,o)||o;yr(n[i])&&yr(r)?n[i]=e(n[i],r):yr(r)?n[i]=e({},r):cr(r)?n[i]=r.slice():n[i]=r};for(let o=0,i=arguments.length;o<i;o++)arguments[o]&&Tr(arguments[o],r);return n},extend:(e,t,n,{allOwnKeys:r}={})=>(Tr(t,((t,r)=>{n&&hr(t)?e[r]=nr(t,n):e[r]=t}),{allOwnKeys:r}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let o,i,a;const l={};if(t=t||{},null==e)return t;do{for(o=Object.getOwnPropertyNames(e),i=o.length;i-- >0;)a=o[i],r&&!r(a,e,t)||l[a]||(t[a]=e[a],l[a]=!0);e=!1!==n&&or(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:lr,kindOfTest:sr,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(cr(e))return e;let t=e.length;if(!mr(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[ir]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:Rr,hasOwnProperty:Ar,hasOwnProp:Ar,reduceDescriptors:zr,freezeMethods:e=>{zr(e,((t,n)=>{if(hr(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];hr(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return cr(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:Pr,global:Nr,isContextDefined:Or,isSpecCompliantForm:function(e){return!!(e&&hr(e.append)&&"FormData"===e[ar]&&e[ir])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(gr(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const o=cr(e)?[]:{};return Tr(e,((e,t)=>{const i=n(e,r+1);!fr(i)&&(o[t]=i)})),t[r]=void 0,o}}return e};return n(e,0)},isAsyncFn:Dr,isThenable:e=>e&&(gr(e)||hr(e))&&hr(e.then)&&hr(e.catch),setImmediate:Mr,asap:Hr,isIterable:e=>null!=e&&hr(e[ir])};function Vr(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}$r.inherits(Vr,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:$r.toJSONObject(this.config),code:this.code,status:this.status}}});const Wr=Vr.prototype,qr={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{qr[e]={value:e}})),Object.defineProperties(Vr,qr),Object.defineProperty(Wr,"isAxiosError",{value:!0}),Vr.from=(e,t,n,r,o,i)=>{const a=Object.create(Wr);return $r.toFlatObject(e,a,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),Vr.call(a,e.message,t,n,r,o),a.cause=e,a.name=e.name,i&&Object.assign(a,i),a};function Qr(e){return $r.isPlainObject(e)||$r.isArray(e)}function Kr(e){return $r.endsWith(e,"[]")?e.slice(0,-2):e}function Yr(e,t,n){return e?e.concat(t).map((function(e,t){return e=Kr(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const Xr=$r.toFlatObject($r,{},null,(function(e){return/^is[A-Z]/.test(e)}));function Jr(e,t,n){if(!$r.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=$r.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!$r.isUndefined(t[e])}))).metaTokens,o=n.visitor||u,i=n.dots,a=n.indexes,l=(n.Blob||"undefined"!=typeof Blob&&Blob)&&$r.isSpecCompliantForm(t);if(!$r.isFunction(o))throw new TypeError("visitor must be a function");function s(e){if(null===e)return"";if($r.isDate(e))return e.toISOString();if(!l&&$r.isBlob(e))throw new Vr("Blob is not supported. Use a Buffer instead.");return $r.isArrayBuffer(e)||$r.isTypedArray(e)?l&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,o){let l=e;if(e&&!o&&"object"==typeof e)if($r.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if($r.isArray(e)&&function(e){return $r.isArray(e)&&!e.some(Qr)}(e)||($r.isFileList(e)||$r.endsWith(n,"[]"))&&(l=$r.toArray(e)))return n=Kr(n),l.forEach((function(e,r){!$r.isUndefined(e)&&null!==e&&t.append(!0===a?Yr([n],r,i):null===a?n:n+"[]",s(e))})),!1;return!!Qr(e)||(t.append(Yr(o,n,i),s(e)),!1)}const c=[],f=Object.assign(Xr,{defaultVisitor:u,convertValue:s,isVisitable:Qr});if(!$r.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!$r.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),$r.forEach(n,(function(n,i){!0===(!($r.isUndefined(n)||null===n)&&o.call(t,n,$r.isString(i)?i.trim():i,r,f))&&e(n,r?r.concat(i):[i])})),c.pop()}}(e),t}function Gr(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function Zr(e,t){this._pairs=[],e&&Jr(e,this,t)}const eo=Zr.prototype;function to(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function no(e,t,n){if(!t)return e;const r=n&&n.encode||to;$r.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let i;if(i=o?o(t,n):$r.isURLSearchParams(t)?t.toString():new Zr(t,n).toString(r),i){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}eo.append=function(e,t){this._pairs.push([e,t])},eo.toString=function(e){const t=e?function(t){return e.call(this,t,Gr)}:Gr;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};class ro{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){$r.forEach(this.handlers,(function(t){null!==t&&e(t)}))}}const oo={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},io={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:Zr,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},ao="undefined"!=typeof window&&"undefined"!=typeof document,lo="object"==typeof navigator&&navigator||void 0,so=ao&&(!lo||["ReactNative","NativeScript","NS"].indexOf(lo.product)<0),uo="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,co=ao&&window.location.href||"http://localhost",fo={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ao,hasStandardBrowserEnv:so,hasStandardBrowserWebWorkerEnv:uo,navigator:lo,origin:co},Symbol.toStringTag,{value:"Module"})),...io};function po(e){function t(e,n,r,o){let i=e[o++];if("__proto__"===i)return!0;const a=Number.isFinite(+i),l=o>=e.length;if(i=!i&&$r.isArray(r)?r.length:i,l)return $r.hasOwnProp(r,i)?r[i]=[r[i],n]:r[i]=n,!a;r[i]&&$r.isObject(r[i])||(r[i]=[]);return t(e,n,r[i],o)&&$r.isArray(r[i])&&(r[i]=function(e){const t={},n=Object.keys(e);let r;const o=n.length;let i;for(r=0;r<o;r++)i=n[r],t[i]=e[i];return t}(r[i])),!a}if($r.isFormData(e)&&$r.isFunction(e.entries)){const n={};return $r.forEachEntry(e,((e,r)=>{t(function(e){return $r.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null}const ho={transitional:oo,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,o=$r.isObject(e);o&&$r.isHTMLForm(e)&&(e=new FormData(e));if($r.isFormData(e))return r?JSON.stringify(po(e)):e;if($r.isArrayBuffer(e)||$r.isBuffer(e)||$r.isStream(e)||$r.isFile(e)||$r.isBlob(e)||$r.isReadableStream(e))return e;if($r.isArrayBufferView(e))return e.buffer;if($r.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let i;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return Jr(e,new fo.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return fo.isNode&&$r.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((i=$r.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return Jr(i?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||r?(t.setContentType("application/json",!1),function(e,t,n){if($r.isString(e))try{return(t||JSON.parse)(e),$r.trim(e)}catch(ld){if("SyntaxError"!==ld.name)throw ld}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||ho.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if($r.isResponse(e)||$r.isReadableStream(e))return e;if(e&&$r.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(ld){if(n){if("SyntaxError"===ld.name)throw Vr.from(ld,Vr.ERR_BAD_RESPONSE,this,null,this.response);throw ld}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:fo.classes.FormData,Blob:fo.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};$r.forEach(["delete","get","head","post","put","patch"],(e=>{ho.headers[e]={}}));const mo=$r.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),go=Symbol("internals");function yo(e){return e&&String(e).trim().toLowerCase()}function vo(e){return!1===e||null==e?e:$r.isArray(e)?e.map(vo):String(e)}function bo(e,t,n,r,o){return $r.isFunction(r)?r.call(this,t,n):(o&&(t=n),$r.isString(t)?$r.isString(r)?-1!==t.indexOf(r):$r.isRegExp(r)?r.test(t):void 0:void 0)}let ko=class{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function o(e,t,n){const o=yo(t);if(!o)throw new Error("header name must be a non-empty string");const i=$r.findKey(r,o);(!i||void 0===r[i]||!0===n||void 0===n&&!1!==r[i])&&(r[i||t]=vo(e))}const i=(e,t)=>$r.forEach(e,((e,n)=>o(e,n,t)));if($r.isPlainObject(e)||e instanceof this.constructor)i(e,t);else if($r.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))i((e=>{const t={};let n,r,o;return e&&e.split("\n").forEach((function(e){o=e.indexOf(":"),n=e.substring(0,o).trim().toLowerCase(),r=e.substring(o+1).trim(),!n||t[n]&&mo[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if($r.isObject(e)&&$r.isIterable(e)){let n,r,o={};for(const t of e){if(!$r.isArray(t))throw TypeError("Object iterator must return a key-value pair");o[r=t[0]]=(n=o[r])?$r.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}i(o,t)}else null!=e&&o(t,e,n);return this}get(e,t){if(e=yo(e)){const n=$r.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if($r.isFunction(t))return t.call(this,e,n);if($r.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=yo(e)){const n=$r.findKey(this,e);return!(!n||void 0===this[n]||t&&!bo(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function o(e){if(e=yo(e)){const o=$r.findKey(n,e);!o||t&&!bo(0,n[o],o,t)||(delete n[o],r=!0)}}return $r.isArray(e)?e.forEach(o):o(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const o=t[n];e&&!bo(0,this[o],o,e,!0)||(delete this[o],r=!0)}return r}normalize(e){const t=this,n={};return $r.forEach(this,((r,o)=>{const i=$r.findKey(n,o);if(i)return t[i]=vo(r),void delete t[o];const a=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(o):String(o).trim();a!==o&&delete t[o],t[a]=vo(r),n[a]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return $r.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&$r.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach((e=>n.set(e))),n}static accessor(e){const t=(this[go]=this[go]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=yo(e);t[r]||(!function(e,t){const n=$r.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})}))}(n,e),t[r]=!0)}return $r.isArray(e)?e.forEach(r):r(e),this}};function wo(e,t){const n=this||ho,r=t||n,o=ko.from(r.headers);let i=r.data;return $r.forEach(e,(function(e){i=e.call(n,i,o.normalize(),t?t.status:void 0)})),o.normalize(),i}function xo(e){return!(!e||!e.__CANCEL__)}function So(e,t,n){Vr.call(this,null==e?"canceled":e,Vr.ERR_CANCELED,t,n),this.name="CanceledError"}function Eo(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new Vr("Request failed with status code "+n.status,[Vr.ERR_BAD_REQUEST,Vr.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}ko.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),$r.reduceDescriptors(ko.prototype,(({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}})),$r.freezeMethods(ko),$r.inherits(So,Vr,{__CANCEL__:!0});const Co=(e,t,n=3)=>{let r=0;const o=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o,i=0,a=0;return t=void 0!==t?t:1e3,function(l){const s=Date.now(),u=r[a];o||(o=s),n[i]=l,r[i]=s;let c=a,f=0;for(;c!==i;)f+=n[c++],c%=e;if(i=(i+1)%e,i===a&&(a=(a+1)%e),s-o<t)return;const d=u&&s-u;return d?Math.round(1e3*f/d):void 0}}(50,250);return function(e,t){let n,r,o=0,i=1e3/t;const a=(t,i=Date.now())=>{o=i,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[(...e)=>{const t=Date.now(),l=t-o;l>=i?a(e,t):(n=e,r||(r=setTimeout((()=>{r=null,a(n)}),i-l)))},()=>n&&a(n)]}((n=>{const i=n.loaded,a=n.lengthComputable?n.total:void 0,l=i-r,s=o(l);r=i;e({loaded:i,total:a,progress:a?i/a:void 0,bytes:l,rate:s||void 0,estimated:s&&a&&i<=a?(a-i)/s:void 0,event:n,lengthComputable:null!=a,[t?"download":"upload"]:!0})}),n)},_o=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},To=e=>(...t)=>$r.asap((()=>e(...t))),Po=fo.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,fo.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(fo.origin),fo.navigator&&/(msie|trident)/i.test(fo.navigator.userAgent)):()=>!0,No=fo.hasStandardBrowserEnv?{write(e,t,n,r,o,i){const a=[e+"="+encodeURIComponent(t)];$r.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),$r.isString(r)&&a.push("path="+r),$r.isString(o)&&a.push("domain="+o),!0===i&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Oo(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Lo=e=>e instanceof ko?{...e}:e;function Ro(e,t){t=t||{};const n={};function r(e,t,n,r){return $r.isPlainObject(e)&&$r.isPlainObject(t)?$r.merge.call({caseless:r},e,t):$r.isPlainObject(t)?$r.merge({},t):$r.isArray(t)?t.slice():t}function o(e,t,n,o){return $r.isUndefined(t)?$r.isUndefined(e)?void 0:r(void 0,e,0,o):r(e,t,0,o)}function i(e,t){if(!$r.isUndefined(t))return r(void 0,t)}function a(e,t){return $r.isUndefined(t)?$r.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function l(n,o,i){return i in t?r(n,o):i in e?r(void 0,n):void 0}const s={url:i,method:i,data:i,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:l,headers:(e,t,n)=>o(Lo(e),Lo(t),0,!0)};return $r.forEach(Object.keys(Object.assign({},e,t)),(function(r){const i=s[r]||o,a=i(e[r],t[r],r);$r.isUndefined(a)&&i!==l||(n[r]=a)})),n}const Ao=e=>{const t=Ro({},e);let n,{data:r,withXSRFToken:o,xsrfHeaderName:i,xsrfCookieName:a,headers:l,auth:s}=t;if(t.headers=l=ko.from(l),t.url=no(Oo(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&l.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):""))),$r.isFormData(r))if(fo.hasStandardBrowserEnv||fo.hasStandardBrowserWebWorkerEnv)l.setContentType(void 0);else if(!1!==(n=l.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];l.setContentType([e||"multipart/form-data",...t].join("; "))}if(fo.hasStandardBrowserEnv&&(o&&$r.isFunction(o)&&(o=o(t)),o||!1!==o&&Po(t.url))){const e=i&&a&&No.read(a);e&&l.set(i,e)}return t},jo="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=Ao(e);let o=r.data;const i=ko.from(r.headers).normalize();let a,l,s,u,c,{responseType:f,onUploadProgress:d,onDownloadProgress:p}=r;function h(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(a),r.signal&&r.signal.removeEventListener("abort",a)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=ko.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Eo((function(e){t(e),h()}),(function(e){n(e),h()}),{data:f&&"text"!==f&&"json"!==f?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new Vr("Request aborted",Vr.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new Vr("Network Error",Vr.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const o=r.transitional||oo;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new Vr(t,o.clarifyTimeoutError?Vr.ETIMEDOUT:Vr.ECONNABORTED,e,m)),m=null},void 0===o&&i.setContentType(null),"setRequestHeader"in m&&$r.forEach(i.toJSON(),(function(e,t){m.setRequestHeader(t,e)})),$r.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),f&&"json"!==f&&(m.responseType=r.responseType),p&&([s,c]=Co(p,!0),m.addEventListener("progress",s)),d&&m.upload&&([l,u]=Co(d),m.upload.addEventListener("progress",l),m.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(a=t=>{m&&(n(!t||t.type?new So(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(a),r.signal&&(r.signal.aborted?a():r.signal.addEventListener("abort",a)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===fo.protocols.indexOf(y)?n(new Vr("Unsupported protocol "+y+":",Vr.ERR_BAD_REQUEST,e)):m.send(o||null)}))},zo=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const o=function(e){if(!n){n=!0,a();const t=e instanceof Error?e:this.reason;r.abort(t instanceof Vr?t:new So(t instanceof Error?t.message:t))}};let i=t&&setTimeout((()=>{i=null,o(new Vr(`timeout ${t} of ms exceeded`,Vr.ETIMEDOUT))}),t);const a=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(o):e.removeEventListener("abort",o)})),e=null)};e.forEach((e=>e.addEventListener("abort",o)));const{signal:l}=r;return l.unsubscribe=()=>$r.asap(a),l}},Do=function*(e,t){let n=e.byteLength;if(n<t)return void(yield e);let r,o=0;for(;o<n;)r=o+t,yield e.slice(o,r),o=r},Mo=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}},Io=(e,t,n,r)=>{const o=async function*(e,t){for await(const n of Mo(e))yield*Do(n,t)}(e,t);let i,a=0,l=e=>{i||(i=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await o.next();if(t)return l(),void e.close();let i=r.byteLength;if(n){let e=a+=i;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw l(t),t}},cancel:e=>(l(e),o.return())},{highWaterMark:2})},Fo="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,Uo=Fo&&"function"==typeof ReadableStream,Bo=Fo&&("function"==typeof TextEncoder?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Ho=(e,...t)=>{try{return!!e(...t)}catch(ld){return!1}},$o=Uo&&Ho((()=>{let e=!1;const t=new Request(fo.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),Vo=Uo&&Ho((()=>$r.isReadableStream(new Response("").body))),Wo={stream:Vo&&(e=>e.body)};var qo;Fo&&(qo=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!Wo[e]&&(Wo[e]=$r.isFunction(qo[e])?t=>t[e]():(t,n)=>{throw new Vr(`Response type '${e}' is not supported`,Vr.ERR_NOT_SUPPORT,n)})})));const Qo=async(e,t)=>{const n=$r.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if($r.isBlob(e))return e.size;if($r.isSpecCompliantForm(e)){const t=new Request(fo.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return $r.isArrayBufferView(e)||$r.isArrayBuffer(e)?e.byteLength:($r.isURLSearchParams(e)&&(e+=""),$r.isString(e)?(await Bo(e)).byteLength:void 0)})(t):n},Ko={http:null,xhr:jo,fetch:Fo&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:i,timeout:a,onDownloadProgress:l,onUploadProgress:s,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:d}=Ao(e);u=u?(u+"").toLowerCase():"text";let p,h=zo([o,i&&i.toAbortSignal()],a);const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let g;try{if(s&&$o&&"get"!==n&&"head"!==n&&0!==(g=await Qo(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if($r.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=_o(g,Co(To(s)));r=Io(n.body,65536,e,t)}}$r.isString(f)||(f=f?"include":"omit");const o="credentials"in Request.prototype;p=new Request(t,{...d,signal:h,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:o?f:void 0});let i=await fetch(p);const a=Vo&&("stream"===u||"response"===u);if(Vo&&(l||a&&m)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=i[t]}));const t=$r.toFiniteNumber(i.headers.get("content-length")),[n,r]=l&&_o(t,Co(To(l),!0))||[];i=new Response(Io(i.body,65536,n,(()=>{r&&r(),m&&m()})),e)}u=u||"text";let y=await Wo[$r.findKey(Wo,u)||"text"](i,e);return!a&&m&&m(),await new Promise(((t,n)=>{Eo(t,n,{data:y,headers:ko.from(i.headers),status:i.status,statusText:i.statusText,config:e,request:p})}))}catch(y){if(m&&m(),y&&"TypeError"===y.name&&/Load failed|fetch/i.test(y.message))throw Object.assign(new Vr("Network Error",Vr.ERR_NETWORK,e,p),{cause:y.cause||y});throw Vr.from(y,y&&y.code,e,p)}})};$r.forEach(Ko,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(ld){}Object.defineProperty(e,"adapterName",{value:t})}}));const Yo=e=>`- ${e}`,Xo=e=>$r.isFunction(e)||null===e||!1===e,Jo=e=>{e=$r.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let i=0;i<t;i++){let t;if(n=e[i],r=n,!Xo(n)&&(r=Ko[(t=String(n)).toLowerCase()],void 0===r))throw new Vr(`Unknown adapter '${t}'`);if(r)break;o[t||"#"+i]=r}if(!r){const e=Object.entries(o).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));throw new Vr("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(Yo).join("\n"):" "+Yo(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function Go(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new So(null,e)}function Zo(e){Go(e),e.headers=ko.from(e.headers),e.data=wo.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return Jo(e.adapter||ho.adapter)(e).then((function(t){return Go(e),t.data=wo.call(e,e.transformResponse,t),t.headers=ko.from(t.headers),t}),(function(t){return xo(t)||(Go(e),t&&t.response&&(t.response.data=wo.call(e,e.transformResponse,t.response),t.response.headers=ko.from(t.response.headers))),Promise.reject(t)}))}const ei="1.9.0",ti={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{ti[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const ni={};ti.transitional=function(e,t,n){return(r,o,i)=>{if(!1===e)throw new Vr(function(e,t){return"[Axios v1.9.0] Transitional option '"+e+"'"+t+(n?". "+n:"")}(o," has been removed"+(t?" in "+t:"")),Vr.ERR_DEPRECATED);return t&&!ni[o]&&(ni[o]=!0),!e||e(r,o,i)}},ti.spelling=function(e){return(e,t)=>!0};const ri={assertOptions:function(e,t,n){if("object"!=typeof e)throw new Vr("options must be an object",Vr.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const i=r[o],a=t[i];if(a){const t=e[i],n=void 0===t||a(t,i,e);if(!0!==n)throw new Vr("option "+i+" must be "+n,Vr.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new Vr("Unknown option "+i,Vr.ERR_BAD_OPTION)}},validators:ti},oi=ri.validators;let ii=class{constructor(e){this.defaults=e||{},this.interceptors={request:new ro,response:new ro}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(ld){}}throw n}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=Ro(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:o}=t;void 0!==n&&ri.assertOptions(n,{silentJSONParsing:oi.transitional(oi.boolean),forcedJSONParsing:oi.transitional(oi.boolean),clarifyTimeoutError:oi.transitional(oi.boolean)},!1),null!=r&&($r.isFunction(r)?t.paramsSerializer={serialize:r}:ri.assertOptions(r,{encode:oi.function,serialize:oi.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),ri.assertOptions(t,{baseUrl:oi.spelling("baseURL"),withXsrfToken:oi.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let i=o&&$r.merge(o.common,o[t.method]);o&&$r.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete o[e]})),t.headers=ko.concat(i,o);const a=[];let l=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(l=l&&e.synchronous,a.unshift(e.fulfilled,e.rejected))}));const s=[];let u;this.interceptors.response.forEach((function(e){s.push(e.fulfilled,e.rejected)}));let c,f=0;if(!l){const e=[Zo.bind(this),void 0];for(e.unshift.apply(e,a),e.push.apply(e,s),c=e.length,u=Promise.resolve(t);f<c;)u=u.then(e[f++],e[f++]);return u}c=a.length;let d=t;for(f=0;f<c;){const e=a[f++],t=a[f++];try{d=e(d)}catch(p){t.call(this,p);break}}try{u=Zo.call(this,d)}catch(p){return Promise.reject(p)}for(f=0,c=s.length;f<c;)u=u.then(s[f++],s[f++]);return u}getUri(e){return no(Oo((e=Ro(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}};$r.forEach(["delete","get","head","options"],(function(e){ii.prototype[e]=function(t,n){return this.request(Ro(n||{},{method:e,url:t,data:(n||{}).data}))}})),$r.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,o){return this.request(Ro(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}ii.prototype[e]=t(),ii.prototype[e+"Form"]=t(!0)}));const ai={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ai).forEach((([e,t])=>{ai[t]=e}));const li=function e(t){const n=new ii(t),r=nr(ii.prototype.request,n);return $r.extend(r,ii.prototype,n,{allOwnKeys:!0}),$r.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Ro(t,n))},r}(ho);li.Axios=ii,li.CanceledError=So,li.CancelToken=class e{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,o){n.reason||(n.reason=new So(e,r,o),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let t;return{token:new e((function(e){t=e})),cancel:t}}},li.isCancel=xo,li.VERSION=ei,li.toFormData=Jr,li.AxiosError=Vr,li.Cancel=li.CanceledError,li.all=function(e){return Promise.all(e)},li.spread=function(e){return function(t){return e.apply(null,t)}},li.isAxiosError=function(e){return $r.isObject(e)&&!0===e.isAxiosError},li.mergeConfig=Ro,li.AxiosHeaders=ko,li.formToJSON=e=>po($r.isHTMLForm(e)?new FormData(e):e),li.getAdapter=Jo,li.HttpStatusCode=ai,li.default=li;const{Axios:si,AxiosError:ui,CanceledError:ci,isCancel:fi,CancelToken:di,VERSION:pi,all:hi,Cancel:mi,isAxiosError:gi,spread:yi,toFormData:vi,AxiosHeaders:bi,HttpStatusCode:ki,formToJSON:wi,getAdapter:xi,mergeConfig:Si}=li,Ei=li.create({baseURL:"http://123.56.141.236:8010",timeout:12e5});Ei.interceptors.request.use((e=>{const{accessToken:t}=vt.getState().auth;return t&&(e.headers.Authorization=`Bearer ${t}`),e}),(e=>Promise.reject(e))),Ei.interceptors.response.use((e=>e),(async e=>{var t;const n=e.config;if(401===(null==(t=e.response)?void 0:t.status)&&!n._retry){n._retry=!0;try{const e=await _i();return n.headers.Authorization=`Bearer ${e}`,Ei(n)}catch(r){const{logout:e}=vt.getState().auth;return e&&e(),window.location.href="/login",Promise.reject(r)}}return Promise.reject(e)}));const Ci=e=>{const{access_token:t,refresh_token:n,session_id:r,user_id:o,expires_in:i=120}=e.data;return vt.dispatch(ht({access_token:t,refresh_token:n,session_id:r,user_id:o,expires_in:i})),t},_i=async()=>{try{const{refreshToken:e}=vt.getState().auth;if(!e)throw new Error("No refresh token");const t=await Ei.post("/api/auth/refresh-token",{refresh_token:e,client_id:"ai_design_web",client_secret:"hbSmqUQ8qPrnJJnyS1Xk933PiS2dKHLP"}),{access_token:n,expires_in:r}=t.data;return vt.dispatch(yt({access_token:n,expires_in:r})),n}catch(e){throw e}},Ti=()=>{const e=fn(),[t,n]=h.useState("18813082663"),[r,o]=h.useState(""),[i,a]=h.useState(""),[s,u]=h.useState("code"),[c,f]=h.useState(!1),[d,p]=h.useState(""),[m,g]=h.useState(!1),[y,v]=h.useState((()=>"dark"===localStorage.getItem("theme"))),[b,k]=h.useState(0);return h.useEffect((()=>{let e;return b>0&&(e=setTimeout((()=>k(b-1)),1e3)),()=>clearTimeout(e)}),[b]),h.useEffect((()=>{localStorage.getItem("access_token")&&e("/chat")}),[e]),l.jsx("div",{className:"page-container "+(y?"dark-theme":"light-theme"),children:l.jsxs("div",{className:"login-container",children:[l.jsx("button",{className:"theme-toggle",onClick:()=>{const e=!y;v(e),localStorage.setItem("theme",e?"dark":"light")},children:y?l.jsxs("svg",{className:"theme-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[l.jsx("path",{d:"M12 17C14.7614 17 17 14.7614 17 12C17 9.23858 14.7614 7 12 7C9.23858 7 7 9.23858 7 12C7 14.7614 9.23858 17 12 17Z",stroke:"currentColor",strokeWidth:"2"}),l.jsx("path",{d:"M12 1V3M12 21V23M1 12H3M21 12H23M4.22 4.22L5.64 5.64M18.36 18.36L19.78 19.78M18.36 5.64L19.78 4.22M4.22 19.78L5.64 18.36",stroke:"currentColor",strokeWidth:"2"})]}):l.jsx("svg",{className:"theme-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:l.jsx("path",{d:"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z",stroke:"currentColor",strokeWidth:"2"})})}),l.jsxs("div",{className:"login-header",children:[l.jsx("h1",{children:"博笛智家"}),l.jsx("p",{children:"开启AI建房新时代"})]}),l.jsxs("form",{onSubmit:async n=>{n.preventDefault(),f(!0),p("");try{if(!t)throw new Error("请输入手机号");if("code"===s){if(!r)throw new Error("请输入验证码");await(async(e,t)=>{var n,r;try{vt.dispatch(pt());const n=await Ei.post("/api/auth/auth-with-verification-code",{target:e,code:t,type:"phone",client_id:"ai_design_web",client_secret:"hbSmqUQ8qPrnJJnyS1Xk933PiS2dKHLP",device_id:"asha",remember_me:!0});return Ci(n)}catch(d){const t=(null==(r=null==(n=d.response)?void 0:n.data)?void 0:r.message)||"登录失败，请检查验证码";throw vt.dispatch(mt(t)),new Error(t)}})(t,r)}else{if(!i)throw new Error("请输入密码");await(async(e,t)=>{var n,r;try{vt.dispatch(pt());const n=await Ei.post("/api/auth/login",{username:e,password:t,client_id:"ai_design_web",client_secret:"hbSmqUQ8qPrnJJnyS1Xk933PiS2dKHLP",device_id:"asha",remember_me:!1});return Ci(n)}catch(d){const t=(null==(r=null==(n=d.response)?void 0:n.data)?void 0:r.message)||"登录失败，请检查手机号和密码";throw vt.dispatch(mt(t)),new Error(t)}})(t,i)}e("/chat")}catch(o){p(o.message||"登录失败，请稍后重试")}finally{f(!1)}},className:"login-form",children:[l.jsx("h2",{children:"用户登录"}),l.jsx("div",{className:"input-group",children:l.jsx("input",{type:"tel",placeholder:"请输入手机号",value:t,onChange:e=>n(e.target.value),disabled:c,required:!0})}),"code"===s?l.jsxs("div",{className:"input-group code-input",children:[l.jsx("input",{type:"text",placeholder:"验证码",value:r,onChange:e=>o(e.target.value),disabled:c,maxLength:6,required:!0}),l.jsx("button",{type:"button",className:"send-code "+(b>0?"disabled":""),onClick:async()=>{if(!t)return p("请输入手机号"),void g(!1);if(!(b>0))try{f(!0),p(""),await(async e=>{var t,n,r;try{const n=await Ei.post("/api/auth/send-verification-code",{target:e,type:"phone"});if(200!==n.status)throw new Error((null==(t=n.data)?void 0:t.message)||"发送验证码失败");return!0}catch(d){throw new Error((null==(r=null==(n=d.response)?void 0:n.data)?void 0:r.message)||"发送验证码失败，请稍后重试")}})(t),k(60),p("验证码已发送，请查收"),g(!0)}catch(e){p(e.message||"发送验证码失败"),g(!1)}finally{f(!1)}},disabled:c||b>0,children:b>0?`${b}秒后重发`:"获取验证码"})]}):l.jsx("div",{className:"input-group",children:l.jsx("input",{type:"password",placeholder:"密码",value:i,onChange:e=>a(e.target.value),disabled:c,required:!0})}),l.jsx("div",{className:"switch-method",children:l.jsx("button",{type:"button",onClick:()=>u("code"===s?"password":"code"),disabled:c,children:"code"===s?"使用密码登录":"使用验证码登录"})}),d&&l.jsx("div",{className:"error-message "+(m?"success":""),children:d}),l.jsx("button",{type:"submit",className:"login-button",disabled:c,children:c?l.jsx("span",{className:"loading-spinner"}):"登录"})]}),l.jsxs("div",{className:"login-footer",children:[l.jsx("p",{children:"© 2023 博笛智家AI智能助手 - 让沟通更智能"}),l.jsx("p",{children:"首次登录将自动创建账号"})]})]})})},Pi=({isDarkMode:e,onConversationSelect:t,fetchTrigger:n})=>{const[r,o]=h.useState("recent"),[i,a]=h.useState(null),[s,u]=h.useState([]),[c,f]=h.useState(!1),[d,p]=h.useState(null),[m,g]=h.useState(""),y=J((e=>e.auth.userId)),[v,b]=h.useState(window.innerWidth<=750);h.useEffect((()=>{const e=()=>{b(window.innerWidth<=750)};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]);const k="http://ai-model.kaikungroup.com:8081",w="Bearer app-2XnCWvdqFxRWhUP69QSsjQbZ",x=async()=>{f(!0);try{const e=await fetch(`${k}/v1/conversations?user=${y}&limit=100`,{method:"GET",headers:{Authorization:w,"Content-Type":"application/json"}});if(!e.ok)throw new Error("获取会话列表失败");const n=(await e.json()).data||[];u(n);const r=e=>{if(!e)return!1;const t="string"==typeof e?Date.parse(e):1e3*Number(e),n=new Date(t),r=new Date;return n.toDateString()===r.toDateString()},o=n.filter((e=>r(e.updated_at)));0===o.length?E():(a(o[0].id),t(o[0].id))}catch(e){E()}finally{f(!1)}};h.useEffect((()=>{y&&x()}),[y,n]);const S=()=>{if(v)return s;switch(r){case"saved":return s.filter((e=>e.saved));case"all":return s;default:return s.filter((e=>(e=>{if(!e)return!1;const t="string"==typeof e?Date.parse(e):1e3*Number(e),n=new Date(t),r=new Date;return n.toDateString()===r.toDateString()})(e.updated_at)))}},E=async()=>{s.some((e=>e.id.toString().startsWith("new-")))&&await x();const e={id:"new-"+Date.now(),name:"新对话",introduction:"您好，有什么需要帮助您呢",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString(),isNew:!0};u((t=>[e,...t])),a(e.id),t(e.id,!0)},C=e=>{if(!e)return"";const t="string"==typeof e?Date.parse(e):1e3*Number(e),n=new Date(t);if(isNaN(n.getTime()))return"";const r=new Date-n,o=Math.floor(r/864e5);if(0===o){return`${n.getHours().toString().padStart(2,"0")}:${n.getMinutes().toString().padStart(2,"0")}`}if(1===o)return"昨天";if(o<7){return["周日","周一","周二","周三","周四","周五","周六"][n.getDay()]}return`${(n.getMonth()+1).toString().padStart(2,"0")}/${n.getDate().toString().padStart(2,"0")}`},_=()=>l.jsxs("div",{className:"empty-state",children:[l.jsx("svg",{width:"64",height:"64",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",children:l.jsx("path",{d:"M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2v10z"})}),l.jsx("p",{children:"recent"===r?"今天还没有新对话":"暂无对话记录"})]}),T=async e=>{if(e.preventDefault(),d&&m.trim())try{if(!(await fetch(`${k}/v1/conversations/${d}/name`,{method:"POST",headers:{Authorization:w,"Content-Type":"application/json"},body:JSON.stringify({name:m.trim(),user:y})})).ok)throw new Error("重命名失败");await x()}catch(t){}finally{p(null),g("")}},P=e=>{"Enter"===e.key?T(e):"Escape"===e.key&&(p(null),g(""))};return l.jsxs("div",{className:"sidebar "+(e?"dark-theme":""),children:[l.jsx("div",{className:"sidebar-header",children:l.jsxs("button",{className:"new-chat-btn",onClick:E,children:[l.jsx("span",{className:"plus-icon",children:"+"})," 新对话"]})}),!v&&l.jsx("div",{className:"tabs-container",children:l.jsxs("div",{className:"tabs",children:[l.jsx("button",{className:"tab-btn "+("recent"===r?"active":""),onClick:()=>o("recent"),children:"今天"}),l.jsx("button",{className:"tab-btn "+("all"===r?"active":""),onClick:()=>o("all"),children:"全部"}),l.jsx("button",{className:"tab-btn "+("saved"===r?"active":""),onClick:()=>o("saved"),children:"已保存"})]})}),l.jsx("div",{className:"conversation-list",children:c?l.jsx("div",{className:"loading-state",children:"加载中..."}):0===S().length?l.jsx(_,{}):S().map((e=>l.jsxs("div",{className:"conversation-item "+(i===e.id?"active":""),onClick:()=>{return n=e.id,a(n),u((e=>e.map((e=>e.id===n?{...e,unread:!1}:e)))),void t(n);var n},children:[l.jsx("div",{className:"conversation-content",children:d===e.id?l.jsx("input",{type:"text",className:"rename-input",value:m,onChange:e=>g(e.target.value),onBlur:T,onKeyDown:P,autoFocus:!0,onClick:e=>e.stopPropagation()}):l.jsxs(l.Fragment,{children:[l.jsx("h4",{className:"conversation-title",onDoubleClick:()=>(e=>{e.id.toString().startsWith("new-")||(p(e.id),g(e.name||"新对话"))})(e),children:e.name||"新对话"}),l.jsx("span",{className:"conversation-time",style:{fontSize:"12px",color:"#94a3b8",display:"block",marginTop:2},children:C(e.updated_at)})]})}),l.jsx("div",{className:"conversation-meta",children:e.unread&&l.jsx("span",{className:"unread-dot"})})]},e.id)))})]})},Ni=({svgContent:e,className:t=""})=>{if(!(e&&(e.includes("<svg")||e.includes("viewBox")||e.trim().startsWith("<svg"))))return null;return l.jsxs("div",{className:`svg-renderer ${t}`,children:[l.jsx("div",{className:"svg-toolbar",children:l.jsxs("button",{className:"svg-download-btn",onClick:()=>{const t=new Blob([e],{type:"image/svg+xml"}),n=URL.createObjectURL(t),r=document.createElement("a");r.href=n,r.download="image.svg",document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(n)},title:"下载SVG图",children:[l.jsx("span",{className:"svg-download-icon","aria-hidden":"true",children:l.jsxs("svg",{viewBox:"0 0 20 20",width:"16",height:"16",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[l.jsx("path",{d:"M10 3v10m0 0l-4-4m4 4l4-4",strokeLinecap:"round",strokeLinejoin:"round"}),l.jsx("rect",{x:"4",y:"15",width:"12",height:"2",rx:"1",fill:"currentColor"})]})}),"下载图纸"]})}),l.jsx("div",{className:"svg-container",dangerouslySetInnerHTML:{__html:e}})]})};const Oi=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,Li=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,Ri={};function Ai(e,t){return(Ri.jsx?Li:Oi).test(e)}const ji=/[ \t\n\f\r]/g;function zi(e){return""===e.replace(ji,"")}class Di{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function Mi(e,t){const n={},r={};for(const o of e)Object.assign(n,o.property),Object.assign(r,o.normal);return new Di(n,r,t)}function Ii(e){return e.toLowerCase()}Di.prototype.normal={},Di.prototype.property={},Di.prototype.space=void 0;class Fi{constructor(e,t){this.attribute=t,this.property=e}}Fi.prototype.attribute="",Fi.prototype.booleanish=!1,Fi.prototype.boolean=!1,Fi.prototype.commaOrSpaceSeparated=!1,Fi.prototype.commaSeparated=!1,Fi.prototype.defined=!1,Fi.prototype.mustUseProperty=!1,Fi.prototype.number=!1,Fi.prototype.overloadedBoolean=!1,Fi.prototype.property="",Fi.prototype.spaceSeparated=!1,Fi.prototype.space=void 0;let Ui=0;const Bi=Ki(),Hi=Ki(),$i=Ki(),Vi=Ki(),Wi=Ki(),qi=Ki(),Qi=Ki();function Ki(){return 2**++Ui}const Yi=Object.freeze(Object.defineProperty({__proto__:null,boolean:Bi,booleanish:Hi,commaOrSpaceSeparated:Qi,commaSeparated:qi,number:Vi,overloadedBoolean:$i,spaceSeparated:Wi},Symbol.toStringTag,{value:"Module"})),Xi=Object.keys(Yi);class Ji extends Fi{constructor(e,t,n,r){let o=-1;if(super(e,t),Gi(this,"space",r),"number"==typeof n)for(;++o<Xi.length;){const e=Xi[o];Gi(this,Xi[o],(n&Yi[e])===Yi[e])}}}function Gi(e,t,n){n&&(e[t]=n)}function Zi(e){const t={},n={};for(const[r,o]of Object.entries(e.properties)){const i=new Ji(r,e.transform(e.attributes||{},r),o,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),t[r]=i,n[Ii(r)]=r,n[Ii(i.attribute)]=r}return new Di(t,n,e.space)}Ji.prototype.defined=!0;const ea=Zi({properties:{ariaActiveDescendant:null,ariaAtomic:Hi,ariaAutoComplete:null,ariaBusy:Hi,ariaChecked:Hi,ariaColCount:Vi,ariaColIndex:Vi,ariaColSpan:Vi,ariaControls:Wi,ariaCurrent:null,ariaDescribedBy:Wi,ariaDetails:null,ariaDisabled:Hi,ariaDropEffect:Wi,ariaErrorMessage:null,ariaExpanded:Hi,ariaFlowTo:Wi,ariaGrabbed:Hi,ariaHasPopup:null,ariaHidden:Hi,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:Wi,ariaLevel:Vi,ariaLive:null,ariaModal:Hi,ariaMultiLine:Hi,ariaMultiSelectable:Hi,ariaOrientation:null,ariaOwns:Wi,ariaPlaceholder:null,ariaPosInSet:Vi,ariaPressed:Hi,ariaReadOnly:Hi,ariaRelevant:null,ariaRequired:Hi,ariaRoleDescription:Wi,ariaRowCount:Vi,ariaRowIndex:Vi,ariaRowSpan:Vi,ariaSelected:Hi,ariaSetSize:Vi,ariaSort:null,ariaValueMax:Vi,ariaValueMin:Vi,ariaValueNow:Vi,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function ta(e,t){return t in e?e[t]:t}function na(e,t){return ta(e,t.toLowerCase())}const ra=Zi({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:qi,acceptCharset:Wi,accessKey:Wi,action:null,allow:null,allowFullScreen:Bi,allowPaymentRequest:Bi,allowUserMedia:Bi,alt:null,as:null,async:Bi,autoCapitalize:null,autoComplete:Wi,autoFocus:Bi,autoPlay:Bi,blocking:Wi,capture:null,charSet:null,checked:Bi,cite:null,className:Wi,cols:Vi,colSpan:null,content:null,contentEditable:Hi,controls:Bi,controlsList:Wi,coords:Vi|qi,crossOrigin:null,data:null,dateTime:null,decoding:null,default:Bi,defer:Bi,dir:null,dirName:null,disabled:Bi,download:$i,draggable:Hi,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:Bi,formTarget:null,headers:Wi,height:Vi,hidden:$i,high:Vi,href:null,hrefLang:null,htmlFor:Wi,httpEquiv:Wi,id:null,imageSizes:null,imageSrcSet:null,inert:Bi,inputMode:null,integrity:null,is:null,isMap:Bi,itemId:null,itemProp:Wi,itemRef:Wi,itemScope:Bi,itemType:Wi,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:Bi,low:Vi,manifest:null,max:null,maxLength:Vi,media:null,method:null,min:null,minLength:Vi,multiple:Bi,muted:Bi,name:null,nonce:null,noModule:Bi,noValidate:Bi,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:Bi,optimum:Vi,pattern:null,ping:Wi,placeholder:null,playsInline:Bi,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:Bi,referrerPolicy:null,rel:Wi,required:Bi,reversed:Bi,rows:Vi,rowSpan:Vi,sandbox:Wi,scope:null,scoped:Bi,seamless:Bi,selected:Bi,shadowRootClonable:Bi,shadowRootDelegatesFocus:Bi,shadowRootMode:null,shape:null,size:Vi,sizes:null,slot:null,span:Vi,spellCheck:Hi,src:null,srcDoc:null,srcLang:null,srcSet:null,start:Vi,step:null,style:null,tabIndex:Vi,target:null,title:null,translate:null,type:null,typeMustMatch:Bi,useMap:null,value:Hi,width:Vi,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:Wi,axis:null,background:null,bgColor:null,border:Vi,borderColor:null,bottomMargin:Vi,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:Bi,declare:Bi,event:null,face:null,frame:null,frameBorder:null,hSpace:Vi,leftMargin:Vi,link:null,longDesc:null,lowSrc:null,marginHeight:Vi,marginWidth:Vi,noResize:Bi,noHref:Bi,noShade:Bi,noWrap:Bi,object:null,profile:null,prompt:null,rev:null,rightMargin:Vi,rules:null,scheme:null,scrolling:Hi,standby:null,summary:null,text:null,topMargin:Vi,valueType:null,version:null,vAlign:null,vLink:null,vSpace:Vi,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:Bi,disableRemotePlayback:Bi,prefix:null,property:null,results:Vi,security:null,unselectable:null},space:"html",transform:na}),oa=Zi({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:Qi,accentHeight:Vi,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:Vi,amplitude:Vi,arabicForm:null,ascent:Vi,attributeName:null,attributeType:null,azimuth:Vi,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:Vi,by:null,calcMode:null,capHeight:Vi,className:Wi,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:Vi,diffuseConstant:Vi,direction:null,display:null,dur:null,divisor:Vi,dominantBaseline:null,download:Bi,dx:null,dy:null,edgeMode:null,editable:null,elevation:Vi,enableBackground:null,end:null,event:null,exponent:Vi,externalResourcesRequired:null,fill:null,fillOpacity:Vi,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:qi,g2:qi,glyphName:qi,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:Vi,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:Vi,horizOriginX:Vi,horizOriginY:Vi,id:null,ideographic:Vi,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:Vi,k:Vi,k1:Vi,k2:Vi,k3:Vi,k4:Vi,kernelMatrix:Qi,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:Vi,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:Vi,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:Vi,overlineThickness:Vi,paintOrder:null,panose1:null,path:null,pathLength:Vi,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:Wi,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:Vi,pointsAtY:Vi,pointsAtZ:Vi,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:Qi,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:Qi,rev:Qi,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:Qi,requiredFeatures:Qi,requiredFonts:Qi,requiredFormats:Qi,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:Vi,specularExponent:Vi,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:Vi,strikethroughThickness:Vi,string:null,stroke:null,strokeDashArray:Qi,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:Vi,strokeOpacity:Vi,strokeWidth:null,style:null,surfaceScale:Vi,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:Qi,tabIndex:Vi,tableValues:null,target:null,targetX:Vi,targetY:Vi,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:Qi,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:Vi,underlineThickness:Vi,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:Vi,values:null,vAlphabetic:Vi,vMathematical:Vi,vectorEffect:null,vHanging:Vi,vIdeographic:Vi,version:null,vertAdvY:Vi,vertOriginX:Vi,vertOriginY:Vi,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:Vi,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:ta}),ia=Zi({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),aa=Zi({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:na}),la=Zi({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),sa={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},ua=/[A-Z]/g,ca=/-[a-z]/g,fa=/^data[-\w.:]+$/i;function da(e){return"-"+e.toLowerCase()}function pa(e){return e.charAt(1).toUpperCase()}const ha=Mi([ea,ra,ia,aa,la],"html"),ma=Mi([ea,oa,ia,aa,la],"svg");var ga,ya,va,ba={};function ka(){if(va)return ba;va=1;var e=ba&&ba.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ba,"__esModule",{value:!0}),ba.default=function(e,n){var r=null;if(!e||"string"!=typeof e)return r;var o=(0,t.default)(e),i="function"==typeof n;return o.forEach((function(e){if("declaration"===e.type){var t=e.property,o=e.value;i?n(t,o,e):o&&((r=r||{})[t]=o)}})),r};var t=e(function(){if(ya)return ga;ya=1;var e=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,t=/\n/g,n=/^\s*/,r=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,o=/^:\s*/,i=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,l=/^\s+|\s+$/g,s="";function u(e){return e?e.replace(l,s):s}return ga=function(l,c){if("string"!=typeof l)throw new TypeError("First argument must be a string");if(!l)return[];c=c||{};var f=1,d=1;function p(e){var n=e.match(t);n&&(f+=n.length);var r=e.lastIndexOf("\n");d=~r?e.length-r:d+e.length}function h(){var e={line:f,column:d};return function(t){return t.position=new m(e),v(),t}}function m(e){this.start=e,this.end={line:f,column:d},this.source=c.source}function g(e){var t=new Error(c.source+":"+f+":"+d+": "+e);if(t.reason=e,t.filename=c.source,t.line=f,t.column=d,t.source=l,!c.silent)throw t}function y(e){var t=e.exec(l);if(t){var n=t[0];return p(n),l=l.slice(n.length),t}}function v(){y(n)}function b(e){var t;for(e=e||[];t=k();)!1!==t&&e.push(t);return e}function k(){var e=h();if("/"==l.charAt(0)&&"*"==l.charAt(1)){for(var t=2;s!=l.charAt(t)&&("*"!=l.charAt(t)||"/"!=l.charAt(t+1));)++t;if(t+=2,s===l.charAt(t-1))return g("End of comment missing");var n=l.slice(2,t-2);return d+=2,p(n),l=l.slice(t),d+=2,e({type:"comment",comment:n})}}function w(){var t=h(),n=y(r);if(n){if(k(),!y(o))return g("property missing ':'");var l=y(i),c=t({type:"declaration",property:u(n[0].replace(e,s)),value:l?u(l[0].replace(e,s)):s});return y(a),c}}return m.prototype.content=l,v(),function(){var e,t=[];for(b(t);e=w();)!1!==e&&(t.push(e),b(t));return t}()}}());return ba}var wa,xa,Sa,Ea={};function Ca(){if(wa)return Ea;wa=1,Object.defineProperty(Ea,"__esModule",{value:!0}),Ea.camelCase=void 0;var e=/^--[a-zA-Z0-9_-]+$/,t=/-([a-z])/g,n=/^[^-]+$/,r=/^-(webkit|moz|ms|o|khtml)-/,o=/^-(ms)-/,i=function(e,t){return t.toUpperCase()},a=function(e,t){return"".concat(t,"-")};return Ea.camelCase=function(l,s){return void 0===s&&(s={}),function(t){return!t||n.test(t)||e.test(t)}(l)?l:(l=l.toLowerCase(),(l=s.reactCompat?l.replace(o,a):l.replace(r,a)).replace(t,i))},Ea}const _a=e(function(){if(Sa)return xa;Sa=1;var e=(xa&&xa.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(ka()),t=Ca();function n(n,r){var o={};return n&&"string"==typeof n?((0,e.default)(n,(function(e,n){e&&n&&(o[(0,t.camelCase)(e,r)]=n)})),o):o}return n.default=n,xa=n}()),Ta=Na("end"),Pa=Na("start");function Na(e){return function(t){const n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function Oa(e){return e&&"object"==typeof e?"position"in e||"type"in e?Ra(e.position):"start"in e||"end"in e?Ra(e):"line"in e||"column"in e?La(e):"":""}function La(e){return Aa(e&&e.line)+":"+Aa(e&&e.column)}function Ra(e){return La(e&&e.start)+"-"+La(e&&e.end)}function Aa(e){return e&&"number"==typeof e?e:1}class ja extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",o={},i=!1;if(t&&(o="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!o.cause&&e&&(i=!0,r=e.message,o.cause=e),!o.ruleId&&!o.source&&"string"==typeof n){const e=n.indexOf(":");-1===e?o.ruleId=n:(o.source=n.slice(0,e),o.ruleId=n.slice(e+1))}if(!o.place&&o.ancestors&&o.ancestors){const e=o.ancestors[o.ancestors.length-1];e&&(o.place=e.position)}const a=o.place&&"start"in o.place?o.place.start:o.place;this.ancestors=o.ancestors||void 0,this.cause=o.cause||void 0,this.column=a?a.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=a?a.line:void 0,this.name=Oa(o.place)||"1:1",this.place=o.place||void 0,this.reason=this.message,this.ruleId=o.ruleId||void 0,this.source=o.source||void 0,this.stack=i&&o.cause&&"string"==typeof o.cause.stack?o.cause.stack:"",this.actual,this.expected,this.note,this.url}}ja.prototype.file="",ja.prototype.name="",ja.prototype.reason="",ja.prototype.message="",ja.prototype.stack="",ja.prototype.column=void 0,ja.prototype.line=void 0,ja.prototype.ancestors=void 0,ja.prototype.cause=void 0,ja.prototype.fatal=void 0,ja.prototype.place=void 0,ja.prototype.ruleId=void 0,ja.prototype.source=void 0;const za={}.hasOwnProperty,Da=new Map,Ma=/[A-Z]/g,Ia=new Set(["table","tbody","thead","tfoot","tr"]),Fa=new Set(["td","th"]),Ua="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function Ba(e,t){if(!t||void 0===t.Fragment)throw new TypeError("Expected `Fragment` in options");const n=t.filePath||void 0;let r;if(t.development){if("function"!=typeof t.jsxDEV)throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=function(e,t){return n;function n(n,r,o,i){const a=Array.isArray(o.children),l=Pa(n);return t(r,o,i,a,{columnNumber:l?l.column-1:void 0,fileName:e,lineNumber:l?l.line:void 0},void 0)}}(n,t.jsxDEV)}else{if("function"!=typeof t.jsx)throw new TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw new TypeError("Expected `jsxs` in production options");r=function(e,t,n){return r;function r(e,r,o,i){const a=Array.isArray(o.children)?n:t;return i?a(r,o,i):a(r,o)}}(0,t.jsx,t.jsxs)}const o={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:r,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?ma:ha,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},i=Ha(o,e,void 0);return i&&"string"!=typeof i?i:o.create(e,o.Fragment,{children:i||void 0},void 0)}function Ha(e,t,n){return"element"===t.type?function(e,t,n){const r=e.schema;let o=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(o=ma,e.schema=o);e.ancestors.push(t);const i=Qa(e,t.tagName,!1),a=function(e,t){const n={};let r,o;for(o in t.properties)if("children"!==o&&za.call(t.properties,o)){const i=qa(e,o,t.properties[o]);if(i){const[o,a]=i;e.tableCellAlignToStyle&&"align"===o&&"string"==typeof a&&Fa.has(t.tagName)?r=a:n[o]=a}}if(r){(n.style||(n.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=r}return n}(e,t);let l=Wa(e,t);Ia.has(t.tagName)&&(l=l.filter((function(e){return"string"!=typeof e||!("object"==typeof(t=e)?"text"===t.type&&zi(t.value):zi(t));var t})));return $a(e,a,i,t),Va(a,l),e.ancestors.pop(),e.schema=r,e.create(t,i,a,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){const n=t.data.estree.body[0];return n.type,e.evaluater.evaluateExpression(n.expression)}Ka(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){const r=e.schema;let o=r;"svg"===t.name&&"html"===r.space&&(o=ma,e.schema=o);e.ancestors.push(t);const i=null===t.name?e.Fragment:Qa(e,t.name,!0),a=function(e,t){const n={};for(const r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){const t=r.data.estree.body[0];t.type;const o=t.expression;o.type;const i=o.properties[0];i.type,Object.assign(n,e.evaluater.evaluateExpression(i.argument))}else Ka(e,t.position);else{const o=r.name;let i;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){const t=r.value.data.estree.body[0];t.type,i=e.evaluater.evaluateExpression(t.expression)}else Ka(e,t.position);else i=null===r.value||r.value;n[o]=i}return n}(e,t),l=Wa(e,t);return $a(e,a,i,t),Va(a,l),e.ancestors.pop(),e.schema=r,e.create(t,i,a,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);Ka(e,t.position)}(e,t):"root"===t.type?function(e,t,n){const r={};return Va(r,Wa(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?function(e,t){return t.value}(0,t):void 0}function $a(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function Va(e,t){if(t.length>0){const n=t.length>1?t:t[0];n&&(e.children=n)}}function Wa(e,t){const n=[];let r=-1;const o=e.passKeys?new Map:Da;for(;++r<t.children.length;){const i=t.children[r];let a;if(e.passKeys){const e="element"===i.type?i.tagName:"mdxJsxFlowElement"===i.type||"mdxJsxTextElement"===i.type?i.name:void 0;if(e){const t=o.get(e)||0;a=e+"-"+t,o.set(e,t+1)}}const l=Ha(e,i,a);void 0!==l&&n.push(l)}return n}function qa(e,t,n){const r=function(e,t){const n=Ii(t);let r=t,o=Fi;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&fa.test(t)){if("-"===t.charAt(4)){const e=t.slice(5).replace(ca,pa);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{const e=t.slice(4);if(!ca.test(e)){let n=e.replace(ua,da);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}o=Ji}return new o(r,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e){const t={};return(""===e[e.length-1]?[...e,""]:e).join((t.padRight?" ":"")+","+(!1===t.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return _a(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};const t=n,r=new ja("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:t,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw r.file=e.filePath||void 0,r.url=Ua+"#cannot-parse-style-attribute",r}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){const t={};let n;for(n in e)za.call(e,n)&&(t[Ya(n)]=e[n]);return t}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?sa[r.property]||r.property:r.attribute,n]}}function Qa(e,t,n){let r;if(n)if(t.includes(".")){const e=t.split(".");let n,o=-1;for(;++o<e.length;){const t=Ai(e[o])?{type:"Identifier",name:e[o]}:{type:"Literal",value:e[o]};n=n?{type:"MemberExpression",object:n,property:t,computed:Boolean(o&&"Literal"===t.type),optional:!1}:t}r=n}else r=Ai(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){const t=r.value;return za.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);Ka(e)}function Ka(e,t){const n=new ja("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=Ua+"#cannot-handle-mdx-estrees-without-createevaluater",n}function Ya(e){let t=e.replace(Ma,Xa);return"ms-"===t.slice(0,3)&&(t="-"+t),t}function Xa(e){return"-"+e.toLowerCase()}const Ja={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]},Ga={};function Za(e,t){return el(e,"boolean"!=typeof Ga.includeImageAlt||Ga.includeImageAlt,"boolean"!=typeof Ga.includeHtml||Ga.includeHtml)}function el(e,t,n){if(function(e){return Boolean(e&&"object"==typeof e)}(e)){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return tl(e.children,t,n)}return Array.isArray(e)?tl(e,t,n):""}function tl(e,t,n){const r=[];let o=-1;for(;++o<e.length;)r[o]=el(e[o],t,n);return r.join("")}const nl=document.createElement("i");function rl(e){const t="&"+e+";";nl.innerHTML=t;const n=nl.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&(n!==t&&n)}function ol(e,t,n,r){const o=e.length;let i,a=0;if(t=t<0?-t>o?0:o+t:t>o?o:t,n=n>0?n:0,r.length<1e4)i=Array.from(r),i.unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);a<r.length;)i=r.slice(a,a+1e4),i.unshift(t,0),e.splice(...i),a+=1e4,t+=1e4}function il(e,t){return e.length>0?(ol(e,e.length,0,t),e):t}const al={}.hasOwnProperty;function ll(e){const t={};let n=-1;for(;++n<e.length;)sl(t,e[n]);return t}function sl(e,t){let n;for(n in t){const r=(al.call(e,n)?e[n]:void 0)||(e[n]={}),o=t[n];let i;if(o)for(i in o){al.call(r,i)||(r[i]=[]);const e=o[i];ul(r[i],Array.isArray(e)?e:e?[e]:[])}}}function ul(e,t){let n=-1;const r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);ol(e,0,0,r)}function cl(e,t){const n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||!(65535&~n)||65534==(65535&n)||n>1114111?"�":String.fromCodePoint(n)}function fl(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const dl=El(/[A-Za-z]/),pl=El(/[\dA-Za-z]/),hl=El(/[#-'*+\--9=?A-Z^-~]/);function ml(e){return null!==e&&(e<32||127===e)}const gl=El(/\d/),yl=El(/[\dA-Fa-f]/),vl=El(/[!-/:-@[-`{-~]/);function bl(e){return null!==e&&e<-2}function kl(e){return null!==e&&(e<0||32===e)}function wl(e){return-2===e||-1===e||32===e}const xl=El(new RegExp("\\p{P}|\\p{S}","u")),Sl=El(/\s/);function El(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function Cl(e){const t=[];let n=-1,r=0,o=0;for(;++n<e.length;){const i=e.charCodeAt(n);let a="";if(37===i&&pl(e.charCodeAt(n+1))&&pl(e.charCodeAt(n+2)))o=2;else if(i<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(i))||(a=String.fromCharCode(i));else if(i>55295&&i<57344){const t=e.charCodeAt(n+1);i<56320&&t>56319&&t<57344?(a=String.fromCharCode(i,t),o=1):a="�"}else a=String.fromCharCode(i);a&&(t.push(e.slice(r,n),encodeURIComponent(a)),r=n+o+1,a=""),o&&(n+=o,o=0)}return t.join("")+e.slice(r)}function _l(e,t,n,r){const o=r?r-1:Number.POSITIVE_INFINITY;let i=0;return function(r){if(wl(r))return e.enter(n),a(r);return t(r)};function a(r){return wl(r)&&i++<o?(e.consume(r),a):(e.exit(n),t(r))}}const Tl={tokenize:function(e){const t=e.attempt(this.parser.constructs.contentInitial,(function(n){if(null===n)return void e.consume(n);return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),_l(e,t,"linePrefix")}),(function(t){return e.enter("paragraph"),r(t)}));let n;return t;function r(t){const r=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=r),n=r,o(t)}function o(t){return null===t?(e.exit("chunkText"),e.exit("paragraph"),void e.consume(t)):bl(t)?(e.consume(t),e.exit("chunkText"),r):(e.consume(t),o)}}};const Pl={tokenize:function(e){const t=this,n=[];let r,o,i,a=0;return l;function l(r){if(a<n.length){const o=n[a];return t.containerState=o[1],e.attempt(o[0].continuation,s,u)(r)}return u(r)}function s(e){if(a++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,r&&v();const n=t.events.length;let o,i=n;for(;i--;)if("exit"===t.events[i][0]&&"chunkFlow"===t.events[i][1].type){o=t.events[i][1].end;break}y(a);let l=n;for(;l<t.events.length;)t.events[l][1].end={...o},l++;return ol(t.events,i+1,0,t.events.slice(n)),t.events.length=l,u(e)}return l(e)}function u(o){if(a===n.length){if(!r)return d(o);if(r.currentConstruct&&r.currentConstruct.concrete)return h(o);t.interrupt=Boolean(r.currentConstruct&&!r._gfmTableDynamicInterruptHack)}return t.containerState={},e.check(Nl,c,f)(o)}function c(e){return r&&v(),y(a),d(e)}function f(e){return t.parser.lazy[t.now().line]=a!==n.length,i=t.now().offset,h(e)}function d(n){return t.containerState={},e.attempt(Nl,p,h)(n)}function p(e){return a++,n.push([t.currentConstruct,t.containerState]),d(e)}function h(n){return null===n?(r&&v(),y(0),void e.consume(n)):(r=r||t.parser.flow(t.now()),e.enter("chunkFlow",{_tokenizer:r,contentType:"flow",previous:o}),m(n))}function m(n){return null===n?(g(e.exit("chunkFlow"),!0),y(0),void e.consume(n)):bl(n)?(e.consume(n),g(e.exit("chunkFlow")),a=0,t.interrupt=void 0,l):(e.consume(n),m)}function g(e,n){const l=t.sliceStream(e);if(n&&l.push(null),e.previous=o,o&&(o.next=e),o=e,r.defineSkip(e.start),r.write(l),t.parser.lazy[e.start.line]){let e=r.events.length;for(;e--;)if(r.events[e][1].start.offset<i&&(!r.events[e][1].end||r.events[e][1].end.offset>i))return;const n=t.events.length;let o,l,s=n;for(;s--;)if("exit"===t.events[s][0]&&"chunkFlow"===t.events[s][1].type){if(o){l=t.events[s][1].end;break}o=!0}for(y(a),e=n;e<t.events.length;)t.events[e][1].end={...l},e++;ol(t.events,s+1,0,t.events.slice(n)),t.events.length=e}}function y(r){let o=n.length;for(;o-- >r;){const r=n[o];t.containerState=r[1],r[0].exit.call(t,e)}n.length=r}function v(){r.write([null]),o=void 0,r=void 0,t.containerState._closeFlow=void 0}}},Nl={tokenize:function(e,t,n){return _l(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};function Ol(e){return null===e||kl(e)||Sl(e)?1:xl(e)?2:void 0}function Ll(e,t,n){const r=[];let o=-1;for(;++o<e.length;){const i=e[o].resolveAll;i&&!r.includes(i)&&(t=i(t,n),r.push(i))}return t}const Rl={name:"attention",resolveAll:function(e,t){let n,r,o,i,a,l,s,u,c=-1;for(;++c<e.length;)if("enter"===e[c][0]&&"attentionSequence"===e[c][1].type&&e[c][1]._close)for(n=c;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[c][1]).charCodeAt(0)){if((e[n][1]._close||e[c][1]._open)&&(e[c][1].end.offset-e[c][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[c][1].end.offset-e[c][1].start.offset)%3))continue;l=e[n][1].end.offset-e[n][1].start.offset>1&&e[c][1].end.offset-e[c][1].start.offset>1?2:1;const f={...e[n][1].end},d={...e[c][1].start};Al(f,-l),Al(d,l),i={type:l>1?"strongSequence":"emphasisSequence",start:f,end:{...e[n][1].end}},a={type:l>1?"strongSequence":"emphasisSequence",start:{...e[c][1].start},end:d},o={type:l>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[c][1].start}},r={type:l>1?"strong":"emphasis",start:{...i.start},end:{...a.end}},e[n][1].end={...i.start},e[c][1].start={...a.end},s=[],e[n][1].end.offset-e[n][1].start.offset&&(s=il(s,[["enter",e[n][1],t],["exit",e[n][1],t]])),s=il(s,[["enter",r,t],["enter",i,t],["exit",i,t],["enter",o,t]]),s=il(s,Ll(t.parser.constructs.insideSpan.null,e.slice(n+1,c),t)),s=il(s,[["exit",o,t],["enter",a,t],["exit",a,t],["exit",r,t]]),e[c][1].end.offset-e[c][1].start.offset?(u=2,s=il(s,[["enter",e[c][1],t],["exit",e[c][1],t]])):u=0,ol(e,n-1,c-n+3,s),c=n+s.length-u-2;break}c=-1;for(;++c<e.length;)"attentionSequence"===e[c][1].type&&(e[c][1].type="data");return e},tokenize:function(e,t){const n=this.parser.constructs.attentionMarkers.null,r=this.previous,o=Ol(r);let i;return function(t){return i=t,e.enter("attentionSequence"),a(t)};function a(l){if(l===i)return e.consume(l),a;const s=e.exit("attentionSequence"),u=Ol(l),c=!u||2===u&&o||n.includes(l),f=!o||2===o&&u||n.includes(r);return s._open=Boolean(42===i?c:c&&(o||!f)),s._close=Boolean(42===i?f:f&&(u||!c)),t(l)}}};function Al(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}const jl={name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),o};function o(t){return dl(t)?(e.consume(t),i):64===t?n(t):s(t)}function i(e){return 43===e||45===e||46===e||pl(e)?(r=1,a(e)):s(e)}function a(t){return 58===t?(e.consume(t),r=0,l):(43===t||45===t||46===t||pl(t))&&r++<32?(e.consume(t),a):(r=0,s(t))}function l(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||ml(r)?n(r):(e.consume(r),l)}function s(t){return 64===t?(e.consume(t),u):hl(t)?(e.consume(t),s):n(t)}function u(e){return pl(e)?c(e):n(e)}function c(n){return 46===n?(e.consume(n),r=0,u):62===n?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(n),e.exit("autolinkMarker"),e.exit("autolink"),t):f(n)}function f(t){if((45===t||pl(t))&&r++<63){const n=45===t?f:c;return e.consume(t),n}return n(t)}}};const zl={partial:!0,tokenize:function(e,t,n){return function(t){return wl(t)?_l(e,r,"linePrefix")(t):r(t)};function r(e){return null===e||bl(e)?t(e):n(e)}}};const Dl={continuation:{tokenize:function(e,t,n){const r=this;return function(t){if(wl(t))return _l(e,o,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t);return o(t)};function o(r){return e.attempt(Dl,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){const r=this;return function(t){if(62===t){const n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),o}return n(t)};function o(n){return wl(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};const Ml={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return vl(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}};const Il={name:"characterReference",tokenize:function(e,t,n){const r=this;let o,i,a=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),l};function l(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),s):(e.enter("characterReferenceValue"),o=31,i=pl,u(t))}function s(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),o=6,i=yl,u):(e.enter("characterReferenceValue"),o=7,i=gl,u(t))}function u(l){if(59===l&&a){const o=e.exit("characterReferenceValue");return i!==pl||rl(r.sliceSerialize(o))?(e.enter("characterReferenceMarker"),e.consume(l),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(l)}return i(l)&&a++<o?(e.consume(l),u):n(l)}}};const Fl={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){if(null===t)return n(t);return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o};function o(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},Ul={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){const r=this,o={partial:!0,tokenize:function(e,t,n){let o=0;return a;function a(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),s}function s(t){return e.enter("codeFencedFence"),wl(t)?_l(e,u,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):u(t)}function u(t){return t===i?(e.enter("codeFencedFenceSequence"),c(t)):n(t)}function c(t){return t===i?(o++,e.consume(t),c):o>=l?(e.exit("codeFencedFenceSequence"),wl(t)?_l(e,f,"whitespace")(t):f(t)):n(t)}function f(r){return null===r||bl(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}};let i,a=0,l=0;return function(t){return function(t){const n=r.events[r.events.length-1];return a=n&&"linePrefix"===n[1].type?n[2].sliceSerialize(n[1],!0).length:0,i=t,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),s(t)}(t)};function s(t){return t===i?(l++,e.consume(t),s):l<3?n(t):(e.exit("codeFencedFenceSequence"),wl(t)?_l(e,u,"whitespace")(t):u(t))}function u(n){return null===n||bl(n)?(e.exit("codeFencedFence"),r.interrupt?t(n):e.check(Fl,p,v)(n)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),c(n))}function c(t){return null===t||bl(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),u(t)):wl(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),_l(e,f,"whitespace")(t)):96===t&&t===i?n(t):(e.consume(t),c)}function f(t){return null===t||bl(t)?u(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),d(t))}function d(t){return null===t||bl(t)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),u(t)):96===t&&t===i?n(t):(e.consume(t),d)}function p(t){return e.attempt(o,v,h)(t)}function h(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),m}function m(t){return a>0&&wl(t)?_l(e,g,"linePrefix",a+1)(t):g(t)}function g(t){return null===t||bl(t)?e.check(Fl,p,v)(t):(e.enter("codeFlowValue"),y(t))}function y(t){return null===t||bl(t)?(e.exit("codeFlowValue"),g(t)):(e.consume(t),y)}function v(n){return e.exit("codeFenced"),t(n)}}};const Bl={name:"codeIndented",tokenize:function(e,t,n){const r=this;return function(t){return e.enter("codeIndented"),_l(e,o,"linePrefix",5)(t)};function o(e){const t=r.events[r.events.length-1];return t&&"linePrefix"===t[1].type&&t[2].sliceSerialize(t[1],!0).length>=4?i(e):n(e)}function i(t){return null===t?l(t):bl(t)?e.attempt(Hl,i,l)(t):(e.enter("codeFlowValue"),a(t))}function a(t){return null===t||bl(t)?(e.exit("codeFlowValue"),i(t)):(e.consume(t),a)}function l(n){return e.exit("codeIndented"),t(n)}}},Hl={partial:!0,tokenize:function(e,t,n){const r=this;return o;function o(t){return r.parser.lazy[r.now().line]?n(t):bl(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o):_l(e,i,"linePrefix",5)(t)}function i(e){const i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(e):bl(e)?o(e):n(e)}}};const $l={name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,o=3;if(!("lineEnding"!==e[o][1].type&&"space"!==e[o][1].type||"lineEnding"!==e[r][1].type&&"space"!==e[r][1].type))for(t=o;++t<r;)if("codeTextData"===e[t][1].type){e[o][1].type="codeTextPadding",e[r][1].type="codeTextPadding",o+=2,r-=2;break}t=o-1,r++;for(;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):t!==r&&"lineEnding"!==e[t][1].type||(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,o,i=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),a(t)};function a(t){return 96===t?(e.consume(t),i++,a):(e.exit("codeTextSequence"),l(t))}function l(t){return null===t?n(t):32===t?(e.enter("space"),e.consume(t),e.exit("space"),l):96===t?(o=e.enter("codeTextSequence"),r=0,u(t)):bl(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),l):(e.enter("codeTextData"),s(t))}function s(t){return null===t||32===t||96===t||bl(t)?(e.exit("codeTextData"),l(t)):(e.consume(t),s)}function u(n){return 96===n?(e.consume(n),r++,u):r===i?(e.exit("codeTextSequence"),e.exit("codeText"),t(n)):(o.type="codeTextData",s(n))}}};class Vl{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){const n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){const r=t||0;this.setCursor(Math.trunc(e));const o=this.right.splice(this.right.length-r,Number.POSITIVE_INFINITY);return n&&Wl(this.left,n),o.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),Wl(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),Wl(this.right,e.reverse())}setCursor(e){if(!(e===this.left.length||e>this.left.length&&0===this.right.length||e<0&&0===this.left.length))if(e<this.left.length){const t=this.left.splice(e,Number.POSITIVE_INFINITY);Wl(this.right,t.reverse())}else{const t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);Wl(this.left,t.reverse())}}}function Wl(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function ql(e){const t={};let n,r,o,i,a,l,s,u=-1;const c=new Vl(e);for(;++u<c.length;){for(;u in t;)u=t[u];if(n=c.get(u),u&&"chunkFlow"===n[1].type&&"listItemPrefix"===c.get(u-1)[1].type&&(l=n[1]._tokenizer.events,o=0,o<l.length&&"lineEndingBlank"===l[o][1].type&&(o+=2),o<l.length&&"content"===l[o][1].type))for(;++o<l.length&&"content"!==l[o][1].type;)"chunkText"===l[o][1].type&&(l[o][1]._isInFirstContentOfListItem=!0,o++);if("enter"===n[0])n[1].contentType&&(Object.assign(t,Ql(c,u)),u=t[u],s=!0);else if(n[1]._container){for(o=u,r=void 0;o--;)if(i=c.get(o),"lineEnding"===i[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(r&&(c.get(r)[1].type="lineEndingBlank"),i[1].type="lineEnding",r=o);else if("linePrefix"!==i[1].type&&"listItemIndent"!==i[1].type)break;r&&(n[1].end={...c.get(r)[1].start},a=c.slice(r,u),a.unshift(n),c.splice(r,u-r+1,a))}}return ol(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!s}function Ql(e,t){const n=e.get(t)[1],r=e.get(t)[2];let o=t-1;const i=[];let a=n._tokenizer;a||(a=r.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(a._contentTypeTextTrailing=!0));const l=a.events,s=[],u={};let c,f,d=-1,p=n,h=0,m=0;const g=[m];for(;p;){for(;e.get(++o)[1]!==p;);i.push(o),p._tokenizer||(c=r.sliceStream(p),p.next||c.push(null),f&&a.defineSkip(p.start),p._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=!0),a.write(c),p._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=void 0)),f=p,p=p.next}for(p=n;++d<l.length;)"exit"===l[d][0]&&"enter"===l[d-1][0]&&l[d][1].type===l[d-1][1].type&&l[d][1].start.line!==l[d][1].end.line&&(m=d+1,g.push(m),p._tokenizer=void 0,p.previous=void 0,p=p.next);for(a.events=[],p?(p._tokenizer=void 0,p.previous=void 0):g.pop(),d=g.length;d--;){const t=l.slice(g[d],g[d+1]),n=i.pop();s.push([n,n+t.length-1]),e.splice(n,2,t)}for(s.reverse(),d=-1;++d<s.length;)u[h+s[d][0]]=h+s[d][1],h+=s[d][1]-s[d][0]-1;return u}const Kl={resolve:function(e){return ql(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?o(t):bl(t)?e.check(Yl,i,o)(t):(e.consume(t),r)}function o(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function i(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},Yl={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),_l(e,o,"linePrefix")};function o(o){if(null===o||bl(o))return n(o);const i=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(o):e.interrupt(r.parser.constructs.flow,n,t)(o)}}};function Xl(e,t,n,r,o,i,a,l,s){const u=s||Number.POSITIVE_INFINITY;let c=0;return function(t){if(60===t)return e.enter(r),e.enter(o),e.enter(i),e.consume(t),e.exit(i),f;if(null===t||32===t||41===t||ml(t))return n(t);return e.enter(r),e.enter(a),e.enter(l),e.enter("chunkString",{contentType:"string"}),h(t)};function f(n){return 62===n?(e.enter(i),e.consume(n),e.exit(i),e.exit(o),e.exit(r),t):(e.enter(l),e.enter("chunkString",{contentType:"string"}),d(n))}function d(t){return 62===t?(e.exit("chunkString"),e.exit(l),f(t)):null===t||60===t||bl(t)?n(t):(e.consume(t),92===t?p:d)}function p(t){return 60===t||62===t||92===t?(e.consume(t),d):d(t)}function h(o){return c||null!==o&&41!==o&&!kl(o)?c<u&&40===o?(e.consume(o),c++,h):41===o?(e.consume(o),c--,h):null===o||32===o||40===o||ml(o)?n(o):(e.consume(o),92===o?m:h):(e.exit("chunkString"),e.exit(l),e.exit(a),e.exit(r),t(o))}function m(t){return 40===t||41===t||92===t?(e.consume(t),h):h(t)}}function Jl(e,t,n,r,o,i){const a=this;let l,s=0;return function(t){return e.enter(r),e.enter(o),e.consume(t),e.exit(o),e.enter(i),u};function u(f){return s>999||null===f||91===f||93===f&&!l||94===f&&!s&&"_hiddenFootnoteSupport"in a.parser.constructs?n(f):93===f?(e.exit(i),e.enter(o),e.consume(f),e.exit(o),e.exit(r),t):bl(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),u):(e.enter("chunkString",{contentType:"string"}),c(f))}function c(t){return null===t||91===t||93===t||bl(t)||s++>999?(e.exit("chunkString"),u(t)):(e.consume(t),l||(l=!wl(t)),92===t?f:c)}function f(t){return 91===t||92===t||93===t?(e.consume(t),s++,c):c(t)}}function Gl(e,t,n,r,o,i){let a;return function(t){if(34===t||39===t||40===t)return e.enter(r),e.enter(o),e.consume(t),e.exit(o),a=40===t?41:t,l;return n(t)};function l(n){return n===a?(e.enter(o),e.consume(n),e.exit(o),e.exit(r),t):(e.enter(i),s(n))}function s(t){return t===a?(e.exit(i),l(a)):null===t?n(t):bl(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),_l(e,s,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),u(t))}function u(t){return t===a||null===t||bl(t)?(e.exit("chunkString"),s(t)):(e.consume(t),92===t?c:u)}function c(t){return t===a||92===t?(e.consume(t),u):u(t)}}function Zl(e,t){let n;return function r(o){if(bl(o))return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),n=!0,r;if(wl(o))return _l(e,r,n?"linePrefix":"lineSuffix")(o);return t(o)}}const es={name:"definition",tokenize:function(e,t,n){const r=this;let o;return function(t){return e.enter("definition"),function(t){return Jl.call(r,e,i,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(t)}(t)};function i(t){return o=fl(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),a):n(t)}function a(t){return kl(t)?Zl(e,l)(t):l(t)}function l(t){return Xl(e,s,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function s(t){return e.attempt(ts,u,u)(t)}function u(t){return wl(t)?_l(e,c,"whitespace")(t):c(t)}function c(i){return null===i||bl(i)?(e.exit("definition"),r.parser.defined.push(o),t(i)):n(i)}}},ts={partial:!0,tokenize:function(e,t,n){return function(t){return kl(t)?Zl(e,r)(t):n(t)};function r(t){return Gl(e,o,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function o(t){return wl(t)?_l(e,i,"whitespace")(t):i(t)}function i(e){return null===e||bl(e)?t(e):n(e)}}};const ns={name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return bl(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}};const rs={name:"headingAtx",resolve:function(e,t){let n,r,o=e.length-2,i=3;"whitespace"===e[i][1].type&&(i+=2);o-2>i&&"whitespace"===e[o][1].type&&(o-=2);"atxHeadingSequence"===e[o][1].type&&(i===o-1||o-4>i&&"whitespace"===e[o-2][1].type)&&(o-=i+1===o?2:4);o>i&&(n={type:"atxHeadingText",start:e[i][1].start,end:e[o][1].end},r={type:"chunkText",start:e[i][1].start,end:e[o][1].end,contentType:"text"},ol(e,i,o-i+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]]));return e},tokenize:function(e,t,n){let r=0;return function(t){return e.enter("atxHeading"),function(t){return e.enter("atxHeadingSequence"),o(t)}(t)};function o(t){return 35===t&&r++<6?(e.consume(t),o):null===t||kl(t)?(e.exit("atxHeadingSequence"),i(t)):n(t)}function i(n){return 35===n?(e.enter("atxHeadingSequence"),a(n)):null===n||bl(n)?(e.exit("atxHeading"),t(n)):wl(n)?_l(e,i,"whitespace")(n):(e.enter("atxHeadingText"),l(n))}function a(t){return 35===t?(e.consume(t),a):(e.exit("atxHeadingSequence"),i(t))}function l(t){return null===t||35===t||kl(t)?(e.exit("atxHeadingText"),i(t)):(e.consume(t),l)}}};const os=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],is=["pre","script","style","textarea"],as={concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2));return e},tokenize:function(e,t,n){const r=this;let o,i,a,l,s;return function(t){return function(t){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),u}(t)};function u(l){return 33===l?(e.consume(l),c):47===l?(e.consume(l),i=!0,p):63===l?(e.consume(l),o=3,r.interrupt?t:j):dl(l)?(e.consume(l),a=String.fromCharCode(l),h):n(l)}function c(i){return 45===i?(e.consume(i),o=2,f):91===i?(e.consume(i),o=5,l=0,d):dl(i)?(e.consume(i),o=4,r.interrupt?t:j):n(i)}function f(o){return 45===o?(e.consume(o),r.interrupt?t:j):n(o)}function d(o){const i="CDATA[";return o===i.charCodeAt(l++)?(e.consume(o),6===l?r.interrupt?t:_:d):n(o)}function p(t){return dl(t)?(e.consume(t),a=String.fromCharCode(t),h):n(t)}function h(l){if(null===l||47===l||62===l||kl(l)){const s=47===l,u=a.toLowerCase();return s||i||!is.includes(u)?os.includes(a.toLowerCase())?(o=6,s?(e.consume(l),m):r.interrupt?t(l):_(l)):(o=7,r.interrupt&&!r.parser.lazy[r.now().line]?n(l):i?g(l):y(l)):(o=1,r.interrupt?t(l):_(l))}return 45===l||pl(l)?(e.consume(l),a+=String.fromCharCode(l),h):n(l)}function m(o){return 62===o?(e.consume(o),r.interrupt?t:_):n(o)}function g(t){return wl(t)?(e.consume(t),g):E(t)}function y(t){return 47===t?(e.consume(t),E):58===t||95===t||dl(t)?(e.consume(t),v):wl(t)?(e.consume(t),y):E(t)}function v(t){return 45===t||46===t||58===t||95===t||pl(t)?(e.consume(t),v):b(t)}function b(t){return 61===t?(e.consume(t),k):wl(t)?(e.consume(t),b):y(t)}function k(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),s=t,w):wl(t)?(e.consume(t),k):x(t)}function w(t){return t===s?(e.consume(t),s=null,S):null===t||bl(t)?n(t):(e.consume(t),w)}function x(t){return null===t||34===t||39===t||47===t||60===t||61===t||62===t||96===t||kl(t)?b(t):(e.consume(t),x)}function S(e){return 47===e||62===e||wl(e)?y(e):n(e)}function E(t){return 62===t?(e.consume(t),C):n(t)}function C(t){return null===t||bl(t)?_(t):wl(t)?(e.consume(t),C):n(t)}function _(t){return 45===t&&2===o?(e.consume(t),O):60===t&&1===o?(e.consume(t),L):62===t&&4===o?(e.consume(t),z):63===t&&3===o?(e.consume(t),j):93===t&&5===o?(e.consume(t),A):!bl(t)||6!==o&&7!==o?null===t||bl(t)?(e.exit("htmlFlowData"),T(t)):(e.consume(t),_):(e.exit("htmlFlowData"),e.check(ls,D,T)(t))}function T(t){return e.check(ss,P,D)(t)}function P(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),N}function N(t){return null===t||bl(t)?T(t):(e.enter("htmlFlowData"),_(t))}function O(t){return 45===t?(e.consume(t),j):_(t)}function L(t){return 47===t?(e.consume(t),a="",R):_(t)}function R(t){if(62===t){const n=a.toLowerCase();return is.includes(n)?(e.consume(t),z):_(t)}return dl(t)&&a.length<8?(e.consume(t),a+=String.fromCharCode(t),R):_(t)}function A(t){return 93===t?(e.consume(t),j):_(t)}function j(t){return 62===t?(e.consume(t),z):45===t&&2===o?(e.consume(t),j):_(t)}function z(t){return null===t||bl(t)?(e.exit("htmlFlowData"),D(t)):(e.consume(t),z)}function D(n){return e.exit("htmlFlow"),t(n)}}},ls={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(zl,t,n)}}},ss={partial:!0,tokenize:function(e,t,n){const r=this;return function(t){if(bl(t))return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o;return n(t)};function o(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}};const us={name:"htmlText",tokenize:function(e,t,n){const r=this;let o,i,a;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),l};function l(t){return 33===t?(e.consume(t),s):47===t?(e.consume(t),k):63===t?(e.consume(t),v):dl(t)?(e.consume(t),S):n(t)}function s(t){return 45===t?(e.consume(t),u):91===t?(e.consume(t),i=0,p):dl(t)?(e.consume(t),y):n(t)}function u(t){return 45===t?(e.consume(t),d):n(t)}function c(t){return null===t?n(t):45===t?(e.consume(t),f):bl(t)?(a=c,R(t)):(e.consume(t),c)}function f(t){return 45===t?(e.consume(t),d):c(t)}function d(e){return 62===e?L(e):45===e?f(e):c(e)}function p(t){const r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),6===i?h:p):n(t)}function h(t){return null===t?n(t):93===t?(e.consume(t),m):bl(t)?(a=h,R(t)):(e.consume(t),h)}function m(t){return 93===t?(e.consume(t),g):h(t)}function g(t){return 62===t?L(t):93===t?(e.consume(t),g):h(t)}function y(t){return null===t||62===t?L(t):bl(t)?(a=y,R(t)):(e.consume(t),y)}function v(t){return null===t?n(t):63===t?(e.consume(t),b):bl(t)?(a=v,R(t)):(e.consume(t),v)}function b(e){return 62===e?L(e):v(e)}function k(t){return dl(t)?(e.consume(t),w):n(t)}function w(t){return 45===t||pl(t)?(e.consume(t),w):x(t)}function x(t){return bl(t)?(a=x,R(t)):wl(t)?(e.consume(t),x):L(t)}function S(t){return 45===t||pl(t)?(e.consume(t),S):47===t||62===t||kl(t)?E(t):n(t)}function E(t){return 47===t?(e.consume(t),L):58===t||95===t||dl(t)?(e.consume(t),C):bl(t)?(a=E,R(t)):wl(t)?(e.consume(t),E):L(t)}function C(t){return 45===t||46===t||58===t||95===t||pl(t)?(e.consume(t),C):_(t)}function _(t){return 61===t?(e.consume(t),T):bl(t)?(a=_,R(t)):wl(t)?(e.consume(t),_):E(t)}function T(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),o=t,P):bl(t)?(a=T,R(t)):wl(t)?(e.consume(t),T):(e.consume(t),N)}function P(t){return t===o?(e.consume(t),o=void 0,O):null===t?n(t):bl(t)?(a=P,R(t)):(e.consume(t),P)}function N(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||kl(t)?E(t):(e.consume(t),N)}function O(e){return 47===e||62===e||kl(e)?E(e):n(e)}function L(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function R(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),A}function A(t){return wl(t)?_l(e,j,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):j(t)}function j(t){return e.enter("htmlTextData"),a(t)}}};const cs={name:"labelEnd",resolveAll:function(e){let t=-1;const n=[];for(;++t<e.length;){const r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){const e="labelImage"===r.type?4:2;r.type="data",t+=e}}e.length!==n.length&&ol(e,0,e.length,n);return e},resolveTo:function(e,t){let n,r,o,i,a=e.length,l=0;for(;a--;)if(n=e[a][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[a][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(o){if("enter"===e[a][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=a,"labelLink"!==n.type)){l=2;break}}else"labelEnd"===n.type&&(o=a);const s={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},u={type:"label",start:{...e[r][1].start},end:{...e[o][1].end}},c={type:"labelText",start:{...e[r+l+2][1].end},end:{...e[o-2][1].start}};return i=[["enter",s,t],["enter",u,t]],i=il(i,e.slice(r+1,r+l+3)),i=il(i,[["enter",c,t]]),i=il(i,Ll(t.parser.constructs.insideSpan.null,e.slice(r+l+4,o-3),t)),i=il(i,[["exit",c,t],e[o-2],e[o-1],["exit",u,t]]),i=il(i,e.slice(o+1)),i=il(i,[["exit",s,t]]),ol(e,r,e.length,i),e},tokenize:function(e,t,n){const r=this;let o,i,a=r.events.length;for(;a--;)if(("labelImage"===r.events[a][1].type||"labelLink"===r.events[a][1].type)&&!r.events[a][1]._balanced){o=r.events[a][1];break}return function(t){if(!o)return n(t);if(o._inactive)return c(t);return i=r.parser.defined.includes(fl(r.sliceSerialize({start:o.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),l};function l(t){return 40===t?e.attempt(fs,u,i?u:c)(t):91===t?e.attempt(ds,u,i?s:c)(t):i?u(t):c(t)}function s(t){return e.attempt(ps,u,c)(t)}function u(e){return t(e)}function c(e){return o._balanced=!0,n(e)}}},fs={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return kl(t)?Zl(e,o)(t):o(t)}function o(t){return 41===t?u(t):Xl(e,i,a,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function i(t){return kl(t)?Zl(e,l)(t):u(t)}function a(e){return n(e)}function l(t){return 34===t||39===t||40===t?Gl(e,s,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):u(t)}function s(t){return kl(t)?Zl(e,u)(t):u(t)}function u(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},ds={tokenize:function(e,t,n){const r=this;return function(t){return Jl.call(r,e,o,i,"reference","referenceMarker","referenceString")(t)};function o(e){return r.parser.defined.includes(fl(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function i(e){return n(e)}}},ps={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}};const hs={name:"labelStartImage",resolveAll:cs.resolveAll,tokenize:function(e,t,n){const r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),o};function o(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),i):n(t)}function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};const ms={name:"labelStartLink",resolveAll:cs.resolveAll,tokenize:function(e,t,n){const r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),o};function o(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};const gs={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),_l(e,t,"linePrefix")}}};const ys={name:"thematicBreak",tokenize:function(e,t,n){let r,o=0;return function(t){return e.enter("thematicBreak"),function(e){return r=e,i(e)}(t)};function i(i){return i===r?(e.enter("thematicBreakSequence"),a(i)):o>=3&&(null===i||bl(i))?(e.exit("thematicBreak"),t(i)):n(i)}function a(t){return t===r?(e.consume(t),o++,a):(e.exit("thematicBreakSequence"),wl(t)?_l(e,i,"whitespace")(t):i(t))}}};const vs={continuation:{tokenize:function(e,t,n){const r=this;return r.containerState._closeFlow=void 0,e.check(zl,(function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,_l(e,t,"listItemIndent",r.containerState.size+1)(n)}),(function(n){if(r.containerState.furtherBlankLines||!wl(n))return r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,o(n);return r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(ks,t,o)(n)}));function o(o){return r.containerState._closeFlow=!0,r.interrupt=void 0,_l(e,e.attempt(vs,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){const r=this,o=r.events[r.events.length-1];let i=o&&"linePrefix"===o[1].type?o[2].sliceSerialize(o[1],!0).length:0,a=0;return function(t){const o=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===o?!r.containerState.marker||t===r.containerState.marker:gl(t)){if(r.containerState.type||(r.containerState.type=o,e.enter(o,{_container:!0})),"listUnordered"===o)return e.enter("listItemPrefix"),42===t||45===t?e.check(ys,n,s)(t):s(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),l(t)}return n(t)};function l(t){return gl(t)&&++a<10?(e.consume(t),l):(!r.interrupt||a<2)&&(r.containerState.marker?t===r.containerState.marker:41===t||46===t)?(e.exit("listItemValue"),s(t)):n(t)}function s(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(zl,r.interrupt?n:u,e.attempt(bs,f,c))}function u(e){return r.containerState.initialBlankLine=!0,i++,f(e)}function c(t){return wl(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),f):n(t)}function f(n){return r.containerState.size=i+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},bs={partial:!0,tokenize:function(e,t,n){const r=this;return _l(e,(function(e){const o=r.events[r.events.length-1];return!wl(e)&&o&&"listItemPrefixWhitespace"===o[1].type?t(e):n(e)}),"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},ks={partial:!0,tokenize:function(e,t,n){const r=this;return _l(e,(function(e){const o=r.events[r.events.length-1];return o&&"listItemIndent"===o[1].type&&o[2].sliceSerialize(o[1],!0).length===r.containerState.size?t(e):n(e)}),"listItemIndent",r.containerState.size+1)}};const ws={name:"setextUnderline",resolveTo:function(e,t){let n,r,o,i=e.length;for(;i--;)if("enter"===e[i][0]){if("content"===e[i][1].type){n=i;break}"paragraph"===e[i][1].type&&(r=i)}else"content"===e[i][1].type&&e.splice(i,1),o||"definition"!==e[i][1].type||(o=i);const a={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};e[r][1].type="setextHeadingText",o?(e.splice(r,0,["enter",a,t]),e.splice(o+1,0,["exit",e[n][1],t]),e[n][1].end={...e[o][1].end}):e[n][1]=a;return e.push(["exit",a,t]),e},tokenize:function(e,t,n){const r=this;let o;return function(t){let a,l=r.events.length;for(;l--;)if("lineEnding"!==r.events[l][1].type&&"linePrefix"!==r.events[l][1].type&&"content"!==r.events[l][1].type){a="paragraph"===r.events[l][1].type;break}if(!r.parser.lazy[r.now().line]&&(r.interrupt||a))return e.enter("setextHeadingLine"),o=t,function(t){return e.enter("setextHeadingLineSequence"),i(t)}(t);return n(t)};function i(t){return t===o?(e.consume(t),i):(e.exit("setextHeadingLineSequence"),wl(t)?_l(e,a,"lineSuffix")(t):a(t))}function a(r){return null===r||bl(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}};const xs={tokenize:function(e){const t=this,n=e.attempt(zl,(function(r){if(null===r)return void e.consume(r);return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n}),e.attempt(this.parser.constructs.flowInitial,r,_l(e,e.attempt(this.parser.constructs.flow,r,e.attempt(Kl,r)),"linePrefix")));return n;function r(r){if(null!==r)return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n;e.consume(r)}}};const Ss={resolveAll:Ts()},Es=_s("string"),Cs=_s("text");function _s(e){return{resolveAll:Ts("text"===e?Ps:void 0),tokenize:function(t){const n=this,r=this.parser.constructs[e],o=t.attempt(r,i,a);return i;function i(e){return s(e)?o(e):a(e)}function a(e){if(null!==e)return t.enter("data"),t.consume(e),l;t.consume(e)}function l(e){return s(e)?(t.exit("data"),o(e)):(t.consume(e),l)}function s(e){if(null===e)return!0;const t=r[e];let o=-1;if(t)for(;++o<t.length;){const e=t[o];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function Ts(e){return function(t,n){let r,o=-1;for(;++o<=t.length;)void 0===r?t[o]&&"data"===t[o][1].type&&(r=o,o++):t[o]&&"data"===t[o][1].type||(o!==r+2&&(t[r][1].end=t[o-1][1].end,t.splice(r+2,o-r-2),o=r+2),r=void 0);return e?e(t,n):t}}function Ps(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){const r=e[n-1][1],o=t.sliceStream(r);let i,a=o.length,l=-1,s=0;for(;a--;){const e=o[a];if("string"==typeof e){for(l=e.length;32===e.charCodeAt(l-1);)s++,l--;if(l)break;l=-1}else if(-2===e)i=!0,s++;else if(-1!==e){a++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(s=0),s){const o={type:n===e.length||i||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:a?l:r.start._bufferIndex+l,_index:r.start._index+a,line:r.end.line,column:r.end.column-s,offset:r.end.offset-s},end:{...r.end}};r.end={...o.start},r.start.offset===r.end.offset?Object.assign(r,o):(e.splice(n,0,["enter",o,t],["exit",o,t]),n+=2)}n++}return e}const Ns={42:vs,43:vs,45:vs,48:vs,49:vs,50:vs,51:vs,52:vs,53:vs,54:vs,55:vs,56:vs,57:vs,62:Dl},Os={91:es},Ls={[-2]:Bl,[-1]:Bl,32:Bl},Rs={35:rs,42:ys,45:[ws,ys],60:as,61:ws,95:ys,96:Ul,126:Ul},As={38:Il,92:Ml},js={[-5]:gs,[-4]:gs,[-3]:gs,33:hs,38:Il,42:Rl,60:[jl,us],91:ms,92:[ns,Ml],93:cs,95:Rl,96:$l},zs={null:[Rl,Ss]},Ds=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:{null:[42,95]},contentInitial:Os,disable:{null:[]},document:Ns,flow:Rs,flowInitial:Ls,insideSpan:zs,string:As,text:js},Symbol.toStringTag,{value:"Module"}));function Ms(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0};const o={},i=[];let a=[],l=[];const s={attempt:g((function(e,t){y(e,t.from)})),check:g(m),consume:function(e){bl(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,v()):-1!==e&&(r.column++,r.offset++);r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===a[r._index].length&&(r._bufferIndex=-1,r._index++));u.previous=e},enter:function(e,t){const n=t||{};return n.type=e,n.start=d(),u.events.push(["enter",n,u]),l.push(n),n},exit:function(e){const t=l.pop();return t.end=d(),u.events.push(["exit",t,u]),t},interrupt:g(m,{interrupt:!0})},u={code:null,containerState:{},defineSkip:function(e){o[e.line]=e.column,v()},events:[],now:d,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n=-1;const r=[];let o;for(;++n<e.length;){const i=e[n];let a;if("string"==typeof i)a=i;else switch(i){case-5:a="\r";break;case-4:a="\n";break;case-3:a="\r\n";break;case-2:a=t?" ":"\t";break;case-1:if(!t&&o)continue;a=" ";break;default:a=String.fromCharCode(i)}o=-2===i,r.push(a)}return r.join("")}(f(e),t)},sliceStream:f,write:function(e){if(a=il(a,e),p(),null!==a[a.length-1])return[];return y(t,0),u.events=Ll(i,u.events,u),u.events}};let c=t.tokenize.call(u,s);return t.resolveAll&&i.push(t),u;function f(e){return function(e,t){const n=t.start._index,r=t.start._bufferIndex,o=t.end._index,i=t.end._bufferIndex;let a;if(n===o)a=[e[n].slice(r,i)];else{if(a=e.slice(n,o),r>-1){const e=a[0];"string"==typeof e?a[0]=e.slice(r):a.shift()}i>0&&a.push(e[o].slice(0,i))}return a}(a,e)}function d(){const{_bufferIndex:e,_index:t,line:n,column:o,offset:i}=r;return{_bufferIndex:e,_index:t,line:n,column:o,offset:i}}function p(){let e;for(;r._index<a.length;){const t=a[r._index];if("string"==typeof t)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<t.length;)h(t.charCodeAt(r._bufferIndex));else h(t)}}function h(e){c=c(e)}function m(e,t){t.restore()}function g(e,t){return function(n,o,i){let a,c,f,p;return Array.isArray(n)?h(n):"tokenize"in n?h([n]):function(e){return t;function t(t){const n=null!==t&&e[t],r=null!==t&&e.null;return h([...Array.isArray(n)?n:n?[n]:[],...Array.isArray(r)?r:r?[r]:[]])(t)}}(n);function h(e){return a=e,c=0,0===e.length?i:m(e[c])}function m(e){return function(n){p=function(){const e=d(),t=u.previous,n=u.currentConstruct,o=u.events.length,i=Array.from(l);return{from:o,restore:a};function a(){r=e,u.previous=t,u.currentConstruct=n,u.events.length=o,l=i,v()}}(),f=e,e.partial||(u.currentConstruct=e);if(e.name&&u.parser.constructs.disable.null.includes(e.name))return y();return e.tokenize.call(t?Object.assign(Object.create(u),t):u,s,g,y)(n)}}function g(t){return e(f,p),o}function y(e){return p.restore(),++c<a.length?m(a[c]):i}}}function y(e,t){e.resolveAll&&!i.includes(e)&&i.push(e),e.resolve&&ol(u.events,t,u.events.length-t,e.resolve(u.events.slice(t),u)),e.resolveTo&&(u.events=e.resolveTo(u.events,u))}function v(){r.line in o&&r.column<2&&(r.column=o[r.line],r.offset+=o[r.line]-1)}}const Is=/[\0\t\n\r]/g;const Fs=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function Us(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){const e=n.charCodeAt(1),t=120===e||88===e;return cl(n.slice(t?2:1),t?16:10)}return rl(n)||e}const Bs={}.hasOwnProperty;function Hs(e,t,n){return"string"!=typeof t&&(n=t,t=void 0),function(e){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:i(te),autolinkProtocol:C,autolinkEmail:C,atxHeading:i(J),blockQuote:i(q),characterEscape:C,characterReference:C,codeFenced:i(Q),codeFencedFenceInfo:a,codeFencedFenceMeta:a,codeIndented:i(Q,a),codeText:i(K,a),codeTextData:C,data:C,codeFlowValue:C,definition:i(Y),definitionDestinationString:a,definitionLabelString:a,definitionTitleString:a,emphasis:i(X),hardBreakEscape:i(G),hardBreakTrailing:i(G),htmlFlow:i(Z,a),htmlFlowData:C,htmlText:i(Z,a),htmlTextData:C,image:i(ee),label:a,link:i(te),listItem:i(re),listItemValue:d,listOrdered:i(ne,f),listUnordered:i(ne),paragraph:i(oe),reference:F,referenceString:a,resourceDestinationString:a,resourceTitleString:a,setextHeading:i(J),strong:i(ie),thematicBreak:i(le)},exit:{atxHeading:s(),atxHeadingSequence:w,autolink:s(),autolinkEmail:W,autolinkProtocol:V,blockQuote:s(),characterEscapeValue:_,characterReferenceMarkerHexadecimal:B,characterReferenceMarkerNumeric:B,characterReferenceValue:H,characterReference:$,codeFenced:s(g),codeFencedFence:m,codeFencedFenceInfo:p,codeFencedFenceMeta:h,codeFlowValue:_,codeIndented:s(y),codeText:s(L),codeTextData:_,data:_,definition:s(),definitionDestinationString:k,definitionLabelString:v,definitionTitleString:b,emphasis:s(),hardBreakEscape:s(P),hardBreakTrailing:s(P),htmlFlow:s(N),htmlFlowData:_,htmlText:s(O),htmlTextData:_,image:s(A),label:z,labelText:j,lineEnding:T,link:s(R),listItem:s(),listOrdered:s(),listUnordered:s(),paragraph:s(),referenceString:U,resourceDestinationString:D,resourceTitleString:M,resource:I,setextHeading:s(E),setextHeadingLineSequence:S,setextHeadingText:x,strong:s(),thematicBreak:s()}};Vs(t,(e||{}).mdastExtensions||[]);const n={};return r;function r(e){let r={type:"root",children:[]};const i={stack:[r],tokenStack:[],config:t,enter:l,exit:u,buffer:a,resume:c,data:n},s=[];let f=-1;for(;++f<e.length;)if("listOrdered"===e[f][1].type||"listUnordered"===e[f][1].type)if("enter"===e[f][0])s.push(f);else{f=o(e,s.pop(),f)}for(f=-1;++f<e.length;){const n=t[e[f][0]];Bs.call(n,e[f][1].type)&&n[e[f][1].type].call(Object.assign({sliceSerialize:e[f][2].sliceSerialize},i),e[f][1])}if(i.tokenStack.length>0){const e=i.tokenStack[i.tokenStack.length-1];(e[1]||qs).call(i,void 0,e[0])}for(r.position={start:$s(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:$s(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},f=-1;++f<t.transforms.length;)r=t.transforms[f](r)||r;return r}function o(e,t,n){let r,o,i,a,l=t-1,s=-1,u=!1;for(;++l<=n;){const t=e[l];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?s++:s--,a=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||a||s||i||(i=l),a=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:a=void 0}if(!s&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===s&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let a=l;for(o=void 0;a--;){const t=e[a];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;o&&(e[o][1].type="lineEndingBlank",u=!0),t[1].type="lineEnding",o=a}else if("linePrefix"!==t[1].type&&"blockQuotePrefix"!==t[1].type&&"blockQuotePrefixWhitespace"!==t[1].type&&"blockQuoteMarker"!==t[1].type&&"listItemIndent"!==t[1].type)break}i&&(!o||i<o)&&(r._spread=!0),r.end=Object.assign({},o?e[o][1].start:t[1].end),e.splice(o||l,0,["exit",r,t[2]]),l++,n++}if("listItemPrefix"===t[1].type){const o={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=o,e.splice(l,0,["enter",o,t[2]]),l++,n++,i=void 0,a=!0}}}return e[t][1]._spread=u,n}function i(e,t){return n;function n(n){l.call(this,e(n),n),t&&t.call(this,n)}}function a(){this.stack.push({type:"fragment",children:[]})}function l(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:$s(t.start),end:void 0}}function s(e){return t;function t(t){e&&e.call(this,t),u.call(this,t)}}function u(e,t){const n=this.stack.pop(),r=this.tokenStack.pop();if(!r)throw new Error("Cannot close `"+e.type+"` ("+Oa({start:e.start,end:e.end})+"): it’s not open");if(r[0].type!==e.type)if(t)t.call(this,e,r[0]);else{(r[1]||qs).call(this,e,r[0])}n.position.end=$s(e.end)}function c(){return Za(this.stack.pop())}function f(){this.data.expectingFirstListItemValue=!0}function d(e){if(this.data.expectingFirstListItemValue){this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0}}function p(){const e=this.resume();this.stack[this.stack.length-1].lang=e}function h(){const e=this.resume();this.stack[this.stack.length-1].meta=e}function m(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function g(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function y(){const e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}function v(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=fl(this.sliceSerialize(e)).toLowerCase()}function b(){const e=this.resume();this.stack[this.stack.length-1].title=e}function k(){const e=this.resume();this.stack[this.stack.length-1].url=e}function w(e){const t=this.stack[this.stack.length-1];if(!t.depth){const n=this.sliceSerialize(e).length;t.depth=n}}function x(){this.data.setextHeadingSlurpLineEnding=!0}function S(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2}function E(){this.data.setextHeadingSlurpLineEnding=void 0}function C(e){const t=this.stack[this.stack.length-1].children;let n=t[t.length-1];n&&"text"===n.type||(n=ae(),n.position={start:$s(e.start),end:void 0},t.push(n)),this.stack.push(n)}function _(e){const t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=$s(e.end)}function T(e){const n=this.stack[this.stack.length-1];if(this.data.atHardBreak){return n.children[n.children.length-1].position.end=$s(e.end),void(this.data.atHardBreak=void 0)}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(C.call(this,e),_.call(this,e))}function P(){this.data.atHardBreak=!0}function N(){const e=this.resume();this.stack[this.stack.length-1].value=e}function O(){const e=this.resume();this.stack[this.stack.length-1].value=e}function L(){const e=this.resume();this.stack[this.stack.length-1].value=e}function R(){const e=this.stack[this.stack.length-1];if(this.data.inReference){const t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}function A(){const e=this.stack[this.stack.length-1];if(this.data.inReference){const t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}function j(e){const t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=function(e){return e.replace(Fs,Us)}(t),n.identifier=fl(t).toLowerCase()}function z(){const e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];if(this.data.inReference=!0,"link"===n.type){const t=e.children;n.children=t}else n.alt=t}function D(){const e=this.resume();this.stack[this.stack.length-1].url=e}function M(){const e=this.resume();this.stack[this.stack.length-1].title=e}function I(){this.data.inReference=void 0}function F(){this.data.referenceType="collapsed"}function U(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=fl(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"}function B(e){this.data.characterReferenceType=e.type}function H(e){const t=this.sliceSerialize(e),n=this.data.characterReferenceType;let r;if(n)r=cl(t,"characterReferenceMarkerNumeric"===n?10:16),this.data.characterReferenceType=void 0;else{r=rl(t)}this.stack[this.stack.length-1].value+=r}function $(e){this.stack.pop().position.end=$s(e.end)}function V(e){_.call(this,e);this.stack[this.stack.length-1].url=this.sliceSerialize(e)}function W(e){_.call(this,e);this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)}function q(){return{type:"blockquote",children:[]}}function Q(){return{type:"code",lang:null,meta:null,value:""}}function K(){return{type:"inlineCode",value:""}}function Y(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function X(){return{type:"emphasis",children:[]}}function J(){return{type:"heading",depth:0,children:[]}}function G(){return{type:"break"}}function Z(){return{type:"html",value:""}}function ee(){return{type:"image",title:null,url:"",alt:null}}function te(){return{type:"link",title:null,url:"",children:[]}}function ne(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}function re(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}function oe(){return{type:"paragraph",children:[]}}function ie(){return{type:"strong",children:[]}}function ae(){return{type:"text",value:""}}function le(){return{type:"thematicBreak"}}}(n)(function(e){for(;!ql(e););return e}(function(e){const t={constructs:ll([Ds,...(e||{}).extensions||[]]),content:n(Tl),defined:[],document:n(Pl),flow:n(xs),lazy:{},string:n(Es),text:n(Cs)};return t;function n(e){return function(n){return Ms(t,e,n)}}}(n).document().write(function(){let e,t=1,n="",r=!0;return function(o,i,a){const l=[];let s,u,c,f,d;for(o=n+("string"==typeof o?o.toString():new TextDecoder(i||void 0).decode(o)),c=0,n="",r&&(65279===o.charCodeAt(0)&&c++,r=void 0);c<o.length;){if(Is.lastIndex=c,s=Is.exec(o),f=s&&void 0!==s.index?s.index:o.length,d=o.charCodeAt(f),!s){n=o.slice(c);break}if(10===d&&c===f&&e)l.push(-3),e=void 0;else switch(e&&(l.push(-5),e=void 0),c<f&&(l.push(o.slice(c,f)),t+=f-c),d){case 0:l.push(65533),t++;break;case 9:for(u=4*Math.ceil(t/4),l.push(-2);t++<u;)l.push(-1);break;case 10:l.push(-4),t=1;break;default:e=!0,t=1}c=f+1}return a&&(e&&l.push(-5),n&&l.push(n),l.push(null)),l}}()(e,t,!0))))}function $s(e){return{line:e.line,column:e.column,offset:e.offset}}function Vs(e,t){let n=-1;for(;++n<t.length;){const r=t[n];Array.isArray(r)?Vs(e,r):Ws(e,r)}}function Ws(e,t){let n;for(n in t)if(Bs.call(t,n))switch(n){case"canContainEols":{const r=t[n];r&&e[n].push(...r);break}case"transforms":{const r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{const r=t[n];r&&Object.assign(e[n],r);break}}}function qs(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+Oa({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+Oa({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+Oa({start:t.start,end:t.end})+") is still open")}function Qs(e){const t=this;t.parser=function(n){return Hs(n,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}function Ks(e,t){const n=t.referenceType;let r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];const o=e.all(t),i=o[0];i&&"text"===i.type?i.value="["+i.value:o.unshift({type:"text",value:"["});const a=o[o.length-1];return a&&"text"===a.type?a.value+=r:o.push({type:"text",value:r}),o}function Ys(e){const t=e.spread;return null==t?e.children.length>1:t}function Xs(e){const t=String(e),n=/\r?\n|\r/g;let r=n.exec(t),o=0;const i=[];for(;r;)i.push(Js(t.slice(o,r.index),o>0,!0),r[0]),o=r.index+r[0].length,r=n.exec(t);return i.push(Js(t.slice(o),o>0,!1)),i.join("")}function Js(e,t,n){let r=0,o=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(o-1);for(;9===t||32===t;)o--,t=e.codePointAt(o-1)}return o>r?e.slice(r,o):""}const Gs={blockquote:function(e,t){const n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){const n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){const n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let o={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(o.data={meta:t.meta}),e.patch(t,o),o=e.applyData(t,o),o={type:"element",tagName:"pre",properties:{},children:[o]},e.patch(t,o),o},delete:function(e,t){const n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){const n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){const n="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",r=String(t.identifier).toUpperCase(),o=Cl(r.toLowerCase()),i=e.footnoteOrder.indexOf(r);let a,l=e.footnoteCounts.get(r);void 0===l?(l=0,e.footnoteOrder.push(r),a=e.footnoteOrder.length):a=i+1,l+=1,e.footnoteCounts.set(r,l);const s={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+o,id:n+"fnref-"+o+(l>1?"-"+l:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(a)}]};e.patch(t,s);const u={type:"element",tagName:"sup",properties:{},children:[s]};return e.patch(t,u),e.applyData(t,u)},heading:function(e,t){const n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){const n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return Ks(e,t);const o={src:Cl(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(o.title=r.title);const i={type:"element",tagName:"img",properties:o,children:[]};return e.patch(t,i),e.applyData(t,i)},image:function(e,t){const n={src:Cl(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);const r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){const n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);const r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return Ks(e,t);const o={href:Cl(r.url||"")};null!==r.title&&void 0!==r.title&&(o.title=r.title);const i={type:"element",tagName:"a",properties:o,children:e.all(t)};return e.patch(t,i),e.applyData(t,i)},link:function(e,t){const n={href:Cl(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);const r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){const r=e.all(t),o=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;const n=e.children;let r=-1;for(;!t&&++r<n.length;)t=Ys(n[r])}return t}(n):Ys(t),i={},a=[];if("boolean"==typeof t.checked){const e=r[0];let n;e&&"element"===e.type&&"p"===e.tagName?n=e:(n={type:"element",tagName:"p",properties:{},children:[]},r.unshift(n)),n.children.length>0&&n.children.unshift({type:"text",value:" "}),n.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),i.className=["task-list-item"]}let l=-1;for(;++l<r.length;){const e=r[l];(o||0!==l||"element"!==e.type||"p"!==e.tagName)&&a.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||o?a.push(e):a.push(...e.children)}const s=r[r.length-1];s&&(o||"element"!==s.type||"p"!==s.tagName)&&a.push({type:"text",value:"\n"});const u={type:"element",tagName:"li",properties:i,children:a};return e.patch(t,u),e.applyData(t,u)},list:function(e,t){const n={},r=e.all(t);let o=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++o<r.length;){const e=r[o];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}const i={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,i),e.applyData(t,i)},paragraph:function(e,t){const n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){const n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){const n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){const n=e.all(t),r=n.shift(),o=[];if(r){const n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),o.push(n)}if(n.length>0){const r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},i=Pa(t.children[1]),a=Ta(t.children[t.children.length-1]);i&&a&&(r.position={start:i,end:a}),o.push(r)}const i={type:"element",tagName:"table",properties:{},children:e.wrap(o,!0)};return e.patch(t,i),e.applyData(t,i)},tableCell:function(e,t){const n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){const r=n?n.children:void 0,o=0===(r?r.indexOf(t):1)?"th":"td",i=n&&"table"===n.type?n.align:void 0,a=i?i.length:t.children.length;let l=-1;const s=[];for(;++l<a;){const n=t.children[l],r={},a=i?i[l]:void 0;a&&(r.align=a);let u={type:"element",tagName:o,properties:r,children:[]};n&&(u.children=e.all(n),e.patch(n,u),u=e.applyData(n,u)),s.push(u)}const u={type:"element",tagName:"tr",properties:{},children:e.wrap(s,!0)};return e.patch(t,u),e.applyData(t,u)},text:function(e,t){const n={type:"text",value:Xs(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){const n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:Zs,yaml:Zs,definition:Zs,footnoteDefinition:Zs};function Zs(){}const eu="object"==typeof self?self:globalThis,tu=e=>((e,t)=>{const n=(t,n)=>(e.set(n,t),t),r=o=>{if(e.has(o))return e.get(o);const[i,a]=t[o];switch(i){case 0:case-1:return n(a,o);case 1:{const e=n([],o);for(const t of a)e.push(r(t));return e}case 2:{const e=n({},o);for(const[t,n]of a)e[r(t)]=r(n);return e}case 3:return n(new Date(a),o);case 4:{const{source:e,flags:t}=a;return n(new RegExp(e,t),o)}case 5:{const e=n(new Map,o);for(const[t,n]of a)e.set(r(t),r(n));return e}case 6:{const e=n(new Set,o);for(const t of a)e.add(r(t));return e}case 7:{const{name:e,message:t}=a;return n(new eu[e](t),o)}case 8:return n(BigInt(a),o);case"BigInt":return n(Object(BigInt(a)),o);case"ArrayBuffer":return n(new Uint8Array(a).buffer,a);case"DataView":{const{buffer:e}=new Uint8Array(a);return n(new DataView(e),a)}}return n(new eu[i](a),o)};return r})(new Map,e)(0),nu="",{toString:ru}={},{keys:ou}=Object,iu=e=>{const t=typeof e;if("object"!==t||!e)return[0,t];const n=ru.call(e).slice(8,-1);switch(n){case"Array":return[1,nu];case"Object":return[2,nu];case"Date":return[3,nu];case"RegExp":return[4,nu];case"Map":return[5,nu];case"Set":return[6,nu];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},au=([e,t])=>0===e&&("function"===t||"symbol"===t),lu=(e,{json:t,lossy:n}={})=>{const r=[];return((e,t,n,r)=>{const o=(e,t)=>{const o=r.push(e)-1;return n.set(t,o),o},i=r=>{if(n.has(r))return n.get(r);let[a,l]=iu(r);switch(a){case 0:{let t=r;switch(l){case"bigint":a=8,t=r.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+l);t=null;break;case"undefined":return o([-1],r)}return o([a,t],r)}case 1:{if(l){let e=r;return"DataView"===l?e=new Uint8Array(r.buffer):"ArrayBuffer"===l&&(e=new Uint8Array(r)),o([l,[...e]],r)}const e=[],t=o([a,e],r);for(const n of r)e.push(i(n));return t}case 2:{if(l)switch(l){case"BigInt":return o([l,r.toString()],r);case"Boolean":case"Number":case"String":return o([l,r.valueOf()],r)}if(t&&"toJSON"in r)return i(r.toJSON());const n=[],s=o([a,n],r);for(const t of ou(r))!e&&au(iu(r[t]))||n.push([i(t),i(r[t])]);return s}case 3:return o([a,r.toISOString()],r);case 4:{const{source:e,flags:t}=r;return o([a,{source:e,flags:t}],r)}case 5:{const t=[],n=o([a,t],r);for(const[o,a]of r)(e||!au(iu(o))&&!au(iu(a)))&&t.push([i(o),i(a)]);return n}case 6:{const t=[],n=o([a,t],r);for(const o of r)!e&&au(iu(o))||t.push(i(o));return n}}const{message:s}=r;return o([a,{name:l,message:s}],r)};return i})(!(t||n),!!t,new Map,r)(e),r},su="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?tu(lu(e,t)):structuredClone(e):(e,t)=>tu(lu(e,t));function uu(e,t){const n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function cu(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}const fu=function(e){if(null==e)return pu;if("function"==typeof e)return du(e);if("object"==typeof e)return Array.isArray(e)?function(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=fu(e[n]);return du(r);function r(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}}(e):function(e){const t=e;return du(n);function n(n){const r=n;let o;for(o in e)if(r[o]!==t[o])return!1;return!0}}(e);if("string"==typeof e)return function(e){return du(t);function t(t){return t&&t.type===e}}(e);throw new Error("Expected function, string, or object as test")};function du(e){return function(t,n,r){return Boolean(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function pu(){return!0}const hu=[],mu=!0,gu=!1;function yu(e,t,n,r){let o;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):o=t;const i=fu(o),a=r?-1:1;!function e(o,l,s){const u=o&&"object"==typeof o?o:{};if("string"==typeof u.type){const e="string"==typeof u.tagName?u.tagName:"string"==typeof u.name?u.name:void 0;Object.defineProperty(c,"name",{value:"node ("+o.type+(e?"<"+e+">":"")+")"})}return c;function c(){let u,c,f,d=hu;if((!t||i(o,l,s[s.length-1]||void 0))&&(d=function(e){if(Array.isArray(e))return e;if("number"==typeof e)return[mu,e];return null==e?hu:[e]}(n(o,s)),d[0]===gu))return d;if("children"in o&&o.children){const t=o;if(t.children&&"skip"!==d[0])for(c=(r?t.children.length:-1)+a,f=s.concat(t);c>-1&&c<t.children.length;){const n=t.children[c];if(u=e(n,c,f)(),u[0]===gu)return u;c="number"==typeof u[1]?u[1]:c+a}}return d}}(e,void 0,[])()}function vu(e,t,n,r){let o,i,a;"function"==typeof t?(i=void 0,a=t,o=n):(i=t,a=n,o=r),yu(e,i,(function(e,t){const n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return a(e,r,n)}),o)}const bu={}.hasOwnProperty,ku={};function wu(e,t){e.position&&(t.position=function(e){const t=Pa(e),n=Ta(e);if(t&&n)return{start:t,end:n}}(e))}function xu(e,t){let n=t;if(e&&e.data){const t=e.data.hName,r=e.data.hChildren,o=e.data.hProperties;if("string"==typeof t)if("element"===n.type)n.tagName=t;else{n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}}"element"===n.type&&o&&Object.assign(n.properties,su(o)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function Su(e,t){const n=t.data||{},r=!("value"in t)||bu.call(n,"hProperties")||bu.call(n,"hChildren")?{type:"element",tagName:"div",properties:{},children:e.all(t)}:{type:"text",value:t.value};return e.patch(t,r),e.applyData(t,r)}function Eu(e,t){const n=[];let r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function Cu(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function _u(e,t){const n=function(e,t){const n=t||ku,r=new Map,o=new Map,i=new Map,a={...Gs,...n.handlers},l={all:function(e){const t=[];if("children"in e){const n=e.children;let r=-1;for(;++r<n.length;){const o=l.one(n[r],e);if(o){if(r&&"break"===n[r-1].type&&(Array.isArray(o)||"text"!==o.type||(o.value=Cu(o.value)),!Array.isArray(o)&&"element"===o.type)){const e=o.children[0];e&&"text"===e.type&&(e.value=Cu(e.value))}Array.isArray(o)?t.push(...o):t.push(o)}}}return t},applyData:xu,definitionById:r,footnoteById:o,footnoteCounts:i,footnoteOrder:[],handlers:a,one:function(e,t){const n=e.type,r=l.handlers[n];if(bu.call(l.handlers,n)&&r)return r(l,e,t);if(l.options.passThrough&&l.options.passThrough.includes(n)){if("children"in e){const{children:t,...n}=e,r=su(n);return r.children=l.all(e),r}return su(e)}return(l.options.unknownHandler||Su)(l,e,t)},options:n,patch:wu,wrap:Eu};return vu(e,(function(e){if("definition"===e.type||"footnoteDefinition"===e.type){const t="definition"===e.type?r:o,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}})),l}(e,t),r=n.one(e,void 0),o=function(e){const t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||uu,r=e.options.footnoteBackLabel||cu,o=e.options.footnoteLabel||"Footnotes",i=e.options.footnoteLabelTagName||"h2",a=e.options.footnoteLabelProperties||{className:["sr-only"]},l=[];let s=-1;for(;++s<e.footnoteOrder.length;){const o=e.footnoteById.get(e.footnoteOrder[s]);if(!o)continue;const i=e.all(o),a=String(o.identifier).toUpperCase(),u=Cl(a.toLowerCase());let c=0;const f=[],d=e.footnoteCounts.get(a);for(;void 0!==d&&++c<=d;){f.length>0&&f.push({type:"text",value:" "});let e="string"==typeof n?n:n(s,c);"string"==typeof e&&(e={type:"text",value:e}),f.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+u+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(s,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}const p=i[i.length-1];if(p&&"element"===p.type&&"p"===p.tagName){const e=p.children[p.children.length-1];e&&"text"===e.type?e.value+=" ":p.children.push({type:"text",value:" "}),p.children.push(...f)}else i.push(...f);const h={type:"element",tagName:"li",properties:{id:t+"fn-"+u},children:e.wrap(i,!0)};e.patch(o,h),l.push(h)}if(0!==l.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:i,properties:{...su(a),id:"footnote-label"},children:[{type:"text",value:o}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(l,!0)},{type:"text",value:"\n"}]}}(n),i=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return o&&i.children.push({type:"text",value:"\n"},o),i}function Tu(e,t){return e&&"run"in e?async function(n,r){const o=_u(n,{file:r,...t});await e.run(o,r)}:function(n,r){return _u(n,{file:r,...e||t})}}function Pu(e){if(e)throw e}var Nu,Ou;const Lu=e(function(){if(Ou)return Nu;Ou=1;var e=Object.prototype.hasOwnProperty,t=Object.prototype.toString,n=Object.defineProperty,r=Object.getOwnPropertyDescriptor,o=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===t.call(e)},i=function(n){if(!n||"[object Object]"!==t.call(n))return!1;var r,o=e.call(n,"constructor"),i=n.constructor&&n.constructor.prototype&&e.call(n.constructor.prototype,"isPrototypeOf");if(n.constructor&&!o&&!i)return!1;for(r in n);return void 0===r||e.call(n,r)},a=function(e,t){n&&"__proto__"===t.name?n(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},l=function(t,n){if("__proto__"===n){if(!e.call(t,n))return;if(r)return r(t,n).value}return t[n]};return Nu=function e(){var t,n,r,s,u,c,f=arguments[0],d=1,p=arguments.length,h=!1;for("boolean"==typeof f&&(h=f,f=arguments[1]||{},d=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});d<p;++d)if(null!=(t=arguments[d]))for(n in t)r=l(f,n),f!==(s=l(t,n))&&(h&&s&&(i(s)||(u=o(s)))?(u?(u=!1,c=r&&o(r)?r:[]):c=r&&i(r)?r:{},a(f,{name:n,newValue:e(h,c,s)})):void 0!==s&&a(f,{name:n,newValue:s}));return f}}());function Ru(e){if("object"!=typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)}function Au(){const e=[],t={run:function(...t){let n=-1;const r=t.pop();if("function"!=typeof r)throw new TypeError("Expected function as last argument, not "+r);!function o(i,...a){const l=e[++n];let s=-1;if(i)r(i);else{for(;++s<t.length;)null!==a[s]&&void 0!==a[s]||(a[s]=t[s]);t=a,l?function(e,t){let n;return r;function r(...t){const r=e.length>t.length;let l;r&&t.push(o);try{l=e.apply(this,t)}catch(i){if(r&&n)throw i;return o(i)}r||(l&&l.then&&"function"==typeof l.then?l.then(a,o):l instanceof Error?o(l):a(l))}function o(e,...r){n||(n=!0,t(e,...r))}function a(e){o(null,e)}}(l,o)(...a):r(null,...a)}}(null,...t)},use:function(n){if("function"!=typeof n)throw new TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}const ju={basename:function(e,t){if(void 0!==t&&"string"!=typeof t)throw new TypeError('"ext" argument must be a string');zu(e);let n,r=0,o=-1,i=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;i--;)if(47===e.codePointAt(i)){if(n){r=i+1;break}}else o<0&&(n=!0,o=i+1);return o<0?"":e.slice(r,o)}if(t===e)return"";let a=-1,l=t.length-1;for(;i--;)if(47===e.codePointAt(i)){if(n){r=i+1;break}}else a<0&&(n=!0,a=i+1),l>-1&&(e.codePointAt(i)===t.codePointAt(l--)?l<0&&(o=i):(l=-1,o=a));r===o?o=a:o<0&&(o=e.length);return e.slice(r,o)},dirname:function(e){if(zu(e),0===e.length)return".";let t,n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){zu(e);let t,n=e.length,r=-1,o=0,i=-1,a=0;for(;n--;){const l=e.codePointAt(n);if(47!==l)r<0&&(t=!0,r=n+1),46===l?i<0?i=n:1!==a&&(a=1):i>-1&&(a=-1);else if(t){o=n+1;break}}if(i<0||r<0||0===a||1===a&&i===r-1&&i===o+1)return"";return e.slice(i,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)zu(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){zu(e);const t=47===e.codePointAt(0);let n=function(e,t){let n,r,o="",i=0,a=-1,l=0,s=-1;for(;++s<=e.length;){if(s<e.length)n=e.codePointAt(s);else{if(47===n)break;n=47}if(47===n){if(a===s-1||1===l);else if(a!==s-1&&2===l){if(o.length<2||2!==i||46!==o.codePointAt(o.length-1)||46!==o.codePointAt(o.length-2))if(o.length>2){if(r=o.lastIndexOf("/"),r!==o.length-1){r<0?(o="",i=0):(o=o.slice(0,r),i=o.length-1-o.lastIndexOf("/")),a=s,l=0;continue}}else if(o.length>0){o="",i=0,a=s,l=0;continue}t&&(o=o.length>0?o+"/..":"..",i=2)}else o.length>0?o+="/"+e.slice(a+1,s):o=e.slice(a+1,s),i=s-a-1;a=s,l=0}else 46===n&&l>-1?l++:l=-1}return o}(e,!t);0!==n.length||t||(n=".");n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/");return t?"/"+n:n}(t)},sep:"/"};function zu(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const Du={cwd:function(){return"/"}};function Mu(e){return Boolean(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}function Iu(e){if("string"==typeof e)e=new URL(e);else if(!Mu(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){const e=new TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){const e=new TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}const t=e.pathname;let n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){const e=t.codePointAt(n+2);if(70===e||102===e){const e=new TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}const Fu=["history","path","basename","stem","extname","dirname"];class Uu{constructor(e){let t;t=e?Mu(e)?{path:e}:"string"==typeof e||function(e){return Boolean(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":Du.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let n,r=-1;for(;++r<Fu.length;){const e=Fu[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)Fu.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?ju.basename(this.path):void 0}set basename(e){Hu(e,"basename"),Bu(e,"basename"),this.path=ju.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?ju.dirname(this.path):void 0}set dirname(e){$u(this.basename,"dirname"),this.path=ju.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?ju.extname(this.path):void 0}set extname(e){if(Bu(e,"extname"),$u(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw new Error("`extname` must start with `.`");if(e.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=ju.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){Mu(e)&&(e=Iu(e)),Hu(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?ju.basename(this.path,this.extname):void 0}set stem(e){Hu(e,"stem"),Bu(e,"stem"),this.path=ju.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){const r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){const r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){const r=new ja(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){if(void 0===this.value)return"";if("string"==typeof this.value)return this.value;return new TextDecoder(e||void 0).decode(this.value)}}function Bu(e,t){if(e&&e.includes(ju.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+ju.sep+"`")}function Hu(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function $u(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}const Vu=function(e){const t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},Wu={}.hasOwnProperty;class qu extends Vu{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=Au()}copy(){const e=new qu;let t=-1;for(;++t<this.attachers.length;){const n=this.attachers[t];e.use(...n)}return e.data(Lu(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2===arguments.length?(Xu("data",this.frozen),this.namespace[e]=t,this):Wu.call(this.namespace,e)&&this.namespace[e]||void 0:e?(Xu("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;const e=this;for(;++this.freezeIndex<this.attachers.length;){const[t,...n]=this.attachers[this.freezeIndex];if(!1===n[0])continue;!0===n[0]&&(n[0]=void 0);const r=t.call(e,...n);"function"==typeof r&&this.transformers.use(r)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();const t=Zu(e),n=this.parser||this.Parser;return Ku("parse",n),n(String(t),t)}process(e,t){const n=this;return this.freeze(),Ku("process",this.parser||this.Parser),Yu("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,o){const i=Zu(e),a=n.parse(i);function l(e,n){e||!n?o(e):r?r(n):t(void 0,n)}n.run(a,i,(function(e,t,r){if(e||!t||!r)return l(e);const o=t,i=n.stringify(o,r);var a;"string"==typeof(a=i)||function(e){return Boolean(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(a)?r.value=i:r.result=i,l(e,r)}))}}processSync(e){let t,n=!1;return this.freeze(),Ku("processSync",this.parser||this.Parser),Yu("processSync",this.compiler||this.Compiler),this.process(e,(function(e,r){n=!0,Pu(e),t=r})),Gu("processSync","process",n),t}run(e,t,n){Ju(e),this.freeze();const r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?o(void 0,n):new Promise(o);function o(o,i){const a=Zu(t);r.run(e,a,(function(t,r,a){const l=r||e;t?i(t):o?o(l):n(void 0,l,a)}))}}runSync(e,t){let n,r=!1;return this.run(e,t,(function(e,t){Pu(e),n=t,r=!0})),Gu("runSync","run",r),n}stringify(e,t){this.freeze();const n=Zu(t),r=this.compiler||this.Compiler;return Yu("stringify",r),Ju(e),r(e,n)}use(e,...t){const n=this.attachers,r=this.namespace;if(Xu("use",this.frozen),null==e);else if("function"==typeof e)l(e,t);else{if("object"!=typeof e)throw new TypeError("Expected usable value, not `"+e+"`");Array.isArray(e)?a(e):i(e)}return this;function o(e){if("function"==typeof e)l(e,[]);else{if("object"!=typeof e)throw new TypeError("Expected usable value, not `"+e+"`");if(Array.isArray(e)){const[t,...n]=e;l(t,n)}else i(e)}}function i(e){if(!("plugins"in e)&&!("settings"in e))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");a(e.plugins),e.settings&&(r.settings=Lu(!0,r.settings,e.settings))}function a(e){let t=-1;if(null==e);else{if(!Array.isArray(e))throw new TypeError("Expected a list of plugins, not `"+e+"`");for(;++t<e.length;){o(e[t])}}}function l(e,t){let r=-1,o=-1;for(;++r<n.length;)if(n[r][0]===e){o=r;break}if(-1===o)n.push([e,...t]);else if(t.length>0){let[r,...i]=t;const a=n[o][1];Ru(a)&&Ru(r)&&(r=Lu(!0,a,r)),n[o]=[e,r,...i]}}}}const Qu=(new qu).freeze();function Ku(e,t){if("function"!=typeof t)throw new TypeError("Cannot `"+e+"` without `parser`")}function Yu(e,t){if("function"!=typeof t)throw new TypeError("Cannot `"+e+"` without `compiler`")}function Xu(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Ju(e){if(!Ru(e)||"string"!=typeof e.type)throw new TypeError("Expected node, got `"+e+"`")}function Gu(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function Zu(e){return function(e){return Boolean(e&&"object"==typeof e&&"message"in e&&"messages"in e)}(e)?e:new Uu(e)}const ec=[],tc={allowDangerousHtml:!0},nc=/^(https?|ircs?|mailto|xmpp)$/i,rc=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function oc(e){const t=function(e){const t=e.rehypePlugins||ec,n=e.remarkPlugins||ec,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...tc}:tc,o=Qu().use(Qs).use(n).use(Tu,r).use(t);return o}(e),n=function(e){const t=e.children||"",n=new Uu;"string"==typeof t&&(n.value=t);return n}(e);return function(e,t){const n=t.allowedElements,r=t.allowElement,o=t.components,i=t.disallowedElements,a=t.skipHtml,s=t.unwrapDisallowed,u=t.urlTransform||ic;for(const l of rc)Object.hasOwn(t,l.from)&&(l.from,l.to&&l.to,l.id);return vu(e,c),Ba(e,{Fragment:l.Fragment,components:o,ignoreInvalidStyle:!0,jsx:l.jsx,jsxs:l.jsxs,passKeys:!0,passNode:!0});function c(e,t,o){if("raw"===e.type&&o&&"number"==typeof t)return a?o.children.splice(t,1):o.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in Ja)if(Object.hasOwn(Ja,t)&&Object.hasOwn(e.properties,t)){const n=e.properties[t],r=Ja[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=u(String(n||""),t,e))}}if("element"===e.type){let a=n?!n.includes(e.tagName):!!i&&i.includes(e.tagName);if(!a&&r&&"number"==typeof t&&(a=!r(e,t,o)),a&&o&&"number"==typeof t)return s&&e.children?o.children.splice(t,1,...e.children):o.children.splice(t,1),t}}}(t.runSync(t.parse(n),n),e)}function ic(e){const t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),o=e.indexOf("/");return-1===t||-1!==o&&t>o||-1!==n&&t>n||-1!==r&&t>r||nc.test(e.slice(0,t))?e:""}function ac(e,t){const n=String(e);if("string"!=typeof t)throw new TypeError("Expected character");let r=0,o=n.indexOf(t);for(;-1!==o;)r++,o=n.indexOf(t,o+t.length);return r}function lc(e,t,n){const r=fu((n||{}).ignore||[]),o=function(e){const t=[];if(!Array.isArray(e))throw new TypeError("Expected find and replace tuple or list of tuples");const n=!e[0]||Array.isArray(e[0])?e:[e];let r=-1;for(;++r<n.length;){const e=n[r];t.push([sc(e[0]),uc(e[1])])}return t}(t);let i=-1;for(;++i<o.length;)yu(e,"text",a);function a(e,t){let n,a=-1;for(;++a<t.length;){const e=t[a],o=n?n.children:void 0;if(r(e,o?o.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){const n=t[t.length-1],r=o[i][0],a=o[i][1];let l=0;const s=n.children.indexOf(e);let u=!1,c=[];r.lastIndex=0;let f=r.exec(e.value);for(;f;){const n=f.index,o={index:f.index,input:f.input,stack:[...t,e]};let i=a(...f,o);if("string"==typeof i&&(i=i.length>0?{type:"text",value:i}:void 0),!1===i?r.lastIndex=n+1:(l!==n&&c.push({type:"text",value:e.value.slice(l,n)}),Array.isArray(i)?c.push(...i):i&&c.push(i),l=n+f[0].length,u=!0),!r.global)break;f=r.exec(e.value)}u?(l<e.value.length&&c.push({type:"text",value:e.value.slice(l)}),n.children.splice(s,1,...c)):c=[e];return s+c.length}(e,t)}}function sc(e){return"string"==typeof e?new RegExp(function(e){if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(e),"g"):e}function uc(e){return"function"==typeof e?e:function(){return e}}const cc="phrasing",fc=["autolink","link","image","label"];function dc(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function pc(e){this.config.enter.autolinkProtocol.call(this,e)}function hc(e){this.config.exit.autolinkProtocol.call(this,e)}function mc(e){this.config.exit.data.call(this,e);const t=this.stack[this.stack.length-1];t.type,t.url="http://"+this.sliceSerialize(e)}function gc(e){this.config.exit.autolinkEmail.call(this,e)}function yc(e){this.exit(e)}function vc(e){lc(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,bc],[new RegExp("(?<=^|\\s|\\p{P}|\\p{S})([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)","gu"),kc]],{ignore:["link","linkReference"]})}function bc(e,t,n,r,o){let i="";if(!wc(o))return!1;if(/^w/i.test(t)&&(n=t+n,t="",i="http://"),!function(e){const t=e.split(".");if(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))return!1;return!0}(n))return!1;const a=function(e){const t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],r=n.indexOf(")");const o=ac(e,"(");let i=ac(e,")");for(;-1!==r&&o>i;)e+=n.slice(0,r+1),n=n.slice(r+1),r=n.indexOf(")"),i++;return[e,n]}(n+r);if(!a[0])return!1;const l={type:"link",title:null,url:i+t+a[0],children:[{type:"text",value:t+a[0]}]};return a[1]?[l,{type:"text",value:a[1]}]:l}function kc(e,t,n,r){return!(!wc(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function wc(e,t){const n=e.input.charCodeAt(e.index-1);return(0===e.index||Sl(n)||xl(n))&&(!t||47!==n)}function xc(){this.buffer()}function Sc(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function Ec(){this.buffer()}function Cc(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function _c(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=fl(this.sliceSerialize(e)).toLowerCase(),n.label=t}function Tc(e){this.exit(e)}function Pc(e){const t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=fl(this.sliceSerialize(e)).toLowerCase(),n.label=t}function Nc(e){this.exit(e)}function Oc(e,t,n,r){const o=n.createTracker(r);let i=o.move("[^");const a=n.enter("footnoteReference"),l=n.enter("reference");return i+=o.move(n.safe(n.associationId(e),{after:"]",before:i})),l(),a(),i+=o.move("]"),i}function Lc(e){let t=!1;return e&&e.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,o){const i=r.createTracker(o);let a=i.move("[^");const l=r.enter("footnoteDefinition"),s=r.enter("label");a+=i.move(r.safe(r.associationId(e),{before:a,after:"]"})),s(),a+=i.move("]:"),e.children&&e.children.length>0&&(i.shift(4),a+=i.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,i.current()),t?Ac:Rc)));return l(),a},footnoteReference:Oc},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}}function Rc(e,t,n){return 0===t?e:Ac(e,t,n)}function Ac(e,t,n){return(n?"":"    ")+e}Oc.peek=function(){return"["};const jc=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function zc(e){this.enter({type:"delete",children:[]},e)}function Dc(e){this.exit(e)}function Mc(e,t,n,r){const o=n.createTracker(r),i=n.enter("strikethrough");let a=o.move("~~");return a+=n.containerPhrasing(e,{...o.current(),before:a,after:"~"}),a+=o.move("~~"),i(),a}function Ic(e){return e.length}function Fc(e){const t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:82===t||114===t?114:0}function Uc(e,t,n){return">"+(n?"":" ")+e}function Bc(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function Hc(e,t,n,r){let o=-1;for(;++o<n.unsafe.length;)if("\n"===n.unsafe[o].character&&(i=n.stack,a=n.unsafe[o],Bc(i,a.inConstruct,!0)&&!Bc(i,a.notInConstruct,!1)))return/[ \t]/.test(r.before)?"":" ";var i,a;return"\\\n"}function $c(e,t,n){return(n?"":"    ")+e}function Vc(e){const t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw new Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function Wc(e){return"&#x"+e.toString(16).toUpperCase()+";"}function qc(e,t,n){const r=Ol(e),o=Ol(t);return void 0===r?void 0===o?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===o?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===r?void 0===o?{inside:!1,outside:!1}:1===o?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===o?{inside:!1,outside:!1}:1===o?{inside:!0,outside:!1}:{inside:!1,outside:!1}}function Qc(e,t,n,r){const o=function(e){const t=e.options.emphasis||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}(n),i=n.enter("emphasis"),a=n.createTracker(r),l=a.move(o);let s=a.move(n.containerPhrasing(e,{after:o,before:l,...a.current()}));const u=s.charCodeAt(0),c=qc(r.before.charCodeAt(r.before.length-1),u,o);c.inside&&(s=Wc(u)+s.slice(1));const f=s.charCodeAt(s.length-1),d=qc(r.after.charCodeAt(0),f,o);d.inside&&(s=s.slice(0,-1)+Wc(f));const p=a.move(o);return i(),n.attentionEncodeSurroundingInfo={after:d.outside,before:c.outside},l+s+p}function Kc(e){return e.value||""}function Yc(e,t,n,r){const o=Vc(n),i='"'===o?"Quote":"Apostrophe",a=n.enter("image");let l=n.enter("label");const s=n.createTracker(r);let u=s.move("![");return u+=s.move(n.safe(e.alt,{before:u,after:"]",...s.current()})),u+=s.move("]("),l(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(l=n.enter("destinationLiteral"),u+=s.move("<"),u+=s.move(n.safe(e.url,{before:u,after:">",...s.current()})),u+=s.move(">")):(l=n.enter("destinationRaw"),u+=s.move(n.safe(e.url,{before:u,after:e.title?" ":")",...s.current()}))),l(),e.title&&(l=n.enter(`title${i}`),u+=s.move(" "+o),u+=s.move(n.safe(e.title,{before:u,after:o,...s.current()})),u+=s.move(o),l()),u+=s.move(")"),a(),u}function Xc(e,t,n,r){const o=e.referenceType,i=n.enter("imageReference");let a=n.enter("label");const l=n.createTracker(r);let s=l.move("![");const u=n.safe(e.alt,{before:s,after:"]",...l.current()});s+=l.move(u+"]["),a();const c=n.stack;n.stack=[],a=n.enter("reference");const f=n.safe(n.associationId(e),{before:s,after:"]",...l.current()});return a(),n.stack=c,i(),"full"!==o&&u&&u===f?"shortcut"===o?s=s.slice(0,-1):s+=l.move("]"):s+=l.move(f+"]"),s}function Jc(e,t,n){let r=e.value||"",o="`",i=-1;for(;new RegExp("(^|[^`])"+o+"([^`]|$)").test(r);)o+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++i<n.unsafe.length;){const e=n.unsafe[i],t=n.compilePattern(e);let o;if(e.atBreak)for(;o=t.exec(r);){let e=o.index;10===r.charCodeAt(e)&&13===r.charCodeAt(e-1)&&e--,r=r.slice(0,e)+" "+r.slice(o.index+1)}}return o+r+o}function Gc(e,t){const n=Za(e);return Boolean(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}function Zc(e,t,n,r){const o=Vc(n),i='"'===o?"Quote":"Apostrophe",a=n.createTracker(r);let l,s;if(Gc(e,n)){const t=n.stack;n.stack=[],l=n.enter("autolink");let r=a.move("<");return r+=a.move(n.containerPhrasing(e,{before:r,after:">",...a.current()})),r+=a.move(">"),l(),n.stack=t,r}l=n.enter("link"),s=n.enter("label");let u=a.move("[");return u+=a.move(n.containerPhrasing(e,{before:u,after:"](",...a.current()})),u+=a.move("]("),s(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),u+=a.move("<"),u+=a.move(n.safe(e.url,{before:u,after:">",...a.current()})),u+=a.move(">")):(s=n.enter("destinationRaw"),u+=a.move(n.safe(e.url,{before:u,after:e.title?" ":")",...a.current()}))),s(),e.title&&(s=n.enter(`title${i}`),u+=a.move(" "+o),u+=a.move(n.safe(e.title,{before:u,after:o,...a.current()})),u+=a.move(o),s()),u+=a.move(")"),l(),u}function ef(e,t,n,r){const o=e.referenceType,i=n.enter("linkReference");let a=n.enter("label");const l=n.createTracker(r);let s=l.move("[");const u=n.containerPhrasing(e,{before:s,after:"]",...l.current()});s+=l.move(u+"]["),a();const c=n.stack;n.stack=[],a=n.enter("reference");const f=n.safe(n.associationId(e),{before:s,after:"]",...l.current()});return a(),n.stack=c,i(),"full"!==o&&u&&u===f?"shortcut"===o?s=s.slice(0,-1):s+=l.move("]"):s+=l.move(f+"]"),s}function tf(e){const t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function nf(e){const t=e.options.rule||"*";if("*"!==t&&"-"!==t&&"_"!==t)throw new Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}Mc.peek=function(){return"~"},Qc.peek=function(e,t,n){return n.options.emphasis||"*"},Kc.peek=function(){return"<"},Yc.peek=function(){return"!"},Xc.peek=function(){return"!"},Jc.peek=function(){return"`"},Zc.peek=function(e,t,n){return Gc(e,n)?"<":"["},ef.peek=function(){return"["};const rf=fu(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function of(e,t,n,r){const o=function(e){const t=e.options.strong||"*";if("*"!==t&&"_"!==t)throw new Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}(n),i=n.enter("strong"),a=n.createTracker(r),l=a.move(o+o);let s=a.move(n.containerPhrasing(e,{after:o,before:l,...a.current()}));const u=s.charCodeAt(0),c=qc(r.before.charCodeAt(r.before.length-1),u,o);c.inside&&(s=Wc(u)+s.slice(1));const f=s.charCodeAt(s.length-1),d=qc(r.after.charCodeAt(0),f,o);d.inside&&(s=s.slice(0,-1)+Wc(f));const p=a.move(o+o);return i(),n.attentionEncodeSurroundingInfo={after:d.outside,before:c.outside},l+s+p}of.peek=function(e,t,n){return n.options.strong||"*"};const af={blockquote:function(e,t,n,r){const o=n.enter("blockquote"),i=n.createTracker(r);i.move("> "),i.shift(2);const a=n.indentLines(n.containerFlow(e,i.current()),Uc);return o(),a},break:Hc,code:function(e,t,n,r){const o=function(e){const t=e.options.fence||"`";if("`"!==t&&"~"!==t)throw new Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}(n),i=e.value||"",a="`"===o?"GraveAccent":"Tilde";if(function(e,t){return Boolean(!1===t.options.fences&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}(e,n)){const e=n.enter("codeIndented"),t=n.indentLines(i,$c);return e(),t}const l=n.createTracker(r),s=o.repeat(Math.max(function(e,t){const n=String(e);let r=n.indexOf(t),o=r,i=0,a=0;if("string"!=typeof t)throw new TypeError("Expected substring");for(;-1!==r;)r===o?++i>a&&(a=i):i=1,o=r+t.length,r=n.indexOf(t,o);return a}(i,o)+1,3)),u=n.enter("codeFenced");let c=l.move(s);if(e.lang){const t=n.enter(`codeFencedLang${a}`);c+=l.move(n.safe(e.lang,{before:c,after:" ",encode:["`"],...l.current()})),t()}if(e.lang&&e.meta){const t=n.enter(`codeFencedMeta${a}`);c+=l.move(" "),c+=l.move(n.safe(e.meta,{before:c,after:"\n",encode:["`"],...l.current()})),t()}return c+=l.move("\n"),i&&(c+=l.move(i+"\n")),c+=l.move(s),u(),c},definition:function(e,t,n,r){const o=Vc(n),i='"'===o?"Quote":"Apostrophe",a=n.enter("definition");let l=n.enter("label");const s=n.createTracker(r);let u=s.move("[");return u+=s.move(n.safe(n.associationId(e),{before:u,after:"]",...s.current()})),u+=s.move("]: "),l(),!e.url||/[\0- \u007F]/.test(e.url)?(l=n.enter("destinationLiteral"),u+=s.move("<"),u+=s.move(n.safe(e.url,{before:u,after:">",...s.current()})),u+=s.move(">")):(l=n.enter("destinationRaw"),u+=s.move(n.safe(e.url,{before:u,after:e.title?" ":"\n",...s.current()}))),l(),e.title&&(l=n.enter(`title${i}`),u+=s.move(" "+o),u+=s.move(n.safe(e.title,{before:u,after:o,...s.current()})),u+=s.move(o),l()),a(),u},emphasis:Qc,hardBreak:Hc,heading:function(e,t,n,r){const o=Math.max(Math.min(6,e.depth||1),1),i=n.createTracker(r);if(function(e,t){let n=!1;return vu(e,(function(e){if("value"in e&&/\r?\n|\r/.test(e.value)||"break"===e.type)return n=!0,gu})),Boolean((!e.depth||e.depth<3)&&Za(e)&&(t.options.setext||n))}(e,n)){const t=n.enter("headingSetext"),r=n.enter("phrasing"),a=n.containerPhrasing(e,{...i.current(),before:"\n",after:"\n"});return r(),t(),a+"\n"+(1===o?"=":"-").repeat(a.length-(Math.max(a.lastIndexOf("\r"),a.lastIndexOf("\n"))+1))}const a="#".repeat(o),l=n.enter("headingAtx"),s=n.enter("phrasing");i.move(a+" ");let u=n.containerPhrasing(e,{before:"# ",after:"\n",...i.current()});return/^[\t ]/.test(u)&&(u=Wc(u.charCodeAt(0))+u.slice(1)),u=u?a+" "+u:a,n.options.closeAtx&&(u+=" "+a),s(),l(),u},html:Kc,image:Yc,imageReference:Xc,inlineCode:Jc,link:Zc,linkReference:ef,list:function(e,t,n,r){const o=n.enter("list"),i=n.bulletCurrent;let a=e.ordered?function(e){const t=e.options.bulletOrdered||".";if("."!==t&&")"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}(n):tf(n);const l=e.ordered?"."===a?")":".":function(e){const t=tf(e),n=e.options.bulletOther;if(!n)return"*"===t?"-":"*";if("*"!==n&&"+"!==n&&"-"!==n)throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw new Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}(n);let s=!(!t||!n.bulletLastUsed)&&a===n.bulletLastUsed;if(!e.ordered){const t=e.children?e.children[0]:void 0;if("*"!==a&&"-"!==a||!t||t.children&&t.children[0]||"list"!==n.stack[n.stack.length-1]||"listItem"!==n.stack[n.stack.length-2]||"list"!==n.stack[n.stack.length-3]||"listItem"!==n.stack[n.stack.length-4]||0!==n.indexStack[n.indexStack.length-1]||0!==n.indexStack[n.indexStack.length-2]||0!==n.indexStack[n.indexStack.length-3]||(s=!0),nf(n)===a&&t){let t=-1;for(;++t<e.children.length;){const n=e.children[t];if(n&&"listItem"===n.type&&n.children&&n.children[0]&&"thematicBreak"===n.children[0].type){s=!0;break}}}}s&&(a=l),n.bulletCurrent=a;const u=n.containerFlow(e,r);return n.bulletLastUsed=a,n.bulletCurrent=i,o(),u},listItem:function(e,t,n,r){const o=function(e){const t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw new Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n);let i=n.bulletCurrent||tf(n);t&&"list"===t.type&&t.ordered&&(i=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+i);let a=i.length+1;("tab"===o||"mixed"===o&&(t&&"list"===t.type&&t.spread||e.spread))&&(a=4*Math.ceil(a/4));const l=n.createTracker(r);l.move(i+" ".repeat(a-i.length)),l.shift(a);const s=n.enter("listItem"),u=n.indentLines(n.containerFlow(e,l.current()),(function(e,t,n){if(t)return(n?"":" ".repeat(a))+e;return(n?i:i+" ".repeat(a-i.length))+e}));return s(),u},paragraph:function(e,t,n,r){const o=n.enter("paragraph"),i=n.enter("phrasing"),a=n.containerPhrasing(e,r);return i(),o(),a},root:function(e,t,n,r){return(e.children.some((function(e){return rf(e)}))?n.containerPhrasing:n.containerFlow).call(n,e,r)},strong:of,text:function(e,t,n,r){return n.safe(e.value,r)},thematicBreak:function(e,t,n){const r=(nf(n)+(n.options.ruleSpaces?" ":"")).repeat(function(e){const t=e.options.ruleRepetition||3;if(t<3)throw new Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}(n));return n.options.ruleSpaces?r.slice(0,-1):r}};function lf(e){const t=e._align;this.enter({type:"table",align:t.map((function(e){return"none"===e?null:e})),children:[]},e),this.data.inTable=!0}function sf(e){this.exit(e),this.data.inTable=void 0}function uf(e){this.enter({type:"tableRow",children:[]},e)}function cf(e){this.exit(e)}function ff(e){this.enter({type:"tableCell",children:[]},e)}function df(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,pf));const n=this.stack[this.stack.length-1];n.type,n.value=t,this.exit(e)}function pf(e,t){return"|"===t?t:e}function hf(e){const t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,o=t.stringLength,i=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[\t :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=af.inlineCode(e,t,n);n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&"));return r},table:function(e,t,n,r){return l(function(e,t,n){const r=e.children;let o=-1;const i=[],a=t.enter("table");for(;++o<r.length;)i[o]=s(r[o],t,n);return a(),i}(e,n,r),e.align)},tableCell:a,tableRow:function(e,t,n,r){const o=l([s(e,n,r)]);return o.slice(0,o.indexOf("\n"))}}};function a(e,t,n,r){const o=n.enter("tableCell"),a=n.enter("phrasing"),l=n.containerPhrasing(e,{...r,before:i,after:i});return a(),o(),l}function l(e,t){return function(e,t){const n=t||{},r=(n.align||[]).concat(),o=n.stringLength||Ic,i=[],a=[],l=[],s=[];let u=0,c=-1;for(;++c<e.length;){const t=[],r=[];let i=-1;for(e[c].length>u&&(u=e[c].length);++i<e[c].length;){const a=null==(f=e[c][i])?"":String(f);if(!1!==n.alignDelimiters){const e=o(a);r[i]=e,(void 0===s[i]||e>s[i])&&(s[i]=e)}t.push(a)}a[c]=t,l[c]=r}var f;let d=-1;if("object"==typeof r&&"length"in r)for(;++d<u;)i[d]=Fc(r[d]);else{const e=Fc(r);for(;++d<u;)i[d]=e}d=-1;const p=[],h=[];for(;++d<u;){const e=i[d];let t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let o=!1===n.alignDelimiters?1:Math.max(1,s[d]-t.length-r.length);const a=t+"-".repeat(o)+r;!1!==n.alignDelimiters&&(o=t.length+o+r.length,o>s[d]&&(s[d]=o),h[d]=o),p[d]=a}a.splice(1,0,p),l.splice(1,0,h),c=-1;const m=[];for(;++c<a.length;){const e=a[c],t=l[c];d=-1;const r=[];for(;++d<u;){const o=e[d]||"";let a="",l="";if(!1!==n.alignDelimiters){const e=s[d]-(t[d]||0),n=i[d];114===n?a=" ".repeat(e):99===n?e%2?(a=" ".repeat(e/2+.5),l=" ".repeat(e/2-.5)):(a=" ".repeat(e/2),l=a):l=" ".repeat(e)}!1===n.delimiterStart||d||r.push("|"),!1===n.padding||!1===n.alignDelimiters&&""===o||!1===n.delimiterStart&&!d||r.push(" "),!1!==n.alignDelimiters&&r.push(a),r.push(o),!1!==n.alignDelimiters&&r.push(l),!1!==n.padding&&r.push(" "),!1===n.delimiterEnd&&d===u-1||r.push("|")}m.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:o})}function s(e,t,n){const r=e.children;let o=-1;const i=[],l=t.enter("tableRow");for(;++o<r.length;)i[o]=a(r[o],0,t,n);return l(),i}}function mf(e){const t=this.stack[this.stack.length-2];t.type,t.checked="taskListCheckValueChecked"===e.type}function gf(e){const t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){const e=this.stack[this.stack.length-1];e.type;const n=e.children[0];if(n&&"text"===n.type){const r=t.children;let o,i=-1;for(;++i<r.length;){const e=r[i];if("paragraph"===e.type){o=e;break}}o===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function yf(e,t,n,r){const o=e.children[0],i="boolean"==typeof e.checked&&o&&"paragraph"===o.type,a="["+(e.checked?"x":" ")+"] ",l=n.createTracker(r);i&&l.move(a);let s=af.listItem(e,t,n,{...r,...l.current()});return i&&(s=s.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,(function(e){return e+a}))),s}const vf={tokenize:function(e,t,n){let r=0;return function t(i){if((87===i||119===i)&&r<3)return r++,e.consume(i),t;if(46===i&&3===r)return e.consume(i),o;return n(i)};function o(e){return null===e?n(e):t(e)}},partial:!0},bf={tokenize:function(e,t,n){let r,o,i;return a;function a(t){return 46===t||95===t?e.check(wf,s,l)(t):null===t||kl(t)||Sl(t)||45!==t&&xl(t)?s(t):(i=!0,e.consume(t),a)}function l(t){return 95===t?r=!0:(o=r,r=void 0),e.consume(t),a}function s(e){return o||r||!i?n(e):t(e)}},partial:!0},kf={tokenize:function(e,t){let n=0,r=0;return o;function o(a){return 40===a?(n++,e.consume(a),o):41===a&&r<n?i(a):33===a||34===a||38===a||39===a||41===a||42===a||44===a||46===a||58===a||59===a||60===a||63===a||93===a||95===a||126===a?e.check(wf,t,i)(a):null===a||kl(a)||Sl(a)?t(a):(e.consume(a),o)}function i(t){return 41===t&&r++,e.consume(t),o}},partial:!0},wf={tokenize:function(e,t,n){return r;function r(a){return 33===a||34===a||39===a||41===a||42===a||44===a||46===a||58===a||59===a||63===a||95===a||126===a?(e.consume(a),r):38===a?(e.consume(a),i):93===a?(e.consume(a),o):60===a||null===a||kl(a)||Sl(a)?t(a):n(a)}function o(e){return null===e||40===e||91===e||kl(e)||Sl(e)?t(e):r(e)}function i(e){return dl(e)?a(e):n(e)}function a(t){return 59===t?(e.consume(t),r):dl(t)?(e.consume(t),a):n(t)}},partial:!0},xf={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return pl(e)?n(e):t(e)}},partial:!0},Sf={name:"wwwAutolink",tokenize:function(e,t,n){const r=this;return function(t){if(87!==t&&119!==t||!Pf.call(r,r.previous)||Rf(r.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(vf,e.attempt(bf,e.attempt(kf,o),n),n)(t)};function o(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:Pf},Ef={name:"protocolAutolink",tokenize:function(e,t,n){const r=this;let o="",i=!1;return function(t){if((72===t||104===t)&&Nf.call(r,r.previous)&&!Rf(r.events))return e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),o+=String.fromCodePoint(t),e.consume(t),a;return n(t)};function a(t){if(dl(t)&&o.length<5)return o+=String.fromCodePoint(t),e.consume(t),a;if(58===t){const n=o.toLowerCase();if("http"===n||"https"===n)return e.consume(t),l}return n(t)}function l(t){return 47===t?(e.consume(t),i?s:(i=!0,l)):n(t)}function s(t){return null===t||ml(t)||kl(t)||Sl(t)||xl(t)?n(t):e.attempt(bf,e.attempt(kf,u),n)(t)}function u(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:Nf},Cf={name:"emailAutolink",tokenize:function(e,t,n){const r=this;let o,i;return function(t){if(!Lf(t)||!Of.call(r,r.previous)||Rf(r.events))return n(t);return e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),a(t)};function a(t){return Lf(t)?(e.consume(t),a):64===t?(e.consume(t),l):n(t)}function l(t){return 46===t?e.check(xf,u,s)(t):45===t||95===t||pl(t)?(i=!0,e.consume(t),l):u(t)}function s(t){return e.consume(t),o=!0,l}function u(a){return i&&o&&dl(r.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(a)):n(a)}},previous:Of},_f={};let Tf=48;for(;Tf<123;)_f[Tf]=Cf,Tf++,58===Tf?Tf=65:91===Tf&&(Tf=97);function Pf(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||kl(e)}function Nf(e){return!dl(e)}function Of(e){return!(47===e||Lf(e))}function Lf(e){return 43===e||45===e||46===e||95===e||pl(e)}function Rf(e){let t=e.length,n=!1;for(;t--;){const r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}_f[43]=Cf,_f[45]=Cf,_f[46]=Cf,_f[95]=Cf,_f[72]=[Cf,Ef],_f[104]=[Cf,Ef],_f[87]=[Cf,Sf],_f[119]=[Cf,Sf];const Af={tokenize:function(e,t,n){const r=this;return _l(e,(function(e){const o=r.events[r.events.length-1];return o&&"gfmFootnoteDefinitionIndent"===o[1].type&&4===o[2].sliceSerialize(o[1],!0).length?t(e):n(e)}),"gfmFootnoteDefinitionIndent",5)},partial:!0};function jf(e,t,n){const r=this;let o=r.events.length;const i=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let a;for(;o--;){const e=r.events[o][1];if("labelImage"===e.type){a=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(o){if(!a||!a._balanced)return n(o);const l=fl(r.sliceSerialize({start:a.end,end:r.now()}));if(94!==l.codePointAt(0)||!i.includes(l.slice(1)))return n(o);return e.enter("gfmFootnoteCallLabelMarker"),e.consume(o),e.exit("gfmFootnoteCallLabelMarker"),t(o)}}function zf(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";const r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},o={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};o.end.column++,o.end.offset++,o.end._bufferIndex++;const i={type:"gfmFootnoteCallString",start:Object.assign({},o.end),end:Object.assign({},e[e.length-1][1].start)},a={type:"chunkString",contentType:"string",start:Object.assign({},i.start),end:Object.assign({},i.end)},l=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",o,t],["exit",o,t],["enter",i,t],["enter",a,t],["exit",a,t],["exit",i,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...l),e}function Df(e,t,n){const r=this,o=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let i,a=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),l};function l(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",s)}function s(l){if(a>999||93===l&&!i||null===l||91===l||kl(l))return n(l);if(93===l){e.exit("chunkString");const i=e.exit("gfmFootnoteCallString");return o.includes(fl(r.sliceSerialize(i)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(l),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(l)}return kl(l)||(i=!0),a++,e.consume(l),92===l?u:s}function u(t){return 91===t||92===t||93===t?(e.consume(t),a++,s):s(t)}}function Mf(e,t,n){const r=this,o=r.parser.gfmFootnotes||(r.parser.gfmFootnotes=[]);let i,a,l=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),s};function s(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",u):n(t)}function u(t){if(l>999||93===t&&!a||null===t||91===t||kl(t))return n(t);if(93===t){e.exit("chunkString");const n=e.exit("gfmFootnoteDefinitionLabelString");return i=fl(r.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),f}return kl(t)||(a=!0),l++,e.consume(t),92===t?c:u}function c(t){return 91===t||92===t||93===t?(e.consume(t),l++,u):u(t)}function f(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),o.includes(i)||o.push(i),_l(e,d,"gfmFootnoteDefinitionWhitespace")):n(t)}function d(e){return t(e)}}function If(e,t,n){return e.check(zl,t,e.attempt(Af,t,n))}function Ff(e){e.exit("gfmFootnoteDefinition")}function Uf(e){let t=(e||{}).singleTilde;const n={name:"strikethrough",tokenize:function(e,n,r){const o=this.previous,i=this.events;let a=0;return function(t){if(126===o&&"characterEscape"!==i[i.length-1][1].type)return r(t);return e.enter("strikethroughSequenceTemporary"),l(t)};function l(i){const s=Ol(o);if(126===i)return a>1?r(i):(e.consume(i),a++,l);if(a<2&&!t)return r(i);const u=e.exit("strikethroughSequenceTemporary"),c=Ol(i);return u._open=!c||2===c&&Boolean(s),u._close=!s||2===s&&Boolean(c),n(i)}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset===e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";const o={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},i={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},a=[["enter",o,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",i,t]],l=t.parser.constructs.insideSpan.null;l&&ol(a,a.length,0,Ll(l,e.slice(r+1,n),t)),ol(a,a.length,0,[["exit",i,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",o,t]]),ol(e,r-1,n-r+3,a),n=r+a.length-2;break}}n=-1;for(;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}class Bf{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let o=0;if(0===n&&0===r.length)return;for(;o<e.map.length;){if(e.map[o][0]===t)return e.map[o][1]+=n,void e.map[o][2].push(...r);o+=1}e.map.push([t,n,r])}(this,e,t,n)}consume(e){if(this.map.sort((function(e,t){return e[0]-t[0]})),0===this.map.length)return;let t=this.map.length;const n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(const t of r)e.push(t);r=n.pop()}this.map.length=0}}function Hf(e,t){let n=!1;const r=[];for(;t<e.length;){const o=e[t];if(n){if("enter"===o[0])"tableContent"===o[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===o[1].type){if("tableDelimiterMarker"===e[t-1][1].type){const e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===o[1].type)break}else"enter"===o[0]&&"tableDelimiterRow"===o[1].type&&(n=!0);t+=1}return r}function $f(e,t,n){const r=this;let o,i=0,a=0;return function(e){let t=r.events.length-1;for(;t>-1;){const e=r.events[t][1].type;if("lineEnding"!==e&&"linePrefix"!==e)break;t--}const o=t>-1?r.events[t][1].type:null,i="tableHead"===o||"tableRow"===o?k:l;if(i===k&&r.parser.lazy[r.now().line])return n(e);return i(e)};function l(t){return e.enter("tableHead"),e.enter("tableRow"),function(e){if(124===e)return s(e);return o=!0,a+=1,s(e)}(t)}function s(t){return null===t?n(t):bl(t)?a>1?(a=0,r.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),f):n(t):wl(t)?_l(e,s,"whitespace")(t):(a+=1,o&&(o=!1,i+=1),124===t?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),o=!0,s):(e.enter("data"),u(t)))}function u(t){return null===t||124===t||kl(t)?(e.exit("data"),s(t)):(e.consume(t),92===t?c:u)}function c(t){return 92===t||124===t?(e.consume(t),u):u(t)}function f(t){return r.interrupt=!1,r.parser.lazy[r.now().line]?n(t):(e.enter("tableDelimiterRow"),o=!1,wl(t)?_l(e,d,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):d(t))}function d(t){return 45===t||58===t?h(t):124===t?(o=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),p):b(t)}function p(t){return wl(t)?_l(e,h,"whitespace")(t):h(t)}function h(t){return 58===t?(a+=1,o=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),m):45===t?(a+=1,m(t)):null===t||bl(t)?v(t):b(t)}function m(t){return 45===t?(e.enter("tableDelimiterFiller"),g(t)):b(t)}function g(t){return 45===t?(e.consume(t),g):58===t?(o=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),y):(e.exit("tableDelimiterFiller"),y(t))}function y(t){return wl(t)?_l(e,v,"whitespace")(t):v(t)}function v(n){return 124===n?d(n):(null===n||bl(n))&&o&&i===a?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(n)):b(n)}function b(e){return n(e)}function k(t){return e.enter("tableRow"),w(t)}function w(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),w):null===n||bl(n)?(e.exit("tableRow"),t(n)):wl(n)?_l(e,w,"whitespace")(n):(e.enter("data"),x(n))}function x(t){return null===t||124===t||kl(t)?(e.exit("data"),w(t)):(e.consume(t),92===t?S:x)}function S(t){return 92===t||124===t?(e.consume(t),x):x(t)}}function Vf(e,t){let n,r,o,i=-1,a=!0,l=0,s=[0,0,0,0],u=[0,0,0,0],c=!1,f=0;const d=new Bf;for(;++i<e.length;){const p=e[i],h=p[1];"enter"===p[0]?"tableHead"===h.type?(c=!1,0!==f&&(qf(d,t,f,n,r),r=void 0,f=0),n={type:"table",start:Object.assign({},h.start),end:Object.assign({},h.end)},d.add(i,0,[["enter",n,t]])):"tableRow"===h.type||"tableDelimiterRow"===h.type?(a=!0,o=void 0,s=[0,0,0,0],u=[0,i+1,0,0],c&&(c=!1,r={type:"tableBody",start:Object.assign({},h.start),end:Object.assign({},h.end)},d.add(i,0,[["enter",r,t]])),l="tableDelimiterRow"===h.type?2:r?3:1):!l||"data"!==h.type&&"tableDelimiterMarker"!==h.type&&"tableDelimiterFiller"!==h.type?"tableCellDivider"===h.type&&(a?a=!1:(0!==s[1]&&(u[0]=u[1],o=Wf(d,t,s,l,void 0,o)),s=u,u=[s[1],i,0,0])):(a=!1,0===u[2]&&(0!==s[1]&&(u[0]=u[1],o=Wf(d,t,s,l,void 0,o),s=[0,0,0,0]),u[2]=i)):"tableHead"===h.type?(c=!0,f=i):"tableRow"===h.type||"tableDelimiterRow"===h.type?(f=i,0!==s[1]?(u[0]=u[1],o=Wf(d,t,s,l,i,o)):0!==u[1]&&(o=Wf(d,t,u,l,i,o)),l=0):!l||"data"!==h.type&&"tableDelimiterMarker"!==h.type&&"tableDelimiterFiller"!==h.type||(u[3]=i)}for(0!==f&&qf(d,t,f,n,r),d.consume(t.events),i=-1;++i<t.events.length;){const e=t.events[i];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=Hf(t.events,i))}return e}function Wf(e,t,n,r,o,i){const a=1===r?"tableHeader":2===r?"tableDelimiter":"tableData";0!==n[0]&&(i.end=Object.assign({},Qf(t.events,n[0])),e.add(n[0],0,[["exit",i,t]]));const l=Qf(t.events,n[1]);if(i={type:a,start:Object.assign({},l),end:Object.assign({},l)},e.add(n[1],0,[["enter",i,t]]),0!==n[2]){const o=Qf(t.events,n[2]),i=Qf(t.events,n[3]),a={type:"tableContent",start:Object.assign({},o),end:Object.assign({},i)};if(e.add(n[2],0,[["enter",a,t]]),2!==r){const r=t.events[n[2]],o=t.events[n[3]];if(r[1].end=Object.assign({},o[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){const t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",a,t]])}return void 0!==o&&(i.end=Object.assign({},Qf(t.events,o)),e.add(o,0,[["exit",i,t]]),i=void 0),i}function qf(e,t,n,r,o){const i=[],a=Qf(t.events,n);o&&(o.end=Object.assign({},a),i.push(["exit",o,t])),r.end=Object.assign({},a),i.push(["exit",r,t]),e.add(n+1,0,i)}function Qf(e,t){const n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}const Kf={name:"tasklistCheck",tokenize:function(e,t,n){const r=this;return function(t){if(null!==r.previous||!r._gfmTasklistFirstContentOfListItem)return n(t);return e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),o};function o(t){return kl(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),i):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),i):n(t)}function i(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),a):n(t)}function a(r){return bl(r)?t(r):wl(r)?e.check({tokenize:Yf},t,n)(r):n(r)}}};function Yf(e,t,n){return _l(e,(function(e){return null===e?n(e):t(e)}),"whitespace")}const Xf={};function Jf(e){const t=e||Xf,n=this.data(),r=n.micromarkExtensions||(n.micromarkExtensions=[]),o=n.fromMarkdownExtensions||(n.fromMarkdownExtensions=[]),i=n.toMarkdownExtensions||(n.toMarkdownExtensions=[]);r.push(function(e){return ll([{text:_f},{document:{91:{name:"gfmFootnoteDefinition",tokenize:Mf,continuation:{tokenize:If},exit:Ff}},text:{91:{name:"gfmFootnoteCall",tokenize:Df},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:jf,resolveTo:zf}}},Uf(e),{flow:{null:{name:"table",tokenize:$f,resolveAll:Vf}}},{text:{91:Kf}}])}(t)),o.push([{transforms:[vc],enter:{literalAutolink:dc,literalAutolinkEmail:pc,literalAutolinkHttp:pc,literalAutolinkWww:pc},exit:{literalAutolink:yc,literalAutolinkEmail:gc,literalAutolinkHttp:hc,literalAutolinkWww:mc}},{enter:{gfmFootnoteCallString:xc,gfmFootnoteCall:Sc,gfmFootnoteDefinitionLabelString:Ec,gfmFootnoteDefinition:Cc},exit:{gfmFootnoteCallString:_c,gfmFootnoteCall:Tc,gfmFootnoteDefinitionLabelString:Pc,gfmFootnoteDefinition:Nc}},{canContainEols:["delete"],enter:{strikethrough:zc},exit:{strikethrough:Dc}},{enter:{table:lf,tableData:ff,tableHeader:ff,tableRow:uf},exit:{codeText:df,table:sf,tableData:cf,tableHeader:cf,tableRow:cf}},{exit:{taskListCheckValueChecked:mf,taskListCheckValueUnchecked:mf,paragraph:gf}}]),i.push(function(e){return{extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:cc,notInConstruct:fc},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:cc,notInConstruct:fc},{character:":",before:"[ps]",after:"\\/",inConstruct:cc,notInConstruct:fc}]},Lc(e),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:jc}],handlers:{delete:Mc}},hf(e),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:yf}}]}}(t))}const Gf=({content:e,className:t=""})=>{if(!e)return null;const n=[];let r=0;const o=/<think>([\s\S]*?)<\/think>/g;let i;for(;null!==(i=o.exec(e));)i.index>r&&n.push({type:"normal",text:e.slice(r,i.index)}),n.push({type:"think",text:i[1]}),r=o.lastIndex;r<e.length&&n.push({type:"normal",text:e.slice(r)});const a={remarkPlugins:[Jf],components:{code:({node:e,inline:t,className:n,children:r,...o})=>{const i=Array.isArray(r)?r.join(""):r||"";return i.trim()?!t&&n&&n.includes("language-svg")?l.jsx(Ni,{svgContent:i}):t?l.jsx("code",{className:"inline-code",...o,children:r}):l.jsx("pre",{className:"code-block",children:l.jsx("code",{className:n,...o,children:r})}):null},table:({children:e})=>l.jsx("div",{className:"table-container",children:l.jsx("table",{className:"markdown-table",children:e})}),a:({href:e,children:t})=>l.jsx("a",{href:e,target:"_blank",rel:"noopener noreferrer",className:"markdown-link",children:t}),img:({src:e,alt:t})=>l.jsx("img",{src:e,alt:t,className:"markdown-image"}),ul:({children:e})=>l.jsx("ul",{className:"markdown-list",children:e}),ol:({children:e})=>l.jsx("ol",{className:"markdown-list",children:e}),blockquote:({children:e})=>l.jsx("blockquote",{className:"markdown-blockquote",children:e}),h1:({children:e})=>l.jsx("h1",{className:"markdown-h1",children:e}),h2:({children:e})=>l.jsx("h2",{className:"markdown-h2",children:e}),h3:({children:e})=>l.jsx("h3",{className:"markdown-h3",children:e}),h4:({children:e})=>l.jsx("h4",{className:"markdown-h4",children:e}),h5:({children:e})=>l.jsx("h5",{className:"markdown-h5",children:e}),h6:({children:e})=>l.jsx("h6",{className:"markdown-h6",children:e})}};return l.jsx("div",{className:`markdown-renderer ${t}`,children:n.map(((e,t)=>"think"===e.type?l.jsx("div",{className:"thinking-process",children:l.jsx(oc,{...a,children:e.text})},t):h.createElement(oc,{...a,key:t},e.text)))})},Zf=({src:e,alt:t,onClose:n})=>{const[r,o]=h.useState(1),[i,a]=h.useState({x:0,y:0}),[s,u]=h.useState(!1),[c,f]=h.useState({x:0,y:0});h.useEffect((()=>{const e=e=>{"Escape"===e.key&&n()};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)}),[n]);const d=()=>{u(!1)};return l.jsxs("div",{className:"image-viewer-overlay",onClick:e=>{e.target===e.currentTarget&&n()},onMouseMove:e=>{s&&a({x:e.clientX-c.x,y:e.clientY-c.y})},onMouseUp:d,onMouseLeave:d,children:[l.jsxs("div",{className:"image-viewer-controls",children:[l.jsx("button",{onClick:()=>o((e=>Math.min(e+.1,10))),children:l.jsx("svg",{viewBox:"0 0 24 24",width:"24",height:"24",children:l.jsx("path",{fill:"currentColor",d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"})})}),l.jsx("button",{onClick:()=>o((e=>Math.max(e-.1,.1))),children:l.jsx("svg",{viewBox:"0 0 24 24",width:"24",height:"24",children:l.jsx("path",{fill:"currentColor",d:"M19 13H5v-2h14v2z"})})}),l.jsx("button",{onClick:()=>{o(1),a({x:0,y:0})},children:l.jsx("svg",{viewBox:"0 0 24 24",width:"24",height:"24",children:l.jsx("path",{fill:"currentColor",d:"M9 3L5 7h3v7c0 .55.45 1 1 1s1-.45 1-1V7h3L9 3zm6 14v-7c0-.55-.45-1-1-1s-1 .45-1 1v7h-3l4 4 4-4h-3z"})})}),l.jsx("button",{onClick:n,children:l.jsx("svg",{viewBox:"0 0 24 24",width:"24",height:"24",children:l.jsx("path",{fill:"currentColor",d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"})})})]}),l.jsx("img",{src:e,alt:t,className:"image-viewer-image",style:{transform:`translate(${i.x}px, ${i.y}px) scale(${r})`,cursor:s?"grabbing":"grab"},onMouseDown:e=>{e.preventDefault(),u(!0),f({x:e.clientX-i.x,y:e.clientY-i.y})},onWheel:e=>{e.preventDefault();const t=-.01*e.deltaY,n=Math.min(Math.max(.1,r+t),10);o(n)},onDoubleClick:()=>{o(1),a({x:0,y:0})},draggable:"false"})]})},ed={API_KEY:"app-2XnCWvdqFxRWhUP69QSsjQbZ",BASE_URL:"http://ai-model.kaikungroup.com:8081",get CHAT_API_URL(){return`${this.BASE_URL}/v1/chat-messages`},get HISTORY_API_URL(){return`${this.BASE_URL}/v1/messages`},get AUTHORIZATION_HEADER(){return"Bearer "+this.API_KEY},MAX_RETRIES:3,RETRY_DELAY:2e3,STREAM_TIMEOUT:5e6,NO_DATA_TIMEOUT:3e3},{AUTHORIZATION_HEADER:td}=ed,nd=({isDarkMode:e,conversationId:t,isNewChat:n,onRefreshConversations:r,onConversationSelect:o})=>{const[i,a]=h.useState(""),[s,u]=h.useState([]),[c,f]=h.useState(!1),[d,p]=h.useState(!1),[m,g]=h.useState(null),[y,v]=h.useState(!1),[b,k]=h.useState(null),w=J((e=>e.auth.userId)),x=h.useRef(null),S=h.useRef(null),E=h.useRef(null),[C,_]=h.useState(window.innerWidth<=750),T=ed.CHAT_API_URL,P=ed.HISTORY_API_URL,N=ed.AUTHORIZATION_HEADER,O=ed.STREAM_TIMEOUT,L=ed.NO_DATA_TIMEOUT,R=ed.MAX_RETRIES,A=ed.RETRY_DELAY,j=()=>{var e;null==(e=x.current)||e.scrollIntoView({behavior:"smooth"})};h.useEffect((()=>{j()}),[s]),h.useEffect((()=>()=>{S.current&&clearTimeout(S.current)}),[]);const z=(e="")=>{u((t=>{const n=t[t.length-1];if(!n||n.isUser)return t;/<think>/.test(e)?v(!0):v(!1);const r=(n.text||"")+e;return[...t.slice(0,-1),{...n,text:r}]}))},D=(e="")=>{u((t=>{const n=t[t.length-1];return!n||n.isUser?t:[...t.slice(0,-1),{...n,text:e}]}))},M=async(e,t=!1)=>{const n=new TextDecoder;let i,a,l="",s=!0,u=null,c=!0,d=Date.now(),p=0;const h=()=>0===l.length&&p<R&&(m(),p++,!0),m=()=>{if(clearTimeout(i),clearTimeout(a),e)try{e.cancel()}catch(ld){}};try{for(i=setTimeout((()=>{Date.now();h()||(m(),c&&z("[响应超时，请重试]"))}),O),a=setTimeout((()=>{if(h())throw new Error("NO_DATA_RETRY")}),L);;){if(!c){m();break}try{const{done:r,value:o}=await e.read();if(d=Date.now(),clearTimeout(a),r){c&&f(!1);break}clearTimeout(i),i=setTimeout((()=>{Date.now();h()||(m(),c&&z("[响应超时，请重试]"))}),O);const p=n.decode(o,{stream:!0});l+=p;const g=l.split("\n");l=g.pop()||"";for(const e of g){if(!c)break;if(e.trim()&&(":heartbeat"!==e&&!e.startsWith("event: ping")))try{const n=e.startsWith("data: ")?e.substring(6):e;if(n.trim().startsWith("{")){const e=JSON.parse(n);if(t&&e.conversation_id&&(u=e.conversation_id),e.answer&&c){const t=String(e.answer);s?(s=!1,D(t)):z(t)}}}catch(ld){e.startsWith("event: ping")}}}catch(g){throw g}}c&&t&&u&&r&&(o(u),r())}catch(y){throw c&&z("已取消当前对话"),y}finally{c&&f(!1),m()}return()=>{c=!1,m()}};h.useEffect((()=>{if(t){if(!n){u([]);let e=!0;return(async()=>{p(!0);try{const n=await fetch(`${P}?user=${w}&conversation_id=${t}`,{method:"GET",headers:{Authorization:N,"Content-Type":"application/json"}});if(!n.ok)throw new Error("获取历史消息失败");const r=await n.json();if(e){const e=[];r.data.forEach((t=>{t.query&&e.push({text:t.query,isUser:!0,timestamp:t.created_at}),t.answer&&e.push({text:t.answer,isUser:!1,timestamp:t.created_at})})),u(e),j()}}catch(n){}finally{e&&p(!1)}})(),()=>{e=!1}}u([{text:"您好，我是博笛智家智能AI设计师，我可以为您提供以下服务，请点击选择！",isUser:!1,timestamp:(new Date).toISOString()}]),j()}return()=>{}}),[t,n]);const I=[{label:"设计图纸",value:"请生成一份房屋设计图纸"}],F=async e=>{try{const t=await fetch(e);if(t.ok){const e=await t.blob();U(e)}}catch(t){}},U=e=>{try{const t=window.URL.createObjectURL(e),n=document.createElement("a");n.href=t,n.download=`design_${Date.now()}.dwg`,document.body.appendChild(n),n.click(),window.URL.revokeObjectURL(t),document.body.removeChild(n)}catch(t){}};return h.useEffect((()=>{!c&&E.current&&E.current.focus()}),[c]),h.useEffect((()=>{const e=()=>_(window.innerWidth<=750);return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]),l.jsxs("div",{className:"chat-containers "+(e?"dark-theme":"light-colour"),children:[l.jsxs("div",{className:"messages",children:[d?l.jsx("div",{className:"loading-history",children:"加载历史消息中..."}):0===s.length?l.jsx("div",{className:"empty-chat",children:l.jsx("p",{children:"开始新的对话吧"})}):s.map(((r,o)=>l.jsx("div",{className:"message "+(r.isUser?"user":"ai"),children:r.isUser?l.jsx(l.Fragment,{children:String(r.text||"")}):l.jsxs(l.Fragment,{children:[(()=>{const t=String(r.text||"").replace(/https?:\/\/[^\s<>"]+?\.svg/gi,"").trim();if(!t)return l.jsx("div",{className:"svg-hidden-message",children:l.jsx("span",{style:{color:"#999",fontSize:"14px"},children:"[内容已隐藏]"})});const n=(e=>{if(!e)return[];const t=[];let n=0;const r=/!\[([^\]]*)\]\(([^)]+)\)/g,o=/(https?:\/\/[^\s<>"]+?\.(?:jpg|jpeg|gif|png|webp))/gi,i=/(https?:\/\/[^\s<>"]+?\.svg)/gi,a=/(data:image\/[^;]+;base64,[^"'\s]+)/g,l=[];let s;for(;null!==(s=r.exec(e));)l.push({index:s.index,length:s[0].length,type:"markdown",url:s[2],alt:s[1]});for(;null!==(s=o.exec(e));)l.some((e=>"markdown"===e.type&&s.index>=e.index&&s.index<e.index+e.length))||l.push({index:s.index,length:s[0].length,type:"url",url:s[0]});for(;null!==(s=i.exec(e));)l.some((e=>"markdown"===e.type&&s.index>=e.index&&s.index<e.index+e.length))||l.push({index:s.index,length:s[0].length,type:"svg-url",url:s[0]});for(;null!==(s=a.exec(e));)l.push({index:s.index,length:s[0].length,type:"base64",url:s[0]});l.sort(((e,t)=>e.index-t.index));for(const u of l){if(u.index>n){const r=e.slice(n,u.index);r.trim()&&t.push({type:"text",content:r})}t.push({type:"image",url:u.url,alt:u.alt||"图片"}),n=u.index+u.length}if(n<e.length){const r=e.slice(n);r.trim()&&t.push({type:"text",content:r})}return t})(t),o=n.filter((e=>"svg-url"!==e.type&&"svg"!==e.type)),i=o.filter((e=>"image"===e.type)),a=o.filter((e=>"image"!==e.type));return l.jsxs(l.Fragment,{children:[a.map(((t,n)=>"svg"===t.type||"svg-url"===t.type?null:l.jsx(Gf,{content:t.content,className:e?"dark-theme":""},`other-${n}`))),i.length>0&&l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"images-wrapper",children:i.map(((e,t)=>{const n=e.url;return l.jsx("div",{className:"message-image-container",children:l.jsx("img",{src:n,alt:e.alt||"图片",className:"message-image",onClick:()=>k({src:n,alt:e.alt||"图片"}),onError:e=>{e.target.style.display="none"}})},`image-${t}`)}))}),l.jsx("div",{className:"buttons-wrapper",children:i.some((e=>{return(t=e.url)&&(t.endsWith(".svg")||t.includes("/svg/")||t.includes("processed_svg"));var t}))&&l.jsxs("button",{className:"download-svg-button independent-button",onClick:()=>(async()=>{try{const t=[];if(s.forEach(((e,n)=>{if(e.text){const r=String(e.text),o=/https?:\/\/[^\s<>"]+?\.svg/gi,i=r.match(o);i&&i.forEach(((e,o)=>{t.push({messageIndex:n,linkIndex:o,url:e,messageText:r.substring(0,100)+(r.length>100?"...":"")})}))}})),t.length>0){const n=t[0].url;try{const e=await fetch("http://ai-model.kaikungroup.com:8020/convert",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({svg_content:n})});if(e.ok){const t=e.headers.get("content-type");if(t&&t.includes("application/json")){const t=await e.json();if(t.download_url||t.file_url||t.dwg_url){const e=t.download_url||t.file_url||t.dwg_url;await F(e)}}else{const t=await e.blob();U(t)}}else await e.text()}catch(e){}}}catch(t){}})(),title:"获取并下载图纸",children:[l.jsx("svg",{viewBox:"0 0 24 24",width:"24",height:"24",children:l.jsx("path",{fill:"currentColor",d:"M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"})}),"下载图纸"]})})]})]})})(),n&&0===o&&!c&&l.jsx("div",{style:{marginTop:12,display:"flex",gap:8},children:I.map((e=>l.jsx("button",{type:"button",className:"send-button messages-button",style:{background:"#10b981",fontSize:14,padding:"6px 16px"},onClick:()=>(async e=>{if(!c){a(""),f(!0),u((t=>[...t,{text:e,isUser:!0,timestamp:Date.now()},{text:"",isUser:!1,timestamp:Date.now()+1}]));try{let o=0,i=!1;for(;!i&&o<R;)try{const r=new AbortController;g(r);const o={inputs:{},query:e,response_mode:"streaming",user:w};!n&&t&&(o.conversation_id=t);const a=await fetch(T,{method:"POST",headers:{Authorization:N,"Content-Type":"application/json"},body:JSON.stringify(o),signal:r.signal});if(!a.ok)throw new Error(a.statusText);if(!a.body)throw new Error("无可读流");const l=a.body.getReader();await M(l,n),i=!0}catch(r){if(o++,!(o<R&&"AbortError"!==r.name))throw r;z(`\n\n[网络连接不稳定，尝试重新连接 (${o}/${R})...]`),await new Promise((e=>setTimeout(e,A)))}}catch(r){"AbortError"!==r.name&&z("\n\n[请求出错，请稍后重试]")}finally{f(!1),g(null)}}})(e.value),children:e.label},e.value)))}),!r.isUser&&o===s.length-1&&c&&l.jsxs("span",{className:"loading-dots",children:[l.jsx("span",{className:"dot"}),l.jsx("span",{className:"dot"}),l.jsx("span",{className:"dot"})]})]})},`${r.timestamp}-${o}`))),l.jsx("div",{ref:x})]}),l.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),!i.trim()||c)return;const r=i;let o;a(""),f(!0);const l=new AbortController;g(l),u((e=>[...e,{text:r,isUser:!0,timestamp:Date.now()},{text:"",isUser:!1,timestamp:Date.now()+1}]));try{let e=0,i=!1;for(;!i&&e<R;)try{const e={inputs:{},query:r,response_mode:"streaming",user:w};!n&&t&&(e.conversation_id=t);const a=await fetch(T,{method:"POST",headers:{Authorization:N,"Content-Type":"application/json"},body:JSON.stringify(e),signal:l.signal});if(!a.ok)throw new Error(a.statusText);if(!a.body)throw new Error("无可读流");const s=a.body.getReader();o=await M(s,n),i=!0}catch(s){if("AbortError"===s.name)throw s;if(e++,!(e<R))throw s;z(`\n\n[网络连接不稳定，尝试重新连接 (${e}/${R})...]`),await new Promise((e=>setTimeout(e,A)))}}catch(s){"AbortError"!==s.name&&z("\n\n[请求出错，请稍后重试]")}finally{f(!1),g(null)}return()=>{o&&o(),l&&l.abort()}},children:[c&&l.jsxs("div",{className:"status-row",children:[l.jsxs("span",{className:"generating-text",children:[l.jsxs("svg",{viewBox:"0 0 24 24",width:"16",height:"16",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[l.jsx("circle",{cx:"12",cy:"12",r:"10"}),l.jsx("path",{d:"M12 6v6l4 2"})]}),"正在生成回答..."]}),l.jsx("button",{type:"button",onClick:()=>{m&&(m.abort(),g(null),f(!1))},className:"abort-button",children:"停止生成"})]}),l.jsxs("div",{className:"input-row",children:[l.jsx("input",{ref:E,type:"text",value:i,onChange:e=>a(e.target.value),placeholder:"输入消息...",disabled:c,autoFocus:!0}),l.jsx("button",{type:"submit",disabled:c,className:"send-button",title:"发送消息",children:C?l.jsxs("svg",{viewBox:"0 0 24 24",width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[l.jsx("path",{d:"M12 19V5"}),l.jsx("polyline",{points:"5 12 12 5 19 12"})]}):l.jsx("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:l.jsx("path",{d:"M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z"})})})]})]}),b&&l.jsx(Zf,{src:b.src,alt:b.alt,onClose:()=>k(null)})]})},rd=()=>{const e=fn(),t=K(),n=J((e=>e.auth.userId)),[r,o]=h.useState((()=>"dark"===localStorage.getItem("chatTheme"))),[i,a]=h.useState(!1),[s]=h.useState((()=>localStorage.getItem("userName")||"U")),[u,c]=h.useState(null),[f,d]=h.useState(!1),[p,m]=h.useState(0),[g,y]=h.useState(!1),[v,b]=h.useState(window.innerWidth<=750),[k,w]=h.useState([]);h.useEffect((()=>{const e=()=>{b(window.innerWidth<=750),window.innerWidth>750&&y(!1)};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]);const x=h.useCallback((async()=>{try{const e=await fetch(`http://ai-model.kaikungroup.com:8081/v1/conversations?user=${n}&limit=100`,{method:"GET",headers:{Authorization:"Bearer app-2XnCWvdqFxRWhUP69QSsjQbZ","Content-Type":"application/json"}});if(!e.ok)throw new Error("获取会话列表失败");const t=(await e.json()).data||[];w(t),m((e=>e+1))}catch(e){}}),[n]),S=()=>{o((e=>{const t=!e;return localStorage.setItem("chatTheme",t?"dark":"light"),t}))},E=()=>{e("/")},C=()=>{localStorage.removeItem("access_token"),t(gt()),e("/")},_=(e,t=!1)=>{c(e),d(t),v&&y(!1)},T=async()=>{k.some((e=>e.id.toString().startsWith("new-")))&&await x();const e={id:"new-"+Date.now(),name:"新对话",introduction:"您好，有什么需要帮助您呢",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString(),isNew:!0};w((t=>[e,...t])),d(!0),c(e.id),v&&y(!1)};return l.jsxs("div",{className:"chat-container "+(r?"dark-theme":""),children:[l.jsxs("div",{className:"chat-header mobile-chat-header",children:[l.jsxs("div",{className:"header-left",children:[v&&l.jsx("button",{className:"menu-button",onClick:()=>y(!0),title:"菜单",children:l.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[l.jsx("line",{x1:"3",y1:"12",x2:"21",y2:"12"}),l.jsx("line",{x1:"3",y1:"6",x2:"21",y2:"6"}),l.jsx("line",{x1:"3",y1:"18",x2:"21",y2:"18"})]})}),!v&&l.jsx("button",{className:"close-button",onClick:E,title:"关闭",children:l.jsx("svg",{viewBox:"0 0 24 24",width:"20",height:"20",children:l.jsx("path",{fill:"currentColor",d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})})})]}),l.jsx("h1",{className:"header-title",children:"博笛AI建筑设计"}),v&&l.jsx("button",{className:"new-chat-icon",onClick:T,title:"新建对话",children:l.jsx("svg",{viewBox:"0 0 1024 1024",width:"30",height:"30",children:l.jsx("path",{d:"M285.074286 849.078857c44.690286 0 134.985143-43.410286 203.154285-91.940571 226.925714 4.169143 396.617143-126.976 396.617143-299.227429 0-166.180571-166.180571-299.885714-373.174857-299.885714-206.994286 0-372.845714 133.705143-372.845714 299.885714 0 105.728 65.572571 199.899429 166.180571 249.088-13.824 26.038857-38.253714 61.732571-52.077714 80.347429-20.553143 27.318857-6.436571 61.732571 32.146286 61.732571z m38.875428-66.56c-4.169143 1.609143-5.778286-0.621714-2.889143-4.169143 16.420571-19.931429 40.521143-51.748571 52.406858-74.24 9.654857-17.664 6.436571-33.097143-14.153143-42.752-98.011429-45.970286-154.916571-119.588571-154.916572-203.446857 0-129.243429 136.265143-234.349714 307.273143-234.349714 171.008 0 307.309714 105.106286 307.309714 234.313143 0 129.243429-136.301714 234.642286-307.309714 234.642285-5.778286 0-14.774857-0.292571-25.709714-0.292571-13.824-0.365714-24.137143 3.84-36.937143 13.824-38.912 28.891429-96.438857 64.914286-125.074286 76.470857z m69.12-291.510857h88.393143v88.393143c0 17.664 12.544 30.208 29.878857 30.208 18.029714 0 30.866286-12.544 30.866286-30.208v-88.393143h88.393143c17.700571 0 30.208-12.544 30.208-30.208 0-18.029714-12.507429-30.537143-30.208-30.537143h-88.393143V341.869714c0-18.029714-12.836571-30.537143-30.866286-30.537143-17.334857 0-29.878857 12.507429-29.878857 30.537143v88.393143h-88.393143c-17.993143 0-30.537143 12.507429-30.537143 30.537143 0 17.664 12.544 30.208 30.537143 30.208z",fill:"currentColor"})})}),!v&&l.jsxs("div",{className:"header-right",children:[l.jsxs("div",{className:"settings-dropdown",onMouseEnter:()=>a(!0),onMouseLeave:()=>a(!1),children:[l.jsx("button",{className:"settings-button",title:"设置",children:l.jsxs("svg",{viewBox:"0 0 24 24",width:"20",height:"20",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[l.jsx("path",{d:"M12 15a3 3 0 100-6 3 3 0 000 6z"}),l.jsx("path",{d:"M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z"})]})}),i&&l.jsxs("div",{className:"settings-menu",children:[l.jsx("button",{children:"保存对话"}),l.jsx("button",{children:"导出对话"})]})]}),l.jsx("button",{className:"theme-button",onClick:S,title:r?"切换到浅色模式":"切换到深色模式",children:r?l.jsxs("svg",{viewBox:"0 0 24 24",width:"20",height:"20",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[l.jsx("circle",{cx:"12",cy:"12",r:"5"}),l.jsx("path",{d:"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"})]}):l.jsx("svg",{viewBox:"0 0 24 24",width:"20",height:"20",fill:"none",stroke:"currentColor",strokeWidth:"2",children:l.jsx("path",{d:"M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"})})}),l.jsx("div",{className:"user-info",children:l.jsx("div",{className:"user-avatar",children:l.jsxs("svg",{viewBox:"0 0 24 24",width:"20",height:"20",fill:"none",stroke:"white",strokeWidth:"2",children:[l.jsx("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),l.jsx("circle",{cx:"12",cy:"7",r:"4"})]})})}),l.jsx("button",{className:"logout-button",onClick:C,title:"退出",children:l.jsxs("svg",{viewBox:"0 0 24 24",width:"20",height:"20",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[l.jsx("path",{d:"M9 21H5a2 2 0 01-2-2V5a2 2 0 012-2h4"}),l.jsx("path",{d:"M16 17l5-5-5-5"}),l.jsx("path",{d:"M21 12H9"})]})})]})]}),l.jsxs("div",{className:"chat-main",children:[!v&&l.jsx(Pi,{isDarkMode:r,onConversationSelect:_,fetchTrigger:p}),l.jsx("div",{className:"chat-content-area",children:l.jsx(nd,{isDarkMode:r,conversationId:u,isNewChat:f,onRefreshConversations:x,onConversationSelect:_,isMobile:v})})]}),v&&g&&l.jsx("div",{className:"sidebar-mask",onClick:()=>y(!1)}),v&&l.jsxs("div",{className:"mobile-sidebar-drawer"+(g?" open":""),children:[l.jsxs("div",{className:"mobile-sidebar-header",children:[l.jsxs("div",{className:"user-info",children:[l.jsx("div",{className:"user-avatar",children:l.jsxs("svg",{viewBox:"0 0 24 24",width:"20",height:"20",fill:"none",stroke:"white",strokeWidth:"2",children:[l.jsx("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),l.jsx("circle",{cx:"12",cy:"7",r:"4"})]})}),l.jsx("span",{className:"user-name",children:s})]}),l.jsx("button",{className:"logout-button",onClick:C,title:"退出",children:l.jsxs("svg",{viewBox:"0 0 24 24",width:"20",height:"20",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[l.jsx("path",{d:"M9 21H5a2 2 0 01-2-2V5a2 2 0 012-2h4"}),l.jsx("path",{d:"M16 17l5-5-5-5"}),l.jsx("path",{d:"M21 12H9"})]})})]}),l.jsx(Pi,{isDarkMode:r,onConversationSelect:_,fetchTrigger:p})]})]})},od=()=>{const[e,t]=h.useState('<svg width="512" height="512" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">\n  <g id="layer-bottom">\n    \x3c!-- 客厅 --\x3e\n    <path d="M 80,60 L 200,60 L 200,140 L 80,140 Z" stroke="black" stroke-width="3" fill="none"/>\n    <text x="140" y="105" font-size="12" text-anchor="middle" fill="black">客厅</text>\n    \n    \x3c!-- 餐厅 --\x3e\n    <path d="M 200,60 L 280,60 L 280,140 L 200,140 Z" stroke="black" stroke-width="3" fill="none"/>\n    <text x="240" y="105" font-size="12" text-anchor="middle" fill="black">餐厅</text>\n    \n    \x3c!-- 厨房 --\x3e\n    <path d="M 280,60 L 360,60 L 360,140 L 280,140 Z" stroke="black" stroke-width="3" fill="none"/>\n    <text x="320" y="105" font-size="12" text-anchor="middle" fill="black">厨房</text>\n    \n    \x3c!-- 主卧 --\x3e\n    <path d="M 80,140 L 200,140 L 200,220 L 80,220 Z" stroke="black" stroke-width="3" fill="none"/>\n    <text x="140" y="185" font-size="12" text-anchor="middle" fill="black">主卧</text>\n    \n    \x3c!-- 主卧卫生间 --\x3e\n    <path d="M 200,140 L 250,140 L 250,180 L 200,180 Z" stroke="black" stroke-width="3" fill="none"/>\n    <text x="225" y="165" font-size="10" text-anchor="middle" fill="black">主卫</text>\n    \n    \x3c!-- 次卧 --\x3e\n    <path d="M 200,180 L 280,180 L 280,220 L 200,220 Z" stroke="black" stroke-width="3" fill="none"/>\n    <text x="240" y="205" font-size="12" text-anchor="middle" fill="black">次卧</text>\n    \n    \x3c!-- 儿童房 --\x3e\n    <path d="M 280,140 L 360,140 L 360,220 L 280,220 Z" stroke="black" stroke-width="3" fill="none"/>\n    <text x="320" y="185" font-size="12" text-anchor="middle" fill="black">儿童房</text>\n    \n    \x3c!-- 客卫 --\x3e\n    <path d="M 80,220 L 130,220 L 130,260 L 80,260 Z" stroke="black" stroke-width="3" fill="none"/>\n    <text x="105" y="245" font-size="10" text-anchor="middle" fill="black">客卫</text>\n    \n    \x3c!-- 走廊 --\x3e\n    <path d="M 130,220 L 280,220 L 280,260 L 130,260 Z" stroke="black" stroke-width="3" fill="none"/>\n    <text x="205" y="245" font-size="12" text-anchor="middle" fill="black">走廊</text>\n    \n    \x3c!-- 储物间 --\x3e\n    <path d="M 280,220 L 360,220 L 360,260 L 280,260 Z" stroke="black" stroke-width="3" fill="none"/>\n    <text x="320" y="245" font-size="10" text-anchor="middle" fill="black">储物间</text>\n    \n    \x3c!-- 阳台 --\x3e\n    <path d="M 80,40 L 200,40 L 200,60 L 80,60 Z" stroke="black" stroke-width="3" fill="none"/>\n    <text x="140" y="55" font-size="10" text-anchor="middle" fill="black">阳台</text>\n  </g>\n</svg>'),[n,r]=h.useState(!1);return l.jsxs("div",{className:"svg-demo "+(n?"dark-theme":"light-theme"),children:[l.jsxs("div",{className:"demo-header",children:[l.jsx("h1",{children:"SVG渲染演示"}),l.jsx("button",{onClick:()=>{r(!n)},className:"theme-toggle",children:n?"🌞":"🌙"})]}),l.jsxs("div",{className:"demo-content",children:[l.jsxs("div",{className:"demo-section",children:[l.jsx("h2",{children:"房屋平面图"}),l.jsx(Ni,{svgContent:e,className:n?"dark-theme":""})]}),l.jsxs("div",{className:"demo-section",children:[l.jsx("h2",{children:"SVG代码"}),l.jsx("textarea",{value:e,onChange:e=>t(e.target.value),className:"svg-code",rows:"20",placeholder:"输入SVG代码..."})]})]})]})},id=({children:e})=>{const{accessToken:t}=J((e=>e.auth));return t?e:l.jsx(xn,{to:"/login"})};function ad(){return l.jsx(Qn,{children:l.jsxs(Cn,{children:[l.jsx(Sn,{path:"/",element:l.jsx(tr,{})}),l.jsx(Sn,{path:"/login",element:l.jsx(Ti,{})}),l.jsx(Sn,{path:"/chat",element:l.jsx(id,{children:l.jsx(rd,{})})}),l.jsx(Sn,{path:"/svg-demo",element:l.jsx(od,{})}),l.jsx(Sn,{path:"*",element:l.jsx(xn,{to:"/"})})]})})}setInterval((()=>{const{tokenExpiry:e}=vt.getState().auth;e&&Date.now()>e-3e5&&_i().catch(console.error)}),3e5);O.createRoot(document.getElementById("root")).render(l.jsx(H,{store:vt,children:l.jsx(ad,{})}));
