// src/features/status/statusSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  isLoading: false,
  controller: null
};

export const statusSlice = createSlice({
  name: 'status',
  initialState,
  reducers: {
    startLoading: (state, action) => {
      state.isLoading = true;
      state.controller = action.payload;
    },
    stopLoading: (state) => {
      state.isLoading = false;
      state.controller = null;
    },
    abortRequest: (state) => {
      if (state.controller) {
        state.controller.abort();
      }
      state.isLoading = false;
      state.controller = null;
    }
  }
});

export const { 
  startLoading, 
  stopLoading, 
  abortRequest 
} = statusSlice.actions;

export const selectIsLoading = state => state.status.isLoading;
export const selectController = state => state.status.controller;

export default statusSlice.reducer;