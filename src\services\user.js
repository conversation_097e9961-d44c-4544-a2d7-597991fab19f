import api from '../utils/api';

export const getUserProfile = async () => {
  try {
    const response = await api.get('/api/user/profile');
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const updateUserProfile = async (data) => {
  try {
    const response = await api.put('/api/user/profile', data);
    return response.data;
  } catch (error) {
    throw error;
  }
};