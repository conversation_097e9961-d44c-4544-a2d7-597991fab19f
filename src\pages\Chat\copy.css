.chat-container {
    display: flex;
    height: 100vh;
  }
  /* 隐藏所有元素的滚动条 */
  /* ::-webkit-scrollbar {
    display: none; 
  } */
  
  /* html, body {
    -ms-overflow-style: none; 
    scrollbar-width: none;  
  } */
  .chat-container .new-chat-btn {
    width: 100%;
    padding: 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    margin-bottom: 20px;
    cursor: pointer;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
  }
  /* 侧边栏样式 */
  /* .chat-container .sidebar {
      width: calc(20vw - 60px);
    background: #f5f5f5;
    border-right: 1px solid #ddd;
    padding: 20px;
    background: linear-gradient(135deg, #1a2a6c, #2c3e50);
    color: #000;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    overflow: hidden;
  } */
  
  .chat-container .sidebar-header {
    padding: 25px 20px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .chat-container .new-chat-btn {
    width: 100%;
    background: linear-gradient(to right, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 14px 20px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .new-chat-btn:hover {
    background: linear-gradient(to right, #2980b9, #1a6ca3);
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
  }
  
  .plus-icon {
    font-size: 20px;
    margin-right: 8px;
    font-weight: 400;
  }
  
  .tabs-container {
    padding: 15px 20px 5px;
  }
  
  .tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 4px;
  }
  
  .tab-btn {
    flex: 1;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 10px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
  }
  
  .tab-btn.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
  
  .conversation-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px 15px;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
  }
  
  .conversation-list::-webkit-scrollbar {
    width: 6px;
  }
  
  .conversation-list::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }
  
  .conversation-item {
    display: flex;
    justify-content: space-between;
    padding: 15px 15px;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.05);
  }
  
  .conversation-item:hover {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .conversation-content {
    flex: 1;
    overflow: hidden;
  }
  
  .conversation-title {
    margin: 0 0 6px 0;
    font-size: 15px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .conversation-message {
    margin: 0;
    font-size: 13px;
    color: rgba(0, 0, 0, 0.7);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .conversation-meta {
    display: flex;
    color: #000;
    flex-direction: column;
    align-items: flex-end;
    min-width: 60px;
  }
  
  .conversation-time {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.7);
    margin-bottom: 5px;
  }
  
  .unread-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    background: #1abc9c;
    border-radius: 50%;
    box-shadow: 0 0 5px rgba(26, 188, 156, 0.5);
  }
  
  /* 响应式设计 */
  @media (max-width: 768px) {
    .sidebar {
      width: 260px;
    }
    
    .new-chat-btn {
      padding: 12px 15px;
      font-size: 15px;
    }
  }
  
  
  
  
  
  .conversation-item {
    padding: 12px;
    margin: 8px 0;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .conversation-item:hover {
    background: #f0f0f0;
  }
  
  
  
  
  .chat-containers {
    width: calc(100vw - 20vw);
    margin: 20px auto 0;
    /* border-radius: 12px; */
    background: #fff;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  }
  
  .messages {
    height: 80vh;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
    background: #f9f9f9;
  }
  
  .message {
    margin: 12px 0;
    padding: 15px 20px;
    border-radius: 20px;
    line-height: 1.6;
    max-width: 80%;
    animation: messageAppear 0.3s ease;
  }
  
  .message.user {
    background: #007bff;
    color: white;
    margin-left: auto;
  }
  
  .message.ai {
    background: #ffffff;
    border: 1px solid #eee;
    margin-right: auto;
    position: relative;
  }
  
  .streaming-cursor {
    animation: cursorBlink 1s infinite;
    margin-left: 4px;
    color: #666;
  }
  
  @keyframes messageAppear {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes cursorBlink {
    0% {
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
  
  .chat-container form {
    display: flex;
    gap: 10px;
    padding: 20px;
    background: #fff;
    border-top: 1px solid #eee;
  }
  
  .chat-container input {
    flex: 1;
    padding: 12px 20px;
    border: 1px solid #ddd;
    border-radius: 30px;
    font-size: 16px;
    transition: all 0.3s;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -ms-border-radius: 30px;
    -o-border-radius: 30px;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -ms-transition: all 0.3s;
    -o-transition: all 0.3s;
  }
  
  .chat-container input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  }
  
  .chat-container button {
    padding: 12px 24px;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -ms-border-radius: 30px;
    -o-border-radius: 30px;
  }
  
  .send-button {
    background: #007bff;
    color: white;
  }
  
  .send-button:disabled {
    background: #a0c4ff;
    cursor: not-allowed;
  }
  
  .abort-button {
    background: #dc3545;
    color: white;
    margin-left: 10px;
  }
  
  .abort-button:hover {
    background: #bb2d3b;
  }