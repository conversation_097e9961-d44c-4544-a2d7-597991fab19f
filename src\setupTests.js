// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
// import '@testing-library/jest-dom';


const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  app.use(
    '/v1',
    createProxyMiddleware({
      target: process.env.REACT_APP_DIFY_API_URL,
      changeOrigin: true,
      pathRewrite: {
        '^/v1': '/v1' // 根据实际API路径调整
      },
    })
  );
};