.image-viewer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    cursor: default;
}

.image-viewer-image {
    max-height: 90vh;
    max-width: 90vw;
    object-fit: contain;
    user-select: none;
    transition: transform 0.1s ease-out;
}

.image-viewer-controls {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 1001;
}

.image-viewer-controls button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    transition: background-color 0.2s;
}

.image-viewer-controls button:hover {
    background: rgba(255, 255, 255, 0.3);
}

.image-viewer-controls button svg {
    width: 24px;
    height: 24px;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .image-viewer-controls {
        top: auto;
        bottom: 20px;
        right: 50%;
        transform: translateX(50%);
    }

    .image-viewer-controls button {
        width: 36px;
        height: 36px;
    }

    .image-viewer-controls button svg {
        width: 20px;
        height: 20px;
    }
} 