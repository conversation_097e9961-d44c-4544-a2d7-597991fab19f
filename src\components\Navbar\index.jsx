import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import './Navbar.css';

const Navbar = () => {
  const navigate = useNavigate();
  const isLoggedIn = localStorage.getItem('token');

  const handleLogin = () => {
    navigate('/login');
  };

  return (
    <nav className="navbar">
      <div className="navbar-brand">
        <Link to="/">博笛智家</Link>
      </div>
      <div className="navbar-menu">
        <Link to="/" className="nav-item">首页</Link>
        <Link to="/about-company" className="nav-item">公司简介</Link>
        <Link to="/ai-assistant" className="nav-item">AI助手</Link>
        <Link to="/about-us" className="nav-item">关于我们</Link>
      </div>
      <button 
        className="login-button"
        onClick={handleLogin}
      >
        {isLoggedIn ? '个人中心' : '登录'}
      </button>
    </nav>
  );
};

export default Navbar; 