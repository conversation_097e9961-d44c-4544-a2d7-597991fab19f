{"name": "dify-chat-app", "proxy": "http://ai-model.kaikungroup.com:8081", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "http-proxy-middleware": "^3.0.5", "marked": "^15.0.12", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.1", "remark-gfm": "^4.0.1", "web-vitals": "^2.1.4"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@vitejs/plugin-react": "^4.5.0", "@vitejs/plugin-react-swc": "^3.10.0", "sass": "^1.89.0", "terser": "^5.40.0", "vite": "^6.3.5", "vitest": "^3.1.4"}}