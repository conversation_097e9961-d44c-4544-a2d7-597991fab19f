.chat-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background: #fff;
  transition: all 0.3s ease;
}
.chat-content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8fafc;
    min-height: 0;
  }
/* 隐藏所有元素的滚动条 */
::-webkit-scrollbar {
  display: none; 
}

/* 深色主题样式 */
.chat-container.dark-theme {
  background: #000;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  padding: 0 24px;
  background: rgb(0 0 0 / var(--tw-bg-opacity, 1));
  border-bottom: 1px solid rgb(59 130 246 / 0.2);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: #fff;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.header-title {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.settings-dropdown {
  position: relative;
}

.settings-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: #3b82f6;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.settings-button:hover {
  background: rgba(59, 130, 246, 0.1);
}

.theme-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: #3b82f6;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.theme-button:hover {
  background: rgba(59, 130, 246, 0.1);
}

.settings-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 4px;
  background: rgb(23 37 84 / 0.9);
  border: 1px solid rgb(59 130 246 / 0.2);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  min-width: 120px;
  z-index: 1000;
}

.settings-menu button {
  display: block;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: none;
  color: #fff;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.settings-menu button:hover {
  background: rgba(59, 130, 246, 0.2);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background: #3b82f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.user-avatar svg {
  width: 20px;
  height: 20px;
}

.user-name {
  color: #fff;
  font-size: 14px;
}

.logout-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: #3b82f6;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  padding: 0;
}

.logout-button:hover {
  background: rgba(59, 130, 246, 0.1);
}

.chat-main {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: calc(100vh - 50px);
}

.chat-containers {
  flex: 1;
  background: #fff;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  max-height: 100%;
  overflow: auto;
}

/* ========== 移动端响应式 ========== */
@media (max-width: 750px) {
  .chat-header,
  .mobile-chat-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 50px;
    z-index: 100;
    background: #18181c;
    display: flex;
    align-items: center;
    padding: 0 12px;
    border-bottom: 1px solid #eee;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .header-title {
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    flex: 1;
    text-align: center;
    letter-spacing: 1px;
    margin-right: 30px;
  }

  .menu-button {
    background: none;
    border: none;
    color: #fff;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    border-radius: 6px;
  }

  .new-chat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    padding: 0;
    margin-right: 25px;
    background: transparent;
    border: none;
    color: #3b82f6;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .new-chat-icon:hover {
    background: rgba(59, 130, 246, 0.1);
  }

  .new-chat-icon:active {
    transform: scale(0.95);
  }

  /* 深色模式适配 */
  .dark-theme .new-chat-icon {
    color: #60a5fa;
  }

  .dark-theme .new-chat-icon:hover {
    background: rgba(96, 165, 250, 0.1);
  }

  .chat-main {
    margin-top: 50px;
    margin-bottom: 64px;
    height: calc(100vh - 114px);
    display: flex;
    flex-direction: column;
    width: 100vw;
    overflow: hidden;
  }
  .chat-content-area {
    flex: 1;
    width: 100vw;
    display: flex;
    flex-direction: column;
    background: #f8fafc;
    min-height: 0;
  }
  /* 移动端隐藏PC侧边栏 */
  .sidebar {
    /* display: none !important; */
    width: 100%;
  }
  .sidebar-header{
    display: none !important;
  }
  /* 移动端抽屉侧边栏 */
  .mobile-sidebar-drawer {
    position: fixed;
    top: 0;
    left: -80vw;
    width: 80vw;
    height: 100vh;
    background: #fff;
    z-index: 200;
    box-shadow: 2px 0 16px rgba(0,0,0,0.08);
    transition: left 0.3s cubic-bezier(.4,0,.2,1);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
  }
  .mobile-sidebar-drawer.open {
    left: 0;
  }
  .sidebar-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.3);
    z-index: 199;
  }
  .mobile-sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
    background: #f8fafc;
  }
  .mobile-sidebar-header .user-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  .mobile-sidebar-header .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #1a7aff;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
  }
  .mobile-sidebar-header .user-name {
    color: #222;
    font-size: 15px;
    font-weight: 500;
  }
  .mobile-sidebar-header .logout-button {
    background: none;
    border: none;
    color: #1a7aff;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    font-size: 20px;
  }
}

@media (min-width: 751px) {
  /* .new-chat-btn {
    display: none !important;
  } */
}