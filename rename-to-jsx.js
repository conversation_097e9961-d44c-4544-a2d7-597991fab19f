const fs = require('fs');
const path = require('path');

const filesToRename = [
  'src/App.js',
  'src/pages/Home/index.js',
  'src/pages/Login/index.js',
  'src/pages/Chat/index.js',
  'src/pages/Chat/Sidebar.js',
  'src/pages/Chat/ChatWindow.js',
  'src/components/Navbar/index.js',
  'src/components/ChatStream.js',
  'src/routes/PrivateRoute.js',
];

filesToRename.forEach(file => {
  if (fs.existsSync(file)) {
    const newPath = file.replace('.js', '.jsx');
    fs.renameSync(file, newPath);
    console.log(`Renamed ${file} to ${newPath}`);
  } else {
    console.log(`File ${file} does not exist`);
  }
}); 