import React, { useState } from 'react';
import SvgRenderer from '../../components/SvgRenderer';
import './SvgDemo.css';

const SvgDemo = () => {
    const [svgContent, setSvgContent] = useState(`<svg width="512" height="512" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <g id="layer-bottom">
    <!-- 客厅 -->
    <path d="M 80,60 L 200,60 L 200,140 L 80,140 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="140" y="105" font-size="12" text-anchor="middle" fill="black">客厅</text>
    
    <!-- 餐厅 -->
    <path d="M 200,60 L 280,60 L 280,140 L 200,140 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="240" y="105" font-size="12" text-anchor="middle" fill="black">餐厅</text>
    
    <!-- 厨房 -->
    <path d="M 280,60 L 360,60 L 360,140 L 280,140 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="320" y="105" font-size="12" text-anchor="middle" fill="black">厨房</text>
    
    <!-- 主卧 -->
    <path d="M 80,140 L 200,140 L 200,220 L 80,220 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="140" y="185" font-size="12" text-anchor="middle" fill="black">主卧</text>
    
    <!-- 主卧卫生间 -->
    <path d="M 200,140 L 250,140 L 250,180 L 200,180 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="225" y="165" font-size="10" text-anchor="middle" fill="black">主卫</text>
    
    <!-- 次卧 -->
    <path d="M 200,180 L 280,180 L 280,220 L 200,220 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="240" y="205" font-size="12" text-anchor="middle" fill="black">次卧</text>
    
    <!-- 儿童房 -->
    <path d="M 280,140 L 360,140 L 360,220 L 280,220 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="320" y="185" font-size="12" text-anchor="middle" fill="black">儿童房</text>
    
    <!-- 客卫 -->
    <path d="M 80,220 L 130,220 L 130,260 L 80,260 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="105" y="245" font-size="10" text-anchor="middle" fill="black">客卫</text>
    
    <!-- 走廊 -->
    <path d="M 130,220 L 280,220 L 280,260 L 130,260 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="205" y="245" font-size="12" text-anchor="middle" fill="black">走廊</text>
    
    <!-- 储物间 -->
    <path d="M 280,220 L 360,220 L 360,260 L 280,260 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="320" y="245" font-size="10" text-anchor="middle" fill="black">储物间</text>
    
    <!-- 阳台 -->
    <path d="M 80,40 L 200,40 L 200,60 L 80,60 Z" stroke="black" stroke-width="3" fill="none"/>
    <text x="140" y="55" font-size="10" text-anchor="middle" fill="black">阳台</text>
  </g>
</svg>`);

    const [isDarkMode, setIsDarkMode] = useState(false);

    const toggleTheme = () => {
        setIsDarkMode(!isDarkMode);
    };

    return (
        <div className={`svg-demo ${isDarkMode ? 'dark-theme' : 'light-theme'}`}>
            <div className="demo-header">
                <h1>SVG渲染演示</h1>
                <button onClick={toggleTheme} className="theme-toggle">
                    {isDarkMode ? '🌞' : '🌙'}
                </button>
            </div>
            
            <div className="demo-content">
                <div className="demo-section">
                    <h2>房屋平面图</h2>
                    <SvgRenderer 
                        svgContent={svgContent}
                        className={isDarkMode ? 'dark-theme' : ''}
                    />
                </div>
                
                <div className="demo-section">
                    <h2>SVG代码</h2>
                    <textarea
                        value={svgContent}
                        onChange={(e) => setSvgContent(e.target.value)}
                        className="svg-code"
                        rows="20"
                        placeholder="输入SVG代码..."
                    />
                </div>
            </div>
        </div>
    );
};

export default SvgDemo; 