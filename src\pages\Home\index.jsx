import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../../components/Header/Header';
import './Home.css';
// import banner from '../../assets/images/banner.jpg';

const Home = () => {
    const navigate = useNavigate();
    const [conversations] = useState([
        {id: 1, title: 'AI智能分析', lastMessage: '利用先进的人工智能技术，分析您的需求并提供最佳建筑方案'},
        {id: 2, title: '实时响应', lastMessage: '系统实时响应您的咨询，提供即时的专业建议和解决方案'},
        {id: 3, title: '数据可视化', lastMessage: '直观展示建筑数据和分析结果，帮助您做出更明智的决策'},
        {id: 4, title: '专业设计', lastMessage: '提供专业的建筑设计方案，兼顾美观与实用性'},
        {id: 5, title: '沟通记录', lastMessage: '自动保存所有咨询记录，方便您随时查看和继续之前的咨询'},
        {id: 6, title: '安全可靠', lastMessage: '采用先进的安全技术，保护您的数据和隐私安全'},
    ]);

    const handleConsult = () => {
        const isLoggedIn = localStorage.getItem('token');
        if (!isLoggedIn) {
            navigate('/login');
        } else {
            navigate('/chat');
        }
    };

    return (
        <div className="home">
            <Header />
            <div className="home-container">
                <div className="banner-section">
                    {/* <img src={banner} alt="AI 智能助手" className="banner-image" /> */}
                    <div className='banner-title'>
                        <span>AI智能建筑设计模型</span>
                    </div>
                    <div className='banner-description'>利用人工智能技术，为您的农村自建房提供专业设计方案</div>
                    <button className="consult-button" onClick={handleConsult}>立即咨询</button>
                </div>

                <div className="features-grid">
                    {conversations.map(item => (
                        <div key={item.id} className="feature-card">
                            <h3>{item.title}</h3>
                            <p>{item.lastMessage}</p>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default Home; // 显式默认导出