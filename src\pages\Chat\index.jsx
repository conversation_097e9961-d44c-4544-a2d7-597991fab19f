import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { logout } from '../../store/authSlice';
import Sidebar from './Sidebar';
import ChatWindow from './ChatWindow';
import './Chat.css';

const MOBILE_BREAKPOINT = 750;

const Chat = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const userId = useSelector(state => state.auth.userId);
  const [isDarkMode, setIsDarkMode] = useState(() => {
    return localStorage.getItem('chatTheme') === 'dark';
  });
  const [showSettingsDropdown, setShowSettingsDropdown] = useState(false);
  const [userName] = useState(() => {
    // 从 localStorage 获取用户名，如果没有则生成随机字母
    return localStorage.getItem('userName') || 'U';
  });
  const [selectedConversationId, setSelectedConversationId] = useState(null);
  const [isNewChat, setIsNewChat] = useState(false);
  const [fetchTrigger, setFetchTrigger] = useState(0);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= MOBILE_BREAKPOINT);
  const [conversations, setConversations] = useState([]);

  // 监听窗口大小，切换移动端/PC端
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= MOBILE_BREAKPOINT);
      if (window.innerWidth > MOBILE_BREAKPOINT) {
        setIsSidebarOpen(false); // PC端关闭抽屉
      }
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 公共配置变量
  const API_KEY = 'app-2XnCWvdqFxRWhUP69QSsjQbZ';
  const BASE_URL = 'http://ai-model.kaikungroup.com:8081';
  const AUTHORIZATION_HEADER = 'Bearer ' + API_KEY;

  const refreshConversations = useCallback(async () => {
    try {
      const response = await fetch(`${BASE_URL}/v1/conversations?user=${userId}&limit=100`, {
        method: 'GET',
        headers: {
          'Authorization': AUTHORIZATION_HEADER,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('获取会话列表失败');
      }

      const data = await response.json();
      const conversationList = data.data || [];
      setConversations(conversationList);
      setFetchTrigger(prev => prev + 1);
    } catch (error) {
      console.error('获取会话列表错误:', error);
    }
  }, [userId]);

  const toggleTheme = () => {
    setIsDarkMode(prev => {
      const newTheme = !prev;
      localStorage.setItem('chatTheme', newTheme ? 'dark' : 'light');
      return newTheme;
    });
  };

  const handleClose = () => {
    navigate('/');
  };

  const handleLogout = () => {
    localStorage.removeItem('access_token');
    dispatch(logout());
    navigate('/');
  };

  const handleConversationSelect = (id, isNew = false) => {
    setSelectedConversationId(id);
    setIsNewChat(isNew);
    if (isMobile) setIsSidebarOpen(false); // 选中后自动关闭侧边栏（移动端）
  };

  const handleNewChat = async () => {
    // 检查是否存在临时对话（ID以'new-'开头的对话）
    const hasTemporaryChat = conversations.some(conv => conv.id.toString().startsWith('new-'));

    // 如果存在临时对话，先刷新列表
    if (hasTemporaryChat) {
      await refreshConversations();
    }

    const newConversation = {
      id: 'new-' + Date.now(), // 临时ID
      name: '新对话',
      introduction: '您好，有什么需要帮助您呢',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      isNew: true // 标记为新对话
    };

    // 将新对话添加到列表开头
    setConversations(prev => [newConversation, ...prev]);
    setIsNewChat(true);
    setSelectedConversationId(newConversation.id);
    if (isMobile) setIsSidebarOpen(false);
  };

  // 头部
  const renderHeader = () => (
    <div className="chat-header mobile-chat-header">
      <div className="header-left">
        {isMobile && (
          <button className="menu-button" onClick={() => setIsSidebarOpen(true)} title="菜单">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><line x1="3" y1="12" x2="21" y2="12"/><line x1="3" y1="6" x2="21" y2="6"/><line x1="3" y1="18" x2="21" y2="18"/></svg>
          </button>
        )}
        {!isMobile && (
          <button className="close-button" onClick={handleClose} title="关闭">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        )}
      </div>
      <h1 className="header-title">{isMobile ? (selectedConversationId ? '博笛AI建筑设计' : '博笛AI建筑设计') : '博笛AI建筑设计'}</h1>
      {isMobile && (
        <button 
          className="new-chat-icon" 
          onClick={handleNewChat}
          title="新建对话"
        >
          <svg 
            viewBox="0 0 1024 1024" 
            width="30" 
            height="30"
          >
            <path d="M285.074286 849.078857c44.690286 0 134.985143-43.410286 203.154285-91.940571 226.925714 4.169143 396.617143-126.976 396.617143-299.227429 0-166.180571-166.180571-299.885714-373.174857-299.885714-206.994286 0-372.845714 133.705143-372.845714 299.885714 0 105.728 65.572571 199.899429 166.180571 249.088-13.824 26.038857-38.253714 61.732571-52.077714 80.347429-20.553143 27.318857-6.436571 61.732571 32.146286 61.732571z m38.875428-66.56c-4.169143 1.609143-5.778286-0.621714-2.889143-4.169143 16.420571-19.931429 40.521143-51.748571 52.406858-74.24 9.654857-17.664 6.436571-33.097143-14.153143-42.752-98.011429-45.970286-154.916571-119.588571-154.916572-203.446857 0-129.243429 136.265143-234.349714 307.273143-234.349714 171.008 0 307.309714 105.106286 307.309714 234.313143 0 129.243429-136.301714 234.642286-307.309714 234.642285-5.778286 0-14.774857-0.292571-25.709714-0.292571-13.824-0.365714-24.137143 3.84-36.937143 13.824-38.912 28.891429-96.438857 64.914286-125.074286 76.470857z m69.12-291.510857h88.393143v88.393143c0 17.664 12.544 30.208 29.878857 30.208 18.029714 0 30.866286-12.544 30.866286-30.208v-88.393143h88.393143c17.700571 0 30.208-12.544 30.208-30.208 0-18.029714-12.507429-30.537143-30.208-30.537143h-88.393143V341.869714c0-18.029714-12.836571-30.537143-30.866286-30.537143-17.334857 0-29.878857 12.507429-29.878857 30.537143v88.393143h-88.393143c-17.993143 0-30.537143 12.507429-30.537143 30.537143 0 17.664 12.544 30.208 30.537143 30.208z" fill="currentColor" />
          </svg>
        </button>
      )}
      {!isMobile && (
        <div className="header-right">
          <div className="settings-dropdown" onMouseEnter={() => setShowSettingsDropdown(true)} onMouseLeave={() => setShowSettingsDropdown(false)}>
            <button className="settings-button" title="设置">
              <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M12 15a3 3 0 100-6 3 3 0 000 6z"/>
                <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z"/>
              </svg>
            </button>
            {showSettingsDropdown && (
              <div className="settings-menu">
                <button>保存对话</button>
                <button>导出对话</button>
              </div>
            )}
          </div>
          <button className="theme-button" onClick={toggleTheme} title={isDarkMode ? "切换到浅色模式" : "切换到深色模式"}>
            {isDarkMode ? (
              <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="5"/>
                <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
              </svg>
            ) : (
              <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"/>
              </svg>
            )}
          </button>
          <div className="user-info">
            <div className="user-avatar">
              <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="white" strokeWidth="2">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                <circle cx="12" cy="7" r="4" />
              </svg>
            </div>
          </div>
          <button className="logout-button" onClick={handleLogout} title="退出">
            <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 21H5a2 2 0 01-2-2V5a2 2 0 012-2h4"/>
              <path d="M16 17l5-5-5-5"/>
              <path d="M21 12H9"/>
            </svg>
          </button>
        </div>
      )}
    </div>
  );

  // 移动端抽屉侧边栏遮罩
  const renderSidebarMask = () => (
    <div className="sidebar-mask" onClick={() => setIsSidebarOpen(false)}></div>
  );

  // 移动端侧边栏内容（含用户信息和退出登录）
  const renderMobileSidebar = () => (
    <div className={`mobile-sidebar-drawer${isSidebarOpen ? ' open' : ''}`}>
      <div className="mobile-sidebar-header">
        <div className="user-info">
          <div className="user-avatar">
            <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="white" strokeWidth="2">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
              <circle cx="12" cy="7" r="4" />
            </svg>
          </div>
          <span className="user-name">{userName}</span>
        </div>
        <button className="logout-button" onClick={handleLogout} title="退出">
          <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M9 21H5a2 2 0 01-2-2V5a2 2 0 012-2h4"/>
            <path d="M16 17l5-5-5-5"/>
            <path d="M21 12H9"/>
          </svg>
        </button>
      </div>
      <Sidebar 
        isDarkMode={isDarkMode} 
        onConversationSelect={handleConversationSelect}
        fetchTrigger={fetchTrigger}
      />
    </div>
  );

  return (
    <div className={`chat-container ${isDarkMode ? 'dark-theme' : ''}`}>
      {renderHeader()}
      <div className="chat-main">
        {/* PC端常驻侧边栏 */}
        {!isMobile && (
          <Sidebar 
            isDarkMode={isDarkMode} 
            onConversationSelect={handleConversationSelect}
            fetchTrigger={fetchTrigger}
          />
        )}
        {/* 聊天内容区 */}
        <div className="chat-content-area">
          <ChatWindow 
            isDarkMode={isDarkMode} 
            conversationId={selectedConversationId}
            isNewChat={isNewChat}
            onRefreshConversations={refreshConversations}
            onConversationSelect={handleConversationSelect}
            isMobile={isMobile}
          />
        </div>
      </div>
      {/* 移动端抽屉侧边栏和遮罩 */}
      {isMobile && isSidebarOpen && renderSidebarMask()}
      {isMobile && renderMobileSidebar()}
    </div>
  );
};

export default Chat;
