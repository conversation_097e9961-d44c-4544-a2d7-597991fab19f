.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    padding: 20px 24px 10px;
    background: rgb(0 0 0 / var(--tw-bg-opacity, 1));
    border-bottom: 1px solid rgb(59 130 246 / 0.2);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}   

.logo-image {
    height: 50px;
    width: auto;
}

.logo-text {
    font-size: 20px;
    font-weight: 600;
    color: #fff;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 40px;
}

.nav-section {
    margin-right: 70px;
    display: flex;
    align-items: center;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 24px;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: #fff;
    text-decoration: none;
    font-size: 15px;
    font-weight: 500;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.nav-link:hover {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.auth-section {
    position: relative;
}

.user-dropdown {
    position: relative;
}

.user-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.user-button:hover {
    background: rgba(59, 130, 246, 0.1);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgb(23 37 84 / 0.3);
    border: 1px solid rgb(59 130 246 / 0.2);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
}

.user-name {
    color: #fff;
    font-size: 15px;
    font-weight: 500;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: rgb(23 37 84 / 0.3);
    border: 1px solid rgb(59 130 246 / 0.2);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    min-width: 160px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    text-align: center;
}

.user-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: 10px 16px;
    color: #fff;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    background: none;
    width: 100%;
    text-align: center;
}

.dropdown-item:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}


.auth-section .login-button {
    padding: 10px 30px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    box-sizing: border-box;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -ms-transition: all 0.3s;
    -o-transition: all 0.3s;
}
.auth-section .login-button:hover {
    color: #3b82f6;
    border: 1px solid #3b82f6;
    background: none;
    box-sizing: border-box;
}

/* 移动端样式 */
@media (max-width: 750px) {
    .header {
        height: 50px;
        padding: 0 16px;
    }

    .logo-image {
        height: 25px;
    }

    .mobile-menu-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        padding: 0;
        background: transparent;
        border: none;
        color: #fff;
        cursor: pointer;
        z-index: 1001;
    }

    .header-right {
        position: fixed;
        top: 50px;
        left: 0;
        right: 0;
        background: rgb(0 0 0 / var(--tw-bg-opacity, 1));
        padding: 16px;
        border-top: 1px solid rgb(59 130 246 / 0.2);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .header-right.show {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .auth-section {
        width: 100%;
    }

    .user-dropdown {
        width: 100%;
    }

    .user-button {
        width: 100%;
        justify-content: center;
        padding: 12px;
    }

    .dropdown-menu {
        position: relative;
        width: 100%;
        margin-top: 8px;
        opacity: 1;
        visibility: visible;
        transform: none;
    }

    .auth-section .login-button {
        display: block;
        width: 100%;
        text-align: center;
        padding: 12px;
        margin: 0;
        background: #3498db;
        border-radius: 6px;
    }

    .auth-section .login-button:hover {
        background: #2980b9;
        color: white;
        border: none;
    }
}

/* 平板端样式 */
@media (min-width: 751px) and (max-width: 1024px) {
    .header {
        padding: 0 20px;
    }

    .mobile-menu-button {
        display: none;
    }
}

@media (min-width: 751px) {
    .mobile-menu-button {
        display: none;
    }
} 