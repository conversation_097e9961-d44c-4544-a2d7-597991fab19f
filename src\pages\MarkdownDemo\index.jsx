import React, { useState } from 'react';
import MarkdownRenderer from '../../components/MarkdownRenderer';
import './MarkdownDemo.css';

const MarkdownDemo = () => {
    const [markdownContent, setMarkdownContent] = useState(`# Markdown 渲染演示

这是一个 **Markdown** 渲染演示页面，展示了各种 Markdown 语法的渲染效果。

## 标题示例

### 三级标题
#### 四级标题
##### 五级标题
###### 六级标题

## 文本格式

这是 *斜体文本*，这是 **粗体文本**，这是 ***粗斜体文本***。

~~这是删除线文本~~。

## 代码示例

行内代码：\`console.log('Hello World')\`

代码块：
\`\`\`javascript
function greet(name) {
    return \`Hello, \${name}!\`;
}

console.log(greet('World'));
\`\`\`

## 列表

### 无序列表
- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

### 有序列表
1. 第一项
2. 第二项
3. 第三项

## 引用

> 这是一个引用示例
> 
> 可以包含多行内容

## 链接和图片

[访问 GitHub](https://github.com)

![示例图片](https://via.placeholder.com/300x200)

## 表格

| 姓名 | 年龄 | 职业 |
|------|------|------|
| 张三 | 25 | 工程师 |
| 李四 | 30 | 设计师 |
| 王五 | 28 | 产品经理 |

## 任务列表

- [x] 完成 Markdown 渲染
- [x] 添加样式支持
- [ ] 测试所有功能
- [ ] 优化性能

## 分割线

---

## 数学公式（如果支持）

行内公式：$E = mc^2$

块级公式：
$$
\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}
$$

## 脚注

这里是一个脚注示例[^1]。

[^1]: 这是脚注的内容。

## 高亮

==这是高亮文本==

## 上标和下标

H<sub>2</sub>O 和 X<sup>2</sup> + Y<sup>2</sup> = Z<sup>2</sup>

---

*感谢使用 Markdown 渲染器！*`);

    const [isDarkMode, setIsDarkMode] = useState(false);

    const toggleTheme = () => {
        setIsDarkMode(!isDarkMode);
    };

    return (
        <div className={`markdown-demo ${isDarkMode ? 'dark-theme' : 'light-theme'}`}>
            <div className="demo-header">
                <h1>Markdown 渲染演示</h1>
                <button onClick={toggleTheme} className="theme-toggle">
                    {isDarkMode ? '🌞' : '🌙'}
                </button>
            </div>
            
            <div className="demo-content">
                <div className="demo-section">
                    <h2>渲染效果</h2>
                    <div className="render-area">
                        <MarkdownRenderer 
                            content={markdownContent}
                            className={isDarkMode ? 'dark-theme' : ''}
                        />
                    </div>
                </div>
                
                <div className="demo-section">
                    <h2>Markdown 代码</h2>
                    <textarea
                        value={markdownContent}
                        onChange={(e) => setMarkdownContent(e.target.value)}
                        className="markdown-code"
                        rows="30"
                        placeholder="输入 Markdown 代码..."
                    />
                </div>
            </div>
        </div>
    );
};

export default MarkdownDemo; 