import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import SvgRenderer from '../../components/SvgRenderer';
import MarkdownRenderer from '../../components/MarkdownRenderer';
import ImageViewer from '../../components/ImageViewer/ImageViewer';
import { API_CONFIG } from '../../config/api';
import './ChatWindow.css';

const ChatWindow = ({ isDarkMode, conversationId, isNewChat, onRefreshConversations, onConversationSelect }) => {
    const [input, setInput] = useState('');
    const [messages, setMessages] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingHistory, setIsLoadingHistory] = useState(false);
    const [controller, setController] = useState(null);
    const [isThinking, setIsThinking] = useState(false);
    const [previewImage, setPreviewImage] = useState(null);
    const userId = useSelector(state => state.auth.userId);
    const messagesEndRef = useRef(null);
    const typingTimeoutRef = useRef(null);
    const inputRef = useRef(null);
    const [isMobile, setIsMobile] = useState(window.innerWidth <= 750);

    // 使用公共配置
    const API_URL = import.meta.env.VITE_DIFY_API_URL || API_CONFIG.CHAT_API_URL;
    const HISTORY_API_URL = API_CONFIG.HISTORY_API_URL;
    const AUTHORIZATION_HEADER = API_CONFIG.AUTHORIZATION_HEADER;
    const STREAM_TIMEOUT = API_CONFIG.STREAM_TIMEOUT;
    const NO_DATA_TIMEOUT = API_CONFIG.NO_DATA_TIMEOUT;
    const MAX_RETRIES = API_CONFIG.MAX_RETRIES;
    const RETRY_DELAY = API_CONFIG.RETRY_DELAY;

    
    // 自动滚动到底部
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    // 清理打字效果的timeout
    useEffect(() => {
        return () => {
            if (typingTimeoutRef.current) {
                clearTimeout(typingTimeoutRef.current);
            }
        };
    }, []);

    // 处理文本中的think标签
    const processThinkTags = (text) => {
        if (!text) return [{ text: '', isThinking: false }];
        
        // 使用正则表达式匹配think标签
        const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
        const parts = [];
        let lastIndex = 0;
        let match;

        while ((match = thinkRegex.exec(text)) !== null) {
            // 添加think标签之前的文本
            if (match.index > lastIndex) {
                parts.push({
                    text: text.slice(lastIndex, match.index),
                    isThinking: false
                });
            }
            // 添加think标签中的文本
            parts.push({
                text: match[1].trim(), // 移除首尾空格
                isThinking: true
            });
            lastIndex = match.index + match[0].length;
        }

        // 添加剩余的文本
        if (lastIndex < text.length) {
            parts.push({
                text: text.slice(lastIndex),
                isThinking: false
            });
        }

        return parts.length ? parts : [{ text: text, isThinking: false }];
    };

    // 更新消息内容
    const updateLastMessage = (newText = '') => {
        setMessages(prev => {
            const last = prev[prev.length - 1];
            if (!last || last.isUser) return prev;

            // 检查是否包含think标签
            if (/<think>/.test(newText)) {
                setIsThinking(true);
            } else {
                setIsThinking(false);
            }

            // 确保文本正确追加
            const updatedText = (last.text || '') + newText;
            console.log('更新消息:', updatedText); // 添加日志

            return [
                ...prev.slice(0, -1),
                { 
                    ...last, 
                    text: updatedText
                }
            ];
        });
    };

    // 替换最后一条消息
    const replaceLastMessage = (newText = '') => {
        setMessages(prev => {
            const last = prev[prev.length - 1];
            if (!last || last.isUser) return prev;

            console.log('替换消息:', newText); // 添加日志

            return [
                ...prev.slice(0, -1),
                { 
                    ...last, 
                    text: newText 
                }
            ];
        });
    };

    // 检查文本是否包含SVG内容
    const containsSvg = (text) => {
        return text && (
            text.includes('<svg') || 
            text.includes('viewBox') ||
            text.trim().startsWith('<svg')
        );
    };

    // 检查文本是否包含Markdown内容
    const containsMarkdown = (text) => {
        if (!text) return false;
        
        // 检查常见的Markdown语法
        const markdownPatterns = [
            /^#{1,6}\s/, // 标题
            /\*\*.*?\*\*/, // 粗体
            /\*.*?\*/, // 斜体
            /`.*?`/, // 行内代码
            /```[\s\S]*?```/, // 代码块
            /\[.*?\]\(.*?\)/, // 链接
            /!\[.*?\]\(.*?\)/, // 图片
            /^\s*[-*+]\s/, // 无序列表
            /^\s*\d+\.\s/, // 有序列表
            /^\s*>\s/, // 引用
            /\|.*\|.*\|/, // 表格
            /~~.*?~~/, // 删除线
            /^\s*---/, // 分割线
        ];
        
        return markdownPatterns.some(pattern => pattern.test(text));
    };

    // 检查文本是否包含图片URL或Base64
    const containsImage = (text) => {
        return text && (
            text.includes('![') || // Markdown 图片语法
            text.includes('data:image') || // Base64 图片
            /https?:\/\/[^\s<>"]+?\.(?:jpg|jpeg|gif|png|webp)/i.test(text) // URL图片
        );
    };

    // 分离图片和其他内容
    const separateContent = (text) => {
        if (!text) return [];
        
        const parts = [];
        let currentIndex = 0;
        
        // 处理Markdown图片语法
        const markdownImageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
        // 处理普通URL图片
        const urlImageRegex = /(http?:\/\/[^\s<>"]+?\.(?:jpg|jpeg|gif|png|webp))/gi;
        // 处理SVG链接
        const svgUrlRegex = /(http?:\/\/[^\s<>"]+?\.svg)/gi;
        // 处理Base64图片
        const base64ImageRegex = /(data:image\/[^;]+;base64,[^"'\s]+)/g;
        
        // 合并所有图片匹配
        const allMatches = [];
        let match;
        
        // 收集Markdown图片
        while ((match = markdownImageRegex.exec(text)) !== null) {
            allMatches.push({
                index: match.index,
                length: match[0].length,
                type: 'markdown',
                url: match[2],
                alt: match[1]
            });
        }
        
        // 收集URL图片
        while ((match = urlImageRegex.exec(text)) !== null) {
            // 检查这个URL是否已经作为Markdown图片的一部分被匹配
            const isPartOfMarkdown = allMatches.some(m =>
                m.type === 'markdown' &&
                (match.index >= m.index && match.index < m.index + m.length)
            );
            if (!isPartOfMarkdown) {
                allMatches.push({
                    index: match.index,
                    length: match[0].length,
                    type: 'url',
                    url: match[0]
                });
            }
        }

        // 收集SVG链接
        while ((match = svgUrlRegex.exec(text)) !== null) {
            // 检查这个URL是否已经作为Markdown图片的一部分被匹配
            const isPartOfMarkdown = allMatches.some(m =>
                m.type === 'markdown' &&
                (match.index >= m.index && match.index < m.index + m.length)
            );
            if (!isPartOfMarkdown) {
                allMatches.push({
                    index: match.index,
                    length: match[0].length,
                    type: 'svg-url',
                    url: match[0]
                });
            }
        }
        
        // 收集Base64图片
        while ((match = base64ImageRegex.exec(text)) !== null) {
            allMatches.push({
                index: match.index,
                length: match[0].length,
                type: 'base64',
                url: match[0]
            });
        }
        
        // 按位置排序
        allMatches.sort((a, b) => a.index - b.index);
        
        // 分离内容
        for (const match of allMatches) {
            // 添加图片前的文本
            if (match.index > currentIndex) {
                const beforeText = text.slice(currentIndex, match.index);
                if (beforeText.trim()) {
                    parts.push({
                        type: 'text',
                        content: beforeText
                    });
                }
            }
            
            // 添加图片
            parts.push({
                type: 'image',
                url: match.url,
                alt: match.alt || '图片'
            });
            
            currentIndex = match.index + match.length;
        }
        
        // 添加最后的文本
        if (currentIndex < text.length) {
            const remainingText = text.slice(currentIndex);
            if (remainingText.trim()) {
                parts.push({
                    type: 'text',
                    content: remainingText
                });
            }
        }
        
        return parts;
    };

    // 处理流式响应
    const processStream = async (reader, isFirstMessage = false) => {
        const decoder = new TextDecoder();
        let buffer = '';
        let isFirstChunk = true;
        let timeoutId;
        let noDataTimeoutId;  // 添加无数据超时计时器
        let newConversationId = null;
        let isMounted = true;
        let lastActivityTime = Date.now();
        let retryCount = 0;  // 添加重试计数

        // 添加无数据检查函数
        const checkNoData = () => {
            if (buffer.length === 0 && retryCount < MAX_RETRIES) {
                console.warn('未收到数据，准备重试:', {
                    重试次数: retryCount + 1,
                    最大重试次数: MAX_RETRIES
                });
                cleanup();
                retryCount++;
                return true;
            }
            return false;
        };

        const cleanup = () => {
            clearTimeout(timeoutId);
            clearTimeout(noDataTimeoutId);  // 清理无数据超时计时器
            if (reader) {
                try {
                    reader.cancel();
                } catch (e) {
                    console.warn('关闭流时出错:', e);
                }
            }
        };

        try {
            timeoutId = setTimeout(() => {
                const timeSinceLastActivity = Date.now() - lastActivityTime;
                console.error('流超时原因:', {
                    总等待时间: timeSinceLastActivity + 'ms',
                    超时阈值: STREAM_TIMEOUT + 'ms',
                    已接收数据: buffer.length > 0 ? '是' : '否',
                    组件状态: isMounted ? '已挂载' : '已卸载',
                    首次响应: isFirstChunk ? '未收到' : '已收到',
                    重试次数: retryCount
                });
                
                if (checkNoData()) {
                    // 如果是无数据状态且可以重试，不显示超时消息
                    return;
                }
                
                cleanup();
                if (isMounted) {
                    updateLastMessage('[响应超时，请重试]');
                }
            }, STREAM_TIMEOUT);

            // 设置无数据超时检查
            noDataTimeoutId = setTimeout(() => {
                if (checkNoData()) {
                    throw new Error('NO_DATA_RETRY');
                }
            }, NO_DATA_TIMEOUT);

            while (true) {
                if (!isMounted) {
                    console.warn('流处理终止: 组件已卸载');
                    cleanup();
                    break;
                }

                try {
                    const { done, value } = await reader.read();
                    lastActivityTime = Date.now();

                    // 收到数据后清除无数据超时计时器
                    clearTimeout(noDataTimeoutId);
                    
                    if (done) {
                        console.log('流读取完成');
                        if (isMounted) {
                            setIsLoading(false);
                        }
                        break;
                    }

                    clearTimeout(timeoutId);
                    timeoutId = setTimeout(() => {
                        const timeSinceLastActivity = Date.now() - lastActivityTime;
                        console.error('流处理中超时:', {
                            距离上次活动: timeSinceLastActivity + 'ms',
                            超时阈值: STREAM_TIMEOUT + 'ms',
                            当前缓冲区大小: buffer.length,
                            组件状态: isMounted ? '已挂载' : '已卸载',
                            重试次数: retryCount
                        });
                        
                        if (checkNoData()) {
                            // 如果是无数据状态且可以重试，不显示超时消息
                            return;
                        }
                        
                        cleanup();
                        if (isMounted) {
                            updateLastMessage('[响应超时，请重试]');
                        }
                    }, STREAM_TIMEOUT);

                    const text = decoder.decode(value, { stream: true });
                    buffer += text;
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';

                    for (const line of lines) {
                        if (!isMounted) {
                            console.warn('流处理终止: 组件已卸载');
                            break;
                        }
                        if (!line.trim()) continue;
                        if (line === ':heartbeat') {
                            console.log('收到心跳信号');
                            continue;
                        }
                        if (line.startsWith('event: ping')) {
                            console.log('收到ping事件');
                            continue;
                        }

                        try {
                            const jsonStr = line.startsWith('data: ') ? line.substring(6) : line;
                            if (jsonStr.trim().startsWith('{')) {
                                const data = JSON.parse(jsonStr);
                                console.log('收到数据:', {
                                    数据类型: data.answer ? 'answer' : 'other',
                                    数据长度: jsonStr.length,
                                    会话ID: data.conversation_id || '无'
                                });
                                
                                if (isFirstMessage && data.conversation_id) {
                                    newConversationId = data.conversation_id;
                                }
                                
                                if (data.answer && isMounted) {
                                    const answer = String(data.answer);
                                    if (isFirstChunk) {
                                        console.log('收到首次回答');
                                        isFirstChunk = false;
                                        replaceLastMessage(answer);
                                    } else {
                                        updateLastMessage(answer);
                                    }
                                }
                            }
                        } catch (e) {
                            if (!line.startsWith('event: ping')) {
                                console.warn('解析JSON失败:', {
                                    错误: e.message,
                                    原始数据: line.substring(0, 100) + '...'  // 只显示前100个字符
                                });
                            }
                        }
                    }
                } catch (streamError) {
                    console.error('流读取错误:', {
                        错误类型: streamError.name,
                        错误信息: streamError.message,
                        组件状态: isMounted ? '已挂载' : '已卸载'
                    });
                    throw streamError;
                }
            }

            if (isMounted && isFirstMessage && newConversationId && onRefreshConversations) {
                onConversationSelect(newConversationId);
                onRefreshConversations();
            }
        } catch (error) {
            console.error('流读取错误:', error);
            if (isMounted) {
                updateLastMessage('已取消当前对话');
            }
            throw error;
        } finally {
            if (isMounted) {
                setIsLoading(false);
            }
            cleanup();
        }

        // Return cleanup function for useEffect
        return () => {
            isMounted = false;
            cleanup();
        };
    };

    // 获取历史消息
    const fetchHistoryMessages = async () => {
        if (!conversationId || !userId) return;
        
        setIsLoadingHistory(true);
        try {
            const response = await fetch(`${HISTORY_API_URL}?user=${userId}&conversation_id=${conversationId}`, {
                method: 'GET',
                headers: {
                    'Authorization': AUTHORIZATION_HEADER,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                console.log('获取历史消息失败:', response);
                throw new Error('获取历史消息失败');
            }

            const data = await response.json();
            // 转换消息格式，将每条记录转换为问题和回答两条消息
            const formattedMessages = [];
            data.data.forEach(msg => {
                if (msg.query) {
                    formattedMessages.push({
                        text: msg.query,
                        isUser: true,
                        timestamp: msg.created_at
                    });
                }
                if (msg.answer) {
                    formattedMessages.push({
                        text: msg.answer,
                        isUser: false,
                        timestamp: msg.created_at
                    });
                }
            });
            
            setMessages(formattedMessages);
            scrollToBottom();
        } catch (error) {
            console.error('获取历史消息错误:', error);
        } finally {
            setIsLoadingHistory(false);
        }
    };

    // 监听会话ID变化
    useEffect(() => {
        let cleanupFn;

        if (conversationId) {
            if (isNewChat) {
                setMessages([{
                    text: '您好，我是博笛智家智能AI设计师，我可以为您提供以下服务，请点击选择！',
                    isUser: false,
                    timestamp: new Date().toISOString()
                }]);
                scrollToBottom();
            } else {
                setMessages([]);
                let isMounted = true;
                
                const fetchMessages = async () => {
                    setIsLoadingHistory(true);
                    try {
                        const response = await fetch(`${HISTORY_API_URL}?user=${userId}&conversation_id=${conversationId}`, {
                            method: 'GET',
                            headers: {
                                'Authorization': AUTHORIZATION_HEADER,
                                'Content-Type': 'application/json'
                            }
                        });

                        if (!response.ok) {
                            throw new Error('获取历史消息失败');
                        }

                        const data = await response.json();
                        if (isMounted) {
                            const formattedMessages = [];
                            data.data.forEach(msg => {
                                if (msg.query) {
                                    formattedMessages.push({
                                        text: msg.query,
                                        isUser: true,
                                        timestamp: msg.created_at
                                    });
                                }
                                if (msg.answer) {
                                    formattedMessages.push({
                                        text: msg.answer,
                                        isUser: false,
                                        timestamp: msg.created_at
                                    });
                                }
                            });
                            
                            setMessages(formattedMessages);
                            scrollToBottom();
                        }
                    } catch (error) {
                        console.error('获取历史消息错误:', error);
                    } finally {
                        if (isMounted) {
                            setIsLoadingHistory(false);
                        }
                    }
                };

                fetchMessages();

                return () => {
                    isMounted = false;
                };
            }
        }

        return () => {
            if (cleanupFn) {
                cleanupFn();
            }
        };
    }, [conversationId, isNewChat]);

    // 提交处理
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!input.trim() || isLoading) return;

        const userInput = input;
        setInput('');
        setIsLoading(true);

        let cleanupFn;
        const currentController = new AbortController();
        setController(currentController);

        setMessages(prev => [ ...prev, { text: userInput, isUser: true, timestamp: Date.now() }, { text: '', isUser: false, timestamp: Date.now() + 1 } ]);
        
        try {
            let retryCount = 0;
            let success = false;
            
            while (!success && retryCount < MAX_RETRIES) {
                try {
                    const requestBody = {
                        inputs: {},
                        query: userInput,
                        response_mode: 'streaming',
                        user: userId
                    };

                    if (!isNewChat && conversationId) {
                        requestBody.conversation_id = conversationId;
                    }
                    
                    const response = await fetch(API_URL, {
                        method: 'POST',
                        headers: {
                            'Authorization': AUTHORIZATION_HEADER,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestBody),
                        signal: currentController.signal
                    });

                    if (!response.ok) throw new Error(response.statusText);
                    if (!response.body) throw new Error('无可读流');

                    const reader = response.body.getReader();
                    cleanupFn = await processStream(reader, isNewChat);
                    success = true;
                } catch (error) {
                    if (error.name === 'AbortError') {
                        throw error;
                    }
                    
                    retryCount++;
                    console.warn(`请求失败 (尝试 ${retryCount}/${MAX_RETRIES}):`, error);
                    
                    if (retryCount < MAX_RETRIES) {
                        updateLastMessage(`\n\n[网络连接不稳定，尝试重新连接 (${retryCount}/${MAX_RETRIES})...]`);
                        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
                    } else {
                        throw error;
                    }
                }
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('请求失败:', error);
                updateLastMessage('\n\n[请求出错，请稍后重试]');
            }
        } finally {
            setIsLoading(false);
            setController(null);
        }

        return () => {
            if (cleanupFn) {
                cleanupFn();
            }
            if (currentController) {
                currentController.abort();
            }
        };
    };

    const handleAbort = () => {
        if (controller) {
            controller.abort();
            setController(null);
            setIsLoading(false);
        }
    };

    // --- 新增: 自动发送设计图纸消息的方法 ---
    const handleSendDrawing = async () => {
        if (isLoading) return;
        const drawingText = '请生成一份房屋设计图纸';
        setInput('');
        setIsLoading(true);
        // 添加用户消息
        setMessages(prev => [
            ...prev,
            { text: drawingText, isUser: true, timestamp: Date.now() },
            { text: '', isUser: false, timestamp: Date.now() + 1 }
        ]);
        try {
            let retryCount = 0;
            let success = false;
            while (!success && retryCount < MAX_RETRIES) {
                try {
                    const newController = new AbortController();
                    setController(newController);
                    const requestBody = {
                        inputs: {},
                        query: drawingText,
                        response_mode: 'streaming',
                        user: userId
                    };
                    if (!isNewChat && conversationId) {
                        requestBody.conversation_id = conversationId;
                    }
                    const response = await fetch(API_URL, {
                        method: 'POST',
                        headers: {
                            'Authorization': AUTHORIZATION_HEADER,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestBody),
                        signal: newController.signal
                    });
                    if (!response.ok) throw new Error(response.statusText);
                    if (!response.body) throw new Error('无可读流');
                    const reader = response.body.getReader();
                    await processStream(reader, isNewChat);
                    success = true;
                } catch (error) {
                    retryCount++;
                    if (retryCount < MAX_RETRIES && error.name !== 'AbortError') {
                        updateLastMessage(`\n\n[网络连接不稳定，尝试重新连接 (${retryCount}/${MAX_RETRIES})...]`);
                        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
                    } else {
                        throw error;
                    }
                }
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                updateLastMessage('\n\n[请求出错，请稍后重试]');
            }
        } finally {
            setIsLoading(false);
            setController(null);
        }
    };

    // --- 新增: 快捷提问按钮内容 ---
    const quickQuestions = [
        { label: '设计图纸', value: '请生成一份房屋设计图纸' },
        // { label: '生成预算', value: '请生成本项目的预算清单' },
        // { label: '生成材料清单', value: '请生成本项目的主要材料清单' },
    ];

    // --- 新增: 快捷提问按钮点击处理 ---
    const handleQuickQuestion = async (question) => {
        if (isLoading) return;
        setInput('');
        setIsLoading(true);
        setMessages(prev => [
            ...prev,
            { text: question, isUser: true, timestamp: Date.now() },
            { text: '', isUser: false, timestamp: Date.now() + 1 }
        ]);
        try {
            let retryCount = 0;
            let success = false;
            while (!success && retryCount < MAX_RETRIES) {
                try {
                    const newController = new AbortController();
                    setController(newController);
                    const requestBody = {
                        inputs: {},
                        query: question,
                        response_mode: 'streaming',
                        user: userId
                    };
                    if (!isNewChat && conversationId) {
                        requestBody.conversation_id = conversationId;
                    }
                    const response = await fetch(API_URL, {
                        method: 'POST',
                        headers: {
                            'Authorization': AUTHORIZATION_HEADER,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestBody),
                        signal: newController.signal
                    });
                    if (!response.ok) throw new Error(response.statusText);
                    if (!response.body) throw new Error('无可读流');
                    const reader = response.body.getReader();
                    await processStream(reader, isNewChat);
                    success = true;
                } catch (error) {
                    retryCount++;
                    if (retryCount < MAX_RETRIES && error.name !== 'AbortError') {
                        updateLastMessage(`\n\n[网络连接不稳定，尝试重新连接 (${retryCount}/${MAX_RETRIES})...]`);
                        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
                    } else {
                        throw error;
                    }
                }
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                updateLastMessage('\n\n[请求出错，请稍后重试]');
            }
        } finally {
            setIsLoading(false);
            setController(null);
        }
    };

    // 获取并打印返回流中的SVG链接
    const handleDownloadSVG = async () => {
        try {
            console.log('=== 开始获取返回流中的SVG链接 ===');

            // 从当前消息列表中查找SVG链接
            const svgLinks = [];

            messages.forEach((msg, msgIndex) => {
                if (msg.text) {
                    const text = String(msg.text);
                    const svgUrlRegex = /https?:\/\/[^\s<>"]+?\.svg/gi;
                    const matches = text.match(svgUrlRegex);

                    if (matches) {
                        matches.forEach((link, linkIndex) => {
                            svgLinks.push({
                                messageIndex: msgIndex,
                                linkIndex: linkIndex,
                                url: link,
                                messageText: text.substring(0, 100) + (text.length > 100 ? '...' : '')
                            });
                            // console.log(`发现SVG链接 ${svgLinks.length}:`, link);
                            // console.log(`所在消息 ${msgIndex}:`, text.substring(0, 100) + (text.length > 100 ? '...' : ''));
                        });
                    }
                }
            });

            // 打印所有找到的SVG链接
            if (svgLinks.length > 0) {
                // console.log(`=== 总共找到 ${svgLinks.length} 个SVG链接 ===`);
                // svgLinks.forEach((item, index) => {
                //     console.log(`SVG链接 ${index + 1}:`, item.url);
                // });

                // 如果有SVG链接，调用转换接口
                const firstSvgUrl = svgLinks[0].url;
                console.log('准备调用转换接口，SVG链接:', firstSvgUrl);

                // 调用转换接口
                try {
                    const convertResponse = await fetch('http://ai-model.kaikungroup.com:8020/convert', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            svg_content: firstSvgUrl
                        })
                    });

                    if (convertResponse.ok) {
                        // 检查响应类型
                        const contentType = convertResponse.headers.get('content-type');
                        console.log('响应内容类型:', contentType);

                        if (contentType && contentType.includes('application/json')) {
                            // 如果是JSON响应，可能包含下载链接
                            const convertResult = await convertResponse.json();
                            console.log('转换接口调用成功:', convertResult);

                            // 检查是否有下载链接或文件URL
                            if (convertResult.download_url || convertResult.file_url || convertResult.dwg_url) {
                                const downloadUrl = convertResult.download_url || convertResult.file_url || convertResult.dwg_url;
                                console.log('准备下载DWG文件:', downloadUrl);
                                await downloadDwgFile(downloadUrl);
                            }
                        } else {
                            // 如果直接返回文件流
                            console.log('接收到文件流，准备下载DWG文件');
                            const blob = await convertResponse.blob();
                            downloadBlobAsDwg(blob);
                        }
                    } else {
                        console.error('转换接口调用失败:', convertResponse.status, convertResponse.statusText);
                        const errorText = await convertResponse.text();
                        console.error('错误详情:', errorText);
                    }
                } catch (convertError) {
                    console.error('调用转换接口时发生错误:', convertError);
                }

                console.log('处理完成，SVG链接:', firstSvgUrl);
            } else {
                console.log('=== 未找到任何SVG链接 ===');
            }

        } catch (error) {
            console.error('获取SVG链接失败:', error);
        }
    };

    // 下载DWG文件的辅助函数
    const downloadDwgFile = async (downloadUrl) => {
        try {
            console.log('开始下载DWG文件:', downloadUrl);
            const response = await fetch(downloadUrl);

            if (response.ok) {
                const blob = await response.blob();
                downloadBlobAsDwg(blob);
                console.log('DWG文件下载完成');
            } else {
                console.error('下载DWG文件失败:', response.status, response.statusText);
            }
        } catch (error) {
            console.error('下载DWG文件时发生错误:', error);
        }
    };

    // 将Blob下载为DWG文件
    const downloadBlobAsDwg = (blob) => {
        try {
            const downloadUrl = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = `design_${Date.now()}.dwg`; // 使用时间戳避免文件名冲突
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(downloadUrl);
            document.body.removeChild(a);
            console.log('DWG文件下载触发完成');
        } catch (error) {
            console.error('创建DWG下载链接时发生错误:', error);
        }
    };

    // 检查是否是SVG URL
    const isSvgUrl = (url) => {
        return url && (
            url.endsWith('.svg') ||
            url.includes('/svg/') ||
            url.includes('processed_svg')
        );
    };

    useEffect(() => {
        if (!isLoading && inputRef.current) {
            inputRef.current.focus();
        }
    }, [isLoading]);

    useEffect(() => {
        const handleResize = () => setIsMobile(window.innerWidth <= 750);
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);
    // /api/drawings/convert/svg-to-dxf
    return (
        <div className={`chat-containers ${isDarkMode ? 'dark-theme' : 'light-colour'}`}>
            <div className="messages">
                {isLoadingHistory ? (
                    <div className="loading-history">加载历史消息中...</div>
                ) : messages.length === 0 ? (
                    <div className="empty-chat">
                        <p>开始新的对话吧</p>
                    </div>
                ) : (
                    messages.map((msg, index) => (
                        <div
                            key={`${msg.timestamp}-${index}`}
                            className={`message ${msg.isUser ? 'user' : 'ai'}`}
                        >
                            {msg.isUser ? (
                                <>
                                    {String(msg.text || '')}
                                </>
                            ) : (
                                <>
                                    {(() => {
                                        const text = String(msg.text || '');

                                        // 移除SVG链接，但保留其他所有内容
                                        const svgUrlRegex = /http?:\/\/[^\s<>"]+?\.svg/gi;
                                        console.log('内容part:', svgUrlRegex);

                                        const cleanedText = text.trim();
                                        console.log('内容part:', cleanedText);

                                        // 如果清理后没有内容，显示占位符
                                        if (!cleanedText) {
                                            return (
                                                <div className="svg-hidden-message">
                                                    <span style={{ color: '#999', fontSize: '14px' }}>
                                                        内容生成中
                                                    </span>
                                                </div>
                                            );
                                        }

                                        // 使用清理后的文本进行内容分离
                                        const parts = separateContent(cleanedText);
                                        console.log('内容part:', parts);
                                        

                                        // 过滤掉SVG相关内容（双重保险）
                                        const filteredParts = parts.filter(part =>
                                            part.type !== 'svg-url' && part.type !== 'svg'
                                        );
                                        // 分离图片和其他内容
                                        const imageParts = filteredParts.filter(part => part.type === 'image');
                                        const otherParts = filteredParts.filter(part => part.type !== 'image');
                                        return (
                                            <>
                                                {/* 渲染非图片内容 */}
                                                {otherParts.map((part, i) => {
                                                    if (part.type === 'svg') {
                                                        // SVG内容已被过滤，不应该到达这里
                                                        return null;
                                                    } else if (part.type === 'svg-url') {
                                                        // SVG链接已被过滤，不应该到达这里
                                                        return null;
                                                    } else {
                                                        return (
                                                            <MarkdownRenderer
                                                                key={`other-${i}`}
                                                                content={part.content}
                                                                className={isDarkMode ? 'dark-theme' : ''}
                                                            />
                                                        );
                                                    }
                                                })}

                                                {/* 渲染图片，使用包装器支持并排显示 */}
                                                {imageParts.length > 0 && (
                                                    <>
                                                        <div className="images-wrapper">
                                                            {imageParts.map((part, i) => {
                                                                const url = part.url;
                                                                return (
                                                                    <div key={`image-${i}`} className="message-image-container">
                                                                        <img
                                                                            src={url}
                                                                            alt={part.alt || '图片'}
                                                                            className="message-image"
                                                                            onClick={() => setPreviewImage({
                                                                                src: url,
                                                                                alt: part.alt || '图片'
                                                                            })}
                                                                            onError={(e) => {
                                                                                console.error('图片加载失败:', url);
                                                                                e.target.style.display = 'none';
                                                                            }}
                                                                        />
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>

                                                        {/* 独立的按钮区域 */}
                                                        <div className="buttons-wrapper">
                                                            {imageParts.some(part => isSvgUrl(part.url)) && (
                                                                <button
                                                                    className="download-svg-button independent-button"
                                                                    onClick={() => handleDownloadSVG()}
                                                                    title="获取并下载图纸"
                                                                >
                                                                    <svg viewBox="0 0 24 24" width="24" height="24">
                                                                        <path fill="currentColor" d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                                                                    </svg>
                                                                    下载图纸
                                                                </button>
                                                            )}
                                                        </div>
                                                    </>
                                                )}
                                            </>
                                        );
                                    })()}
                                    {isNewChat && index === 0 && !isLoading && (
                                        <div style={{ marginTop: 12, display: 'flex', gap: 8 }}>
                                            {quickQuestions.map(btn => (
                                                <button
                                                    key={btn.value}
                                                    type="button"
                                                    className="send-button messages-button"
                                                    style={{ background: '#10b981', fontSize: 14, padding: '6px 16px' }}
                                                    onClick={() => handleQuickQuestion(btn.value)}
                                                >
                                                    {btn.label}
                                                </button>
                                            ))}
                                        </div>
                                    )}
                                    {!msg.isUser && index === messages.length - 1 && isLoading && (
                                        <span className="loading-dots">
                                            <span className="dot"></span>
                                            <span className="dot"></span>
                                            <span className="dot"></span>
                                        </span>
                                    )}
                                </>
                            )}
                        </div>
                    ))
                )}
                <div ref={messagesEndRef} />
            </div>
            <form onSubmit={handleSubmit}>
                {isLoading && (
                    <div className="status-row">
                        <span className="generating-text">
                            <svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
                                <circle cx="12" cy="12" r="10" />
                                <path d="M12 6v6l4 2" />
                            </svg>
                            正在生成回答...
                        </span>
                        <button
                            type="button"
                            onClick={handleAbort}
                            className="abort-button"
                        >
                            停止生成
                        </button>
                    </div>
                )}
                <div className="input-row">
                    <input
                        ref={inputRef}
                        type="text"
                        value={input}
                        onChange={(e) => setInput(e.target.value)}
                        placeholder="输入消息..."
                        disabled={isLoading}
                        autoFocus
                    />
                    <button
                        type="submit"
                        disabled={isLoading}
                        className="send-button"
                        title="发送消息"
                    >
                        {isMobile ? (
                            <svg viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M12 19V5" />
                                <polyline points="5 12 12 5 19 12" />
                            </svg>
                        ) : (
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z" />
                            </svg>
                        )}
                    </button>
                </div>
            </form>
            {previewImage && (
                <ImageViewer
                    src={previewImage.src}
                    alt={previewImage.alt}
                    onClose={() => setPreviewImage(null)}
                />
            )}
        </div>
    );
};

export default ChatWindow;