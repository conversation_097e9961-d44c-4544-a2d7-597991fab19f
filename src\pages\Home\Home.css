body {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.home {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.home-container {
    max-width: 1200px;
    margin: 0 auto;
    padding-top: 64px; /* Header height */
}

.banner-section {
    position: relative;
    padding: 8rem 0;
}

.banner-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
}

.banner-title {
    line-height: 1.2;
    color: #fff;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
}

.banner-title span {
    font-size: 3.5rem;
    font-weight: 800;
    letter-spacing: -0.5px;
    background: linear-gradient(
        300deg,
        #00c6ff 0%,
        #0072ff 25%,
        #7b42ff 50%,
        #ff00e5 75%,
        #00c6ff 100%
    );
    background-size: 400% 400%;
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    animation: gradientMove 12s ease infinite;
    text-shadow: 0 0 30px rgba(0, 198, 255, 0.2);
    position: relative;
    display: inline-block;
}

.banner-title span::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    filter: blur(30px) opacity(0.3);
    z-index: -1;
}

/* 定义动画关键帧 */
@keyframes gradientMove {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.banner-description {
    font-size: 1.5rem;
    line-height: 2rem;
    color: #fff;
    text-align: center;
}

.consult-button {
    position: relative;
    top: 58px;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 16px 40px;
    font-size: 1.5rem;
    /*background: #007bff;*/
    background: linear-gradient(to right, #06b6d4 0%, #3b82f6 100%);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -ms-border-radius: 6px;
    -o-border-radius: 6px;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    padding: 40px 0;
}

.feature-card {
    padding: 25px;
    /* background: #fff; */
    background-color: rgb(23 37 84 / 0.3);
    border: 1px solid rgb(59 130 246 / 0.2);
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
    -webkit-transition: transform 0.3s;
    -moz-transition: transform 0.3s;
    -ms-transition: transform 0.3s;
    -o-transition: transform 0.3s;
}
.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #fff;
}
.feature-card p {
    font-size: 1rem;
    color: rgb(191 219 254);
}
.feature-card:hover {
    transform: translateY(-5px);
}
/* 移动端响应式样式 */
@media (max-width: 750px) {
    .home-container {
        padding: 0 16px;
        padding-top: 50px; /* 移动端header高度 */
    }

    .banner-section {
        padding: 4rem 0;
    }

    .banner-title span {
        font-size: 2rem;
        padding: 0 20px;
        line-height: 1.4;
    }

    .banner-description {
        font-size: 1.1rem;
        line-height: 1.6;
        padding: 0 20px;
        margin-top: 16px;
    }

    .consult-button {
        padding: 12px 32px;
        font-size: 1.2rem;
        top: 48px;
        width: calc(100% - 32px);
        max-width: 280px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 32px 0;
    }

    .feature-card {
        padding: 20px;
    }

    .feature-card h3 {
        font-size: 1.2rem;
        margin-bottom: 8px;
    }

    .feature-card p {
        font-size: 0.95rem;
        line-height: 1.5;
    }
}

/* 平板响应式样式 */
@media (min-width: 751px) and (max-width: 1024px) {
    .home-container {
        padding: 0 24px;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
    }

    .banner-title span {
        font-size: 3rem;
    }
}