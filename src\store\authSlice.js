// src/store/authSlice.js
import { createSlice } from '@reduxjs/toolkit';

const getInitialState = () => {
  return {
    accessToken: localStorage.getItem('access_token') || null,
    refreshToken: localStorage.getItem('refresh_token') || null,
    sessionId: localStorage.getItem('session_id') || null,
    userId: localStorage.getItem('user_id') || null,
    tokenExpiry: localStorage.getItem('token_expiry') || null,
    isLoading: false,
    error: null
  };
};

const authSlice = createSlice({
  name: 'auth',
  initialState: getInitialState(),
  reducers: {
    loginStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    loginSuccess: (state, action) => {
      const { access_token, refresh_token, session_id, user_id, expires_in } = action.payload;
      
      state.accessToken = access_token;
      state.refreshToken = refresh_token;
      state.sessionId = session_id;
      state.userId = user_id;
      state.tokenExpiry = Date.now() + expires_in * 1000;
      state.isLoading = false;
      state.error = null;
      
      // 保存到 localStorage
      localStorage.setItem('access_token', access_token);
      localStorage.setItem('refresh_token', refresh_token);
      if (session_id) localStorage.setItem('session_id', session_id);
      if (user_id) localStorage.setItem('user_id', user_id);
      localStorage.setItem('token_expiry', state.tokenExpiry);
    },
    loginFailure: (state, action) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    logout: (state) => {
      // 清除 Redux 状态
      state.accessToken = null;
      state.refreshToken = null;
      state.sessionId = null;
      state.userId = null;
      state.tokenExpiry = null;
      
      // 清除 localStorage
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('session_id');
      localStorage.removeItem('user_id');
      localStorage.removeItem('token_expiry');
    },
    tokenRefreshed: (state, action) => {
      const { access_token, expires_in } = action.payload;
      state.accessToken = access_token;
      state.tokenExpiry = Date.now() + expires_in * 1000;
      
      // 更新 localStorage
      localStorage.setItem('access_token', access_token);
      localStorage.setItem('token_expiry', state.tokenExpiry);
    }
  }
});

export const { loginStart, loginSuccess, loginFailure, logout, tokenRefreshed } = authSlice.actions;
export default authSlice.reducer;