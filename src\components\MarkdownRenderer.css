.markdown-renderer {
    line-height: 0.6;
    color: inherit;
}

/* 标题样式 */
.markdown-h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0.75rem 0 0.5rem 0;
    color: inherit;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 0.3rem;
}

.markdown-h2 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0.65rem 0 0.4rem 0;
    color: inherit;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 0.2rem;
}

.markdown-h3 {
    font-size: 1.05rem;
    font-weight: 600;
    margin: 0.55rem 0 0.3rem 0;
    color: inherit;
}

.markdown-h4 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0.5rem 0 0.25rem 0;
    color: inherit;
}

.markdown-h5 {
    font-size: 0.95rem;
    font-weight: 600;
    margin: 0.45rem 0 0.2rem 0;
    color: inherit;
}

.markdown-h6 {
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0.4rem 0 0.15rem 0;
    color: inherit;
}

/* 段落样式 */
.markdown-renderer p {
    margin: 0.4rem 0;
    line-height: 1.5;
}

/* 列表样式 */
.markdown-list {
    margin: 0.4rem 0;
    padding-left: 1.2rem;
}

.markdown-list li {
    margin: 0.15rem 0;
    line-height: 1.4;
}

/* 代码样式 */
.code-block {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.5rem;
    margin: 0.5rem 0;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9rem;
    line-height: 1.3;
}

.code-block code {
    background: transparent;
    padding: 0;
    border: none;
    color: #333;
}

.inline-code {
    background: #f1f3f4;
    color: #d73a49;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
}

/* 表格样式 */
.table-container {
    overflow-x: auto;
    margin: 0.5rem 0;
}

.markdown-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
}

.markdown-table th,
.markdown-table td {
    padding: 0.4rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
    border-right: 1px solid #e2e8f0;
}

.markdown-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
}

.markdown-table th:last-child,
.markdown-table td:last-child {
    border-right: none;
}

.markdown-table tr:last-child td {
    border-bottom: none;
}

/* 引用样式 */
.markdown-blockquote {
    margin: 0.5rem 0;
    padding: 0.5rem 1rem;
    border-left: 4px solid #1a7aff;
    background: #f8fafc;
    border-radius: 0 6px 6px 0;
    font-style: italic;
    color: #4a5568;
}

.markdown-blockquote p {
    margin: 0;
}

/* 链接样式 */
.markdown-link {
    color: #1a7aff;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;
}

.markdown-link:hover {
    color: #0056d6;
    border-bottom-color: #0056d6;
}

/* 图片样式 */
.markdown-image {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    margin: 0.5rem 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 分割线样式 */
.markdown-renderer hr {
    border: none;
    border-top: 1px solid #e2e8f0;
    margin: 1rem 0;
}

/* 强调样式 */
.markdown-renderer strong {
    font-weight: 600;
    color: inherit;
}

.markdown-renderer em {
    font-style: italic;
    color: inherit;
}

/* 删除线样式 */
.markdown-renderer del {
    text-decoration: line-through;
    color: #6b7280;
}

/* 深色主题样式 */
.dark-theme .code-block {
    background: #1a202c;
    border-color: #4a5568;
}

.dark-theme .code-block code {
    color: #e2e8f0;
}

.dark-theme .inline-code {
    background: #2d3748;
    color: #fbb6ce;
}

.dark-theme .markdown-table th {
    background: #2d3748;
    color: #e2e8f0;
}

.dark-theme .markdown-table th,
.dark-theme .markdown-table td {
    border-color: #4a5568;
}

.dark-theme .markdown-blockquote {
    background: #2d3748;
    color: #a0aec0;
    border-left-color: #1a7aff;
}

.dark-theme .markdown-link {
    color: #63b3ed;
}

.dark-theme .markdown-link:hover {
    color: #90cdf4;
}

.dark-theme .markdown-image {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark-theme .markdown-renderer hr {
    border-top-color: #4a5568;
}

.dark-theme .markdown-renderer del {
    color: #a0aec0;
}

/* 思考过程样式 */
.markdown-renderer .thinking-process {
    background: rgba(0, 0, 0, 0.03);
    border-left: 4px solid #9ca3af;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0 6px 6px 0;
    font-family: system-ui, -apple-system, sans-serif;
    color: #4b5563;
}

.dark-theme .markdown-renderer .thinking-process {
    background: rgba(255, 255, 255, 0.05);
    border-left-color: #6b7280;
    color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .markdown-h1 {
        font-size: 1.5rem;
    }
    
    .markdown-h2 {
        font-size: 1.3rem;
    }
    
    .markdown-h3 {
        font-size: 1.1rem;
    }
    
    .code-block {
        padding: 0.75rem;
        font-size: 0.8rem;
    }
    
    .markdown-table th,
    .markdown-table td {
        padding: 0.5rem;
        font-size: 0.9rem;
    }
} 