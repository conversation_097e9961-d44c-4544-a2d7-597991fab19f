// src/features/input/inputSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  value: '',
  isComposing: false
};

export const inputSlice = createSlice({
  name: 'input',
  initialState,
  reducers: {
    setInput: (state, action) => {
      state.value = action.payload;
    },
    clearInput: (state) => {
      state.value = '';
    },
    startComposition: (state) => {
      state.isComposing = true;
    },
    endComposition: (state) => {
      state.isComposing = false;
    }
  }
});

export const { 
  setInput, 
  clearInput, 
  startComposition, 
  endComposition 
} = inputSlice.actions;

export const selectInput = state => state.input.value;
export const selectIsComposing = state => state.input.isComposing;

export default inputSlice.reducer;